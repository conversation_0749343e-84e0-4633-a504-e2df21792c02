# Enhanced GA4 Analytics Implementation

## Overview
This implementation extends your existing GA4 analytics with advanced custom event tracking for lead generation and vehicle engagement. All functions are SSR-safe and follow Next.js best practices.

## Quick Start

### Using the Analytics Hook (Recommended)
```javascript
import { useAnalytics } from '../hooks/useAnalytics';

function VehicleDetailPage({ vehicle }) {
  const analytics = useAnalytics();
  
  // Track vehicle page view
  useEffect(() => {
    analytics.trackVehiclePageView(vehicle);
  }, [vehicle]);
  
  // Track contact interactions
  const handleContactClick = () => {
    analytics.trackPhoneClick('vehicle_detail_page', vehicle);
  };
}
```

### Direct Import (Alternative)
```javascript
import { trackContactForm, trackTestDrive, trackSearch } from '../lib/analytics';

// Track events directly
trackContactForm('vehicle_inquiry', vehicleId, vehicleData);
trackTestDrive(vehicleId, vehicleMake, vehicleModel, vehicleData);
trackSearch('Honda Civic', { make: 'Honda', year: '2020' }, 15);
```

## Core Features Implemented

### 1. Contact/Test Drive Events ✅
- `trackContactForm(formType, vehicleId, vehicleData)` - Track contact form submissions
- `trackTestDrive(vehicleId, vehicleMake, vehicleModel, vehicleData)` - Track test drive requests

### 2. Custom Vehicle Data Properties ✅
All vehicle events automatically include:
- `vehicle_id`
- `vehicle_make` 
- `vehicle_model`
- `vehicle_year`
- `dealer_id` (if available)
- `vehicle_price` (for conversion tracking)

### 3. Search Tracking ✅
- `trackSearch(searchTerm, filters, resultsCount)` - Track all search activities
- Includes filter count and results count for analysis

### 4. Click-to-Call/Message Tracking ✅
- `trackContactClick(method, location, vehicleId, vehicleData)` - Track contact method clicks
- Supports: 'phone', 'whatsapp', 'messenger', 'email'
- Tracks location context (e.g., 'vehicle_detail_page', 'header', 'footer')

## Event Categories in GA4

### Lead Generation Events
- `form_submit` - Contact form submissions
- `generate_lead` - Test drive requests and high-intent actions
- `financing_interest` - Financing inquiries
- `contact_click` - Contact method clicks

### Vehicle Engagement Events
- `view_item` - Vehicle page views (e-commerce standard)
- `vehicle_engagement` - Image views, video plays, feature expansions
- `share` - Social sharing activities

### Search & Browsing Events
- `search` - Search queries with filters and results
- `inventory_browse` - Filter applications and sorting

## Advanced Usage Examples

### Vehicle Detail Page Integration
```javascript
// Track vehicle view on page load
useEffect(() => {
  analytics.trackVehiclePageView({
    id: vehicle.id,
    make: vehicle.make,
    model: vehicle.model,
    year: vehicle.year,
    price: vehicle.price,
    dealerId: vehicle.dealerId
  });
}, [vehicle]);

// Track contact form submission
const handleContactSubmit = async (formData) => {
  analytics.trackVehicleContact('vehicle_inquiry', vehicle);
  // ... your form submission logic
};

// Track test drive interest
const handleTestDrive = () => {
  analytics.trackVehicleTestDrive(vehicle);
  // ... your test drive logic
};
```

### Search Page Integration
```javascript
const handleSearch = (searchTerm, filters, results) => {
  analytics.trackVehicleSearch(searchTerm, filters, results.length);
};

const handleFilterChange = (newFilters, results) => {
  analytics.trackInventoryFilter(newFilters, results.length);
};
```

### Contact Method Tracking
```javascript
// Phone click from vehicle detail page
const handlePhoneClick = () => {
  analytics.trackPhoneClick('vehicle_detail_page', vehicle);
  window.location.href = 'tel:************';
};

// WhatsApp click from header
const handleWhatsAppClick = () => {
  analytics.trackWhatsAppClick('header');
  // ... open WhatsApp
};
```

## GA4 Custom Dimensions Setup

To get the most value from this implementation, configure these custom dimensions in GA4:

### Recommended Custom Dimensions
1. `vehicle_id` - Vehicle identifier
2. `vehicle_make` - Vehicle manufacturer
3. `vehicle_model` - Vehicle model
4. `dealer_id` - Dealer identifier
5. `contact_method` - Contact method used
6. `form_type` - Type of form submitted
7. `search_filters` - Applied search filters
8. `click_location` - Where contact click occurred

## Conversion Tracking

The implementation includes conversion value tracking for:
- Test drive requests (vehicle price as value)
- Contact form submissions (vehicle price as value)
- Financing inquiries (vehicle price as value)

## SSR Safety

All analytics functions include SSR safety checks:
```javascript
if (typeof window === 'undefined') return;
```

The `useAnalytics` hook automatically handles this for you.

## File Structure

```
apps/frontend/
├── lib/analytics.js              # Core analytics functions
├── hooks/useAnalytics.js         # React hook wrapper
├── components/GoogleAnalytics/   # GA4 component
└── examples/                     # Implementation examples
```

## Testing

Test your analytics implementation:
1. Open browser dev tools
2. Go to Network tab
3. Filter by "google-analytics" or "gtag"
4. Trigger events and verify they're being sent

## Next Steps

1. Configure custom dimensions in GA4
2. Set up conversion goals
3. Create custom reports for lead tracking
4. Implement A/B testing with event tracking
5. Add enhanced e-commerce tracking for vehicle sales
