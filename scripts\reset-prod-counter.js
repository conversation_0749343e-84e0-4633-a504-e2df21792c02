#!/usr/bin/env node

/**
 * Reset Production Counter Script
 * Resets only the production counter back to 0
 * 
 * Usage:
 *   node scripts/reset-prod-counter.js
 */

import dotenv from 'dotenv';
import { connectToDatabase } from '../apps/api/lib/dbConnect.js';
import Counter from '../apps/frontend/src/lib/models/Counter.js';

// Load environment variables from frontend directory
dotenv.config({ path: '../apps/frontend/.env.local' });

async function resetProdCounter() {
  try {
    console.log('🔄 Resetting production counter...');

    // Connect to database
    await connectToDatabase();

    const currentYear = new Date().getFullYear();
    const counterId = 'billOfSale_prod';

    // Get current status
    const beforeCounter = await Counter.findOne({ _id: counterId });
    if (beforeCounter) {
      console.log(`📊 Current prod counter: ${beforeCounter.sequenceValue} (Year: ${beforeCounter.year})`);
    } else {
      console.log('📊 No existing prod counter found');
    }

    // Delete existing counter
    const deleteResult = await Counter.deleteOne({ _id: counterId });
    console.log(`🗑️ Deleted ${deleteResult.deletedCount} existing counter(s)`);

    // Create fresh counter
    const newCounter = await Counter.create({
      _id: counterId,
      year: currentYear,
      sequenceValue: 0
    });

    console.log('✅ Production counter reset successfully!');
    console.log(`📊 New prod counter: ${newCounter.sequenceValue} (Year: ${newCounter.year})`);
    console.log(`🎯 Next production invoice: BOS-${currentYear}-00001`);

    console.log('\n⚠️  Important Notes:');
    console.log('   - Test mode counter was NOT affected');
    console.log('   - Next production Bill of Sale will be BOS-2025-00001');
    console.log('   - Use this only when you want to start fresh for live sales');

    process.exit(0);

  } catch (error) {
    console.error('❌ Error resetting production counter:', error);
    process.exit(1);
  }
}

// Run the reset
resetProdCounter();
