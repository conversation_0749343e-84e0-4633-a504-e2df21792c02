// 'use client';
// import styles from './VehicleList.module.css';
// import { useRouter } from 'next/navigation';
// import Link from 'next/link';

// export default function VehicleList({ vehicles }) {
//   const router = useRouter();

//   const handleDelete = async (id) => {
//     const confirm = window.confirm('Are you sure you want to delete this vehicle?');
//     if (!confirm) return;

//     try {
//       const res = await fetch(`/api/vehicles/${id}`, {
//         method: 'DELETE',
//       });

//       if (res.ok) {
//         router.refresh(); // Re-fetch the page data
//       } else {
//         alert('Failed to delete vehicle');
//       }
//     } catch (err) {
//       console.error('Error deleting vehicle:', err);
//     }
//   };

//   return (
//     <div className={styles.vehicleGrid}>
//       {vehicles.map((vehicle) => (
//         <div key={vehicle._id} className={styles.vehicleCard}>
//           <div className={styles.imageWrapper}>
//             <img
//               src={vehicle.imageUrl}
//               alt={`${vehicle.make} ${vehicle.model}`}
//               className={styles.vehicleImage}
//             />
//             <span className={styles.priceTag}>${vehicle.price.toLocaleString()}</span>
//           </div>

//           <h3>{vehicle.year} {vehicle.make} {vehicle.model}</h3>
//           <p>Color: {vehicle.color}</p>
//           <p>Mileage: {vehicle.mileage.toLocaleString()} mi</p>
//           <p>Engine: {vehicle.engine}</p>
//           <p>Drivetrain: {vehicle.drivetrain}</p>
//           <p>Transmission: {vehicle.transmission}</p>

//           <div className={styles.actions}>
//             <Link href={`/admin/vehicles/${vehicle._id}/edit`}>
//               <button>Edit</button>
//             </Link>
//             <button onClick={() => handleDelete(vehicle._id)} className={styles.deleteButton}>
//               Delete
//             </button>
//           </div>
//         </div>
//       ))}
//     </div>
//   );
// }


// /src/app/admin/VehicleList.jsx

'use client';
import styles from './VehicleList.module.css';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

export default function VehicleList({ vehicles }) {
  const router = useRouter();

  const handleDelete = async (id) => {
    const confirm = window.confirm('Are you sure you want to delete this vehicle?');
    if (!confirm) return;

    try {
      // Get user authentication data
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      if (!user.email || !user.role) {
        alert('Authentication error. Please log in again.');
        return;
      }

      const res = await fetch(`/api/vehicles/${id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userEmail: user.email,
          userRole: user.role
        }),
      });

      const data = await res.json();

      if (res.ok && data.success) {
        router.refresh();
      } else {
        alert(data.error || 'Failed to delete vehicle');
      }
    } catch (err) {
      console.error('Error deleting vehicle:', err);
      alert('Error deleting vehicle: ' + err.message);
    }
  };

  return (
    <div className={styles.vehicleGrid}>
      {vehicles.map((vehicle) => {
        // Get the first image from images array or fall back to imageUrl
        const primaryImage = (vehicle.images && vehicle.images.length > 0)
          ? vehicle.images[0]
          : vehicle.imageUrl;

        return (
          <div key={vehicle._id} className={styles.vehicleCard}>
            <Link href={`/admin/vehicles/${vehicle._id}`}>
              <div className={styles.imageWrapper}>
                <img
                  src={primaryImage}
                  alt={`${vehicle.make} ${vehicle.model}`}
                  className={styles.vehicleImage}
                />
                <div className={styles.priceTag}>${vehicle.price.toLocaleString()}</div>
              </div>
              <h3>{vehicle.year} {vehicle.make} {vehicle.model}</h3>
              <p><strong>Odometer:</strong> {vehicle.mileage.toLocaleString()} km</p>
              <p><strong>Engine:</strong> {vehicle.engine || 'N/A'}</p>
              <p><strong>Transmission:</strong> {vehicle.transmission || 'N/A'}</p>
              <p><strong>Fuel Type:</strong> {vehicle.fuelType || 'N/A'}</p>
              <p><strong>Doors:</strong> {vehicle.doors || 'N/A'}</p>
              <p><strong>Drivetrain:</strong> {vehicle.drivetrain || 'N/A'}</p>
              <p><strong>Color:</strong> {vehicle.color || 'N/A'}</p>
            </Link>

            <div className={styles.actions}>
              <Link href={`/admin/vehicles/${vehicle._id}/edit`}>
                <button>Edit</button>
              </Link>
              <button onClick={() => handleDelete(vehicle._id)} className={styles.deleteButton}>
                Delete
              </button>
            </div>
          </div>
        );
      })}


    </div>
  );
}
