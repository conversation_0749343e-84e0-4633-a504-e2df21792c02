'use client';

import { useRouter } from 'next/navigation';
import { useCompare } from '../../contexts/CompareContext';
import styles from './CompareCard.module.css';

export default function CompareCard({ vehicle }) {
  const router = useRouter();
  const { removeFromCompare } = useCompare();

  // Get the first image from images array or fall back to imageUrl
  const primaryImage = (vehicle.images && vehicle.images.length > 0)
    ? vehicle.images[0]
    : vehicle.imageUrl;

  const handleRemove = () => {
    removeFromCompare(vehicle._id);
  };

  const handleViewDetails = () => {
    router.push(`/vehicles/${vehicle._id}`);
  };

  // Helper function to check if a feature exists
  const hasFeature = (featureName) => {
    if (!vehicle.features) return false;
    if (Array.isArray(vehicle.features)) {
      return vehicle.features.includes(featureName);
    }
    // If features is a string, check if it contains the feature name
    if (typeof vehicle.features === 'string') {
      return vehicle.features.toLowerCase().includes(featureName.toLowerCase());
    }
    return false;
  };

  const displayFeatures = Array.isArray(vehicle.features) ? vehicle.features : [];

  // Helper function to capitalize first letter
  const capitalize = (str) => {
    if (!str) return 'N/A';
    return str.charAt(0).toUpperCase() + str.slice(1);
  };

  // Vehicle specifications data
  const vehicleOverview = [
    { label: 'Odometer', value: vehicle.mileage ? `${vehicle.mileage.toLocaleString()} km` : 'N/A' },
    { label: 'Body Style', value: capitalize(vehicle.bodyStyle) },
    { label: 'Transmission', value: capitalize(vehicle.transmission) },
    { label: 'Engine', value: capitalize(vehicle.engine) },
    { label: 'Engine Size', value: vehicle.engineSize || 'N/A' },
    { label: 'Driveline', value: capitalize(vehicle.drivetrain) },
    { label: 'Exterior', value: capitalize(vehicle.color) },
    { label: 'Interior', value: capitalize(vehicle.interiorColor) },
    { label: 'Doors', value: vehicle.doors || 'N/A' },
    { label: 'Passengers', value: vehicle.seatingCapacity || vehicle.passengers || 'N/A' },
    { label: 'Fuel Type', value: capitalize(vehicle.fuelType) },
    { label: 'City Fuel', value: vehicle.cityFuelEconomy ? `${vehicle.cityFuelEconomy} L/100km` : 'N/A' },
    { label: 'Hwy Fuel', value: vehicle.highwayFuelEconomy ? `${vehicle.highwayFuelEconomy} L/100km` : 'N/A' },
    { label: 'Stock Number', value: vehicle.stockNumber || vehicle.stock || 'N/A' },
    { label: 'VIN', value: vehicle.vin || 'N/A' }
  ];

  return (
    <div className={styles.compareCard}>
      {/* Remove Button */}
      <button
        className={styles.removeBtn}
        onClick={handleRemove}
        title="Remove from comparison"
      >
        ×
      </button>

      {/* Vehicle Image */}
      <div className={styles.imageWrapper}>
        <img
          src={primaryImage}
          alt={`${vehicle.year} ${vehicle.make} ${vehicle.model}`}
          className={styles.vehicleImage}
        />
      </div>

      {/* Vehicle Info */}
      <div className={styles.vehicleInfo}>
        <h3 className={styles.vehicleTitle}>
          {vehicle.year} {vehicle.make} {vehicle.model}
        </h3>
        
        <div className={styles.priceSection}>
          <span className={styles.price}>
            ${vehicle.price?.toLocaleString() || 'N/A'}
          </span>
          <span className={styles.taxText}>+ tax</span>
        </div>

        {/* Vehicle Overview */}
        <div className={styles.overviewSection}>
          <h4 className={styles.sectionTitle}>Vehicle Overview</h4>
          <div className={styles.overviewGrid}>
            {vehicleOverview.map((item, index) => (
              <div key={index} className={styles.overviewItem}>
                <span className={styles.overviewLabel}>{item.label}</span>
                <span className={styles.overviewValue}>{item.value}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Features */}
        {displayFeatures.length > 0 && (
          <div className={styles.featuresSection}>
            <h4 className={styles.sectionTitle}>Features</h4>
            <div className={styles.featuresList}>
              {displayFeatures.map((feature, index) => (
                <div key={index} className={styles.featureItem}>
                  <span className={styles.featureIcon}>✓</span>
                  <span className={styles.featureText}>{feature}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Specifications */}
        <div className={styles.specificationsSection}>
          <h4 className={styles.sectionTitle}>Specifications</h4>
          <div className={styles.specificationsList}>
            <div className={styles.specificationItem}>
              <span className={styles.specificationLabel}>ABS Brakes</span>
              <span className={styles.specificationValue}>
                {vehicle.specifications?.absBrakes || hasFeature('ABS Brakes') ? '✓' : '-'}
              </span>
            </div>
            <div className={styles.specificationItem}>
              <span className={styles.specificationLabel}>Air Conditioning</span>
              <span className={styles.specificationValue}>
                {vehicle.specifications?.airConditioning || hasFeature('Air Conditioning') ? '✓' : '-'}
              </span>
            </div>
            <div className={styles.specificationItem}>
              <span className={styles.specificationLabel}>Backup Camera</span>
              <span className={styles.specificationValue}>
                {vehicle.specifications?.backupCamera || hasFeature('Backup Camera') ? '✓' : '-'}
              </span>
            </div>
            <div className={styles.specificationItem}>
              <span className={styles.specificationLabel}>Bluetooth</span>
              <span className={styles.specificationValue}>
                {vehicle.specifications?.bluetooth || hasFeature('Bluetooth') ? '✓' : '-'}
              </span>
            </div>
            <div className={styles.specificationItem}>
              <span className={styles.specificationLabel}>Cruise Control</span>
              <span className={styles.specificationValue}>
                {vehicle.specifications?.cruiseControl || hasFeature('Cruise Control') ? '✓' : '-'}
              </span>
            </div>
            <div className={styles.specificationItem}>
              <span className={styles.specificationLabel}>Navigation System</span>
              <span className={styles.specificationValue}>
                {vehicle.specifications?.navigationSystem || hasFeature('Navigation') ? '✓' : '-'}
              </span>
            </div>
            <div className={styles.specificationItem}>
              <span className={styles.specificationLabel}>Alloy Wheels</span>
              <span className={styles.specificationValue}>
                {vehicle.specifications?.alloyWheels || hasFeature('Alloy Wheels') ? '✓' : '-'}
              </span>
            </div>
            <div className={styles.specificationItem}>
              <span className={styles.specificationLabel}>Power Windows</span>
              <span className={styles.specificationValue}>
                {vehicle.specifications?.powerWindows || hasFeature('Power Windows') ? '✓' : '-'}
              </span>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className={styles.actionButtons}>
          <button
            className={`glossy-button view-btn ${styles.viewDetailsBtn}`}
            onClick={handleViewDetails}
          >
            View Details
          </button>
          <button className={`glossy-button contact-btn ${styles.contactBtn}`}>
            Contact Us
          </button>
        </div>
      </div>
    </div>
  );
}
