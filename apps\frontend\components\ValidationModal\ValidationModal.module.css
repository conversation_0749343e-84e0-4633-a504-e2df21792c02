.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.2s ease-out;
}

.modalContent {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  animation: slideIn 0.3s ease-out;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: linear-gradient(135deg, #2d2d2d 0%, #1a1a1a 100%);
}

.modalHeader h3 {
  margin: 0;
  color: #ffffff;
  font-size: 1.25rem;
  font-weight: 600;
}

.closeButton {
  background: none;
  border: none;
  color: #ffffff;
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.closeButton:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: scale(1.1);
}

.modalBody {
  padding: 24px;
  text-align: center;
}

.errorIcon {
  font-size: 48px;
  margin-bottom: 16px;
  animation: pulse 2s infinite;
}

.errorMessage {
  color: #ffffff;
  font-size: 1.1rem;
  margin-bottom: 16px;
  font-weight: 500;
}

.errorList {
  list-style: none;
  padding: 0;
  margin: 0 0 20px 0;
  text-align: left;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  padding: 16px;
  border-left: 4px solid #ff4444;
}

.errorItem {
  color: #ffcccc;
  padding: 4px 0;
  font-size: 0.95rem;
  position: relative;
  padding-left: 20px;
}

.errorItem::before {
  content: '•';
  color: #ff4444;
  position: absolute;
  left: 0;
  font-weight: bold;
}

.continueMessage {
  color: #cccccc;
  font-size: 0.9rem;
  margin-bottom: 0;
  font-style: italic;
}

.modalFooter {
  display: flex;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
}

.cancelButton,
.continueButton {
  flex: 1;
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.cancelButton {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.cancelButton:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.continueButton {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  color: white;
  border: 1px solid #4CAF50;
}

.continueButton:hover {
  background: linear-gradient(135deg, #45a049 0%, #3d8b40 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.continueButton:active,
.cancelButton:active {
  transform: translateY(0);
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .modalContent {
    width: 95%;
    margin: 20px;
  }
  
  .modalHeader,
  .modalBody,
  .modalFooter {
    padding: 16px;
  }
  
  .modalFooter {
    flex-direction: column;
  }
  
  .cancelButton,
  .continueButton {
    width: 100%;
  }
}
