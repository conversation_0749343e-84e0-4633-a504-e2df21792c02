.complianceFormsPage {
  padding: 2rem 1rem;
  background-color: #ffffff;
  min-height: 100vh;
  color: var(--text-primary, #333);
  transition: background 0.3s ease;
}

/* Dark mode: Apply consistent gradient background */
[data-theme="dark"] .complianceFormsPage {
  background: linear-gradient(to bottom right, #0d1117, #161b22);
}

/* Responsive padding adjustments for larger screens */
@media (min-width: 769px) {
  .complianceFormsPage {
    padding: 2rem 3rem;
  }
}

@media (min-width: 1200px) {
  .complianceFormsPage {
    padding: 2rem 4rem;
  }
}

.header {
  margin-bottom: 2rem;
}

.header h1 {
  font-size: 2rem;
  font-weight: bold;
  color: var(--text-primary, #1f2937);
  margin-bottom: 0.5rem;
}

.header p {
  color: var(--text-secondary, #6b7280);
  font-size: 1.1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .complianceFormsPage {
    padding: 1rem;
  }

  .header h1 {
    font-size: 1.5rem;
  }

  .header p {
    font-size: 1rem;
  }
}
