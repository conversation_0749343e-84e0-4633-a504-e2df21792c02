'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useLanguage } from '../../../contexts/LanguageContext';
import styles from './Dashboard.module.css';

export default function AdminDashboard() {
  const [vehicles, setVehicles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    draft: 0,
    sold: 0,
    pending: 0,
    deleted: 0,
    inService: 0,
    financial: 0,
    comingSoon: 0
  });
  const router = useRouter();
  const { t } = useLanguage();

  useEffect(() => {
    fetchVehicles();
  }, []);

  const fetchVehicles = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/vehicles?admin=true&limit=10&sort=createdAt&order=desc');
      const data = await response.json();

      if (data.success) {
        const vehicleData = data.data || [];
        setVehicles(vehicleData);
        
        // Calculate stats
        const newStats = {
          total: vehicleData.length,
          active: vehicleData.filter(v => v.status === 'active').length,
          draft: vehicleData.filter(v => v.status === 'draft').length,
          sold: vehicleData.filter(v => v.status === 'sold').length,
          pending: vehicleData.filter(v => v.status === 'pending').length,
          deleted: vehicleData.filter(v => v.status === 'deleted').length,
          inService: vehicleData.filter(v => v.status === 'in_service').length,
          financial: vehicleData.filter(v => v.status === 'financial').length,
          comingSoon: vehicleData.filter(v => v.status === 'coming_soon').length
        };
        setStats(newStats);
      } else {
        setError(data.error || 'Failed to fetch vehicles');
      }
    } catch (err) {
      setError('Error fetching vehicles: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const getImageUrl = (vehicle) => {
    if (vehicle.images && vehicle.images.length > 0) {
      return vehicle.images[0];
    }
    return vehicle.imageUrl || '/placeholder-car.jpg';
  };

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.spinner}></div>
        <p>Loading dashboard...</p>
      </div>
    );
  }

  return (
    <div className={styles.dashboardContainer}>
      {/* Mobile Header */}
      <div className={styles.mobileHeader}>
        <h1>{t('admin.dashboard', 'Dashboard')}</h1>
      </div>

      {/* Desktop Content - Keep existing functionality */}
      <div className={styles.desktopContent}>
        <div className={styles.header}>
          <h1>{t('admin.dashboard', 'Dashboard')}</h1>
          <p>Vehicle inventory overview and latest additions</p>
        </div>



        {/* Desktop Chart Section */}
        <div className={styles.desktopChartSection}>
          <h2>Inventory Status Overview</h2>
          <div className={styles.desktopChartContainer}>
            <div className={styles.chartWrapper}>
              <svg viewBox="0 0 200 200" className={styles.donutChart}>
                {/* Background circle */}
                <circle cx="100" cy="100" r="80" fill="none" stroke="#e5e7eb" strokeWidth="20"/>

                {/* Chart segments based on actual data */}
                {(() => {
                  const radius = 80;
                  const circumference = 2 * Math.PI * radius;
                  let currentOffset = 0;

                  const segments = [
                    { value: stats.active, color: '#10b981', label: 'Current' },
                    { value: stats.comingSoon || 0, color: '#f59e0b', label: 'Coming Soon' },
                    { value: stats.sold, color: '#8b5cf6', label: 'Sold' },
                    { value: stats.draft || 0, color: '#6b7280', label: 'Draft' }
                  ].filter(segment => segment.value > 0);

                  return segments.map((segment, index) => {
                    const percentage = stats.total > 0 ? segment.value / stats.total : 0;
                    const strokeDasharray = `${percentage * circumference} ${circumference}`;
                    const strokeDashoffset = -currentOffset * circumference;
                    currentOffset += percentage;

                    return (
                      <circle
                        key={index}
                        cx="100"
                        cy="100"
                        r={radius}
                        fill="none"
                        stroke={segment.color}
                        strokeWidth="20"
                        strokeDasharray={strokeDasharray}
                        strokeDashoffset={strokeDashoffset}
                        transform="rotate(-90 100 100)"
                        style={{ transition: 'all 0.3s ease' }}
                      />
                    );
                  });
                })()}

                {/* Center text showing total */}
                <text x="100" y="95" textAnchor="middle" className={styles.chartCenterText}>
                  Total
                </text>
                <text x="100" y="115" textAnchor="middle" className={styles.chartCenterNumber}>
                  {stats.total}
                </text>
              </svg>
            </div>

            <div className={styles.desktopChartLegend}>
              <div className={styles.legendItem}>
                <span className={styles.legendColor} style={{backgroundColor: '#10b981'}}></span>
                <span className={styles.legendLabel}>Current Inventory</span>
                <span className={styles.legendValue}>{stats.active}</span>
              </div>
              <div className={styles.legendItem}>
                <span className={styles.legendColor} style={{backgroundColor: '#f59e0b'}}></span>
                <span className={styles.legendLabel}>Coming Soon</span>
                <span className={styles.legendValue}>{stats.comingSoon || 0}</span>
              </div>
              <div className={styles.legendItem}>
                <span className={styles.legendColor} style={{backgroundColor: '#8b5cf6'}}></span>
                <span className={styles.legendLabel}>Sold</span>
                <span className={styles.legendValue}>{stats.sold}</span>
              </div>
              <div className={styles.legendItem}>
                <span className={styles.legendColor} style={{backgroundColor: '#6b7280'}}></span>
                <span className={styles.legendLabel}>Draft</span>
                <span className={styles.legendValue}>{stats.draft || 0}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Latest Inventory */}
        <div className={styles.latestSection}>
          <h2>Latest Inventory</h2>
          <div className={styles.vehicleGrid}>
            {vehicles.slice(0, 8).map((vehicle) => (
              <div 
                key={vehicle._id} 
                className={styles.vehicleCard}
                onClick={() => router.push(`/admin/inventory/${vehicle._id}`)}
              >
                <div className={styles.vehicleImage}>
                  <img 
                    src={getImageUrl(vehicle)} 
                    alt={`${vehicle.year} ${vehicle.make} ${vehicle.model}`}
                    onError={(e) => {
                      e.target.src = '/placeholder-car.jpg';
                    }}
                  />
                </div>
                <div className={styles.vehicleInfo}>
                  <h4>{vehicle.year} {vehicle.make} {vehicle.model}</h4>
                  <p className={styles.vehiclePrice}>
                    {vehicle.price ? `$${Number(vehicle.price).toLocaleString()}` : 'Price TBD'}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Mobile Chart and Latest Inventory */}
      <div className={styles.mobileContent}>
        {/* Inventory Status Chart */}
        <div className={styles.chartSection}>
          <div className={styles.chartContainer}>
            <svg viewBox="0 0 200 200" className={styles.donutChart}>
              {/* Background circle */}
              <circle cx="100" cy="100" r="80" fill="none" stroke="#e5e7eb" strokeWidth="20"/>

              {/* Chart segments based on actual data */}
              {(() => {
                const radius = 80;
                const circumference = 2 * Math.PI * radius;
                let currentOffset = 0;

                const segments = [
                  { value: stats.active, color: '#10b981', label: 'Current' },
                  { value: stats.comingSoon || 0, color: '#f59e0b', label: 'Coming Soon' },
                  { value: stats.sold, color: '#8b5cf6', label: 'Sold' },
                  { value: stats.draft || 0, color: '#6b7280', label: 'Draft' }
                ].filter(segment => segment.value > 0);

                return segments.map((segment, index) => {
                  const percentage = stats.total > 0 ? segment.value / stats.total : 0;
                  const strokeDasharray = `${percentage * circumference} ${circumference}`;
                  const strokeDashoffset = -currentOffset * circumference;
                  currentOffset += percentage;

                  return (
                    <circle
                      key={index}
                      cx="100"
                      cy="100"
                      r={radius}
                      fill="none"
                      stroke={segment.color}
                      strokeWidth="20"
                      strokeDasharray={strokeDasharray}
                      strokeDashoffset={strokeDashoffset}
                      transform="rotate(-90 100 100)"
                      style={{ transition: 'all 0.3s ease' }}
                    />
                  );
                });
              })()}

              {/* Center text showing total */}
              <text x="100" y="95" textAnchor="middle" className={styles.chartCenterText}>
                Total
              </text>
              <text x="100" y="115" textAnchor="middle" className={styles.chartCenterNumber}>
                {stats.total}
              </text>
            </svg>
          </div>

          {/* Chart Legend */}
          <div className={styles.chartLegend}>
            <div className={styles.legendItem}>
              <span className={styles.legendColor} style={{backgroundColor: '#10b981'}}></span>
              <span>Current Inventory</span>
              <span className={styles.legendValue}>{stats.active}</span>
            </div>
            <div className={styles.legendItem}>
              <span className={styles.legendColor} style={{backgroundColor: '#f59e0b'}}></span>
              <span>Coming Soon</span>
              <span className={styles.legendValue}>{stats.comingSoon || 0}</span>
            </div>
            <div className={styles.legendItem}>
              <span className={styles.legendColor} style={{backgroundColor: '#8b5cf6'}}></span>
              <span>Sold</span>
              <span className={styles.legendValue}>{stats.sold}</span>
            </div>
            <div className={styles.legendItem}>
              <span className={styles.legendColor} style={{backgroundColor: '#6b7280'}}></span>
              <span>Draft</span>
              <span className={styles.legendValue}>{stats.draft || 0}</span>
            </div>
          </div>
        </div>

        {/* Latest Inventory Cards */}
        <div className={styles.latestInventoryMobile}>
          <h2>Latest Inventory</h2>
          <div className={styles.mobileVehicleList}>
            {vehicles.slice(0, 5).map((vehicle) => (
              <div 
                key={vehicle._id} 
                className={styles.mobileVehicleCard}
                onClick={() => router.push(`/admin/inventory/${vehicle._id}`)}
              >
                <div className={styles.mobileVehicleImage}>
                  <img 
                    src={getImageUrl(vehicle)} 
                    alt={`${vehicle.year} ${vehicle.make} ${vehicle.model}`}
                    onError={(e) => {
                      e.target.src = '/placeholder-car.jpg';
                    }}
                  />
                </div>
                <div className={styles.mobileVehicleInfo}>
                  <h4 className={styles.mobileVehicleTitle}>
                    {vehicle.year} {vehicle.make} {vehicle.model}
                  </h4>
                  <div className={styles.mobileVehicleDetails}>
                    <div className={styles.mobileInfoRow}>
                      <span className={styles.mobileInfoLabel}>Year:</span>
                      <span className={styles.mobileInfoValue}>{vehicle.year}</span>
                    </div>
                    <div className={styles.mobileInfoRow}>
                      <span className={styles.mobileInfoLabel}>VIN:</span>
                      <span className={styles.mobileInfoValue}>
                        {vehicle.vin ? vehicle.vin.slice(-8) : 'N/A'}
                      </span>
                    </div>
                  </div>
                  <div className={styles.mobileVehiclePrice}>
                    {vehicle.price ? `$${Number(vehicle.price).toLocaleString()}` : 'Price TBD'}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
