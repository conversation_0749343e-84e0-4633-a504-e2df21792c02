'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useLanguage } from '../../../contexts/LanguageContext';
import styles from './Dashboard.module.css';

export default function AdminDashboard() {
  const [vehicles, setVehicles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    draft: 0,
    sold: 0,
    pending: 0,
    deleted: 0,
    inService: 0,
    financial: 0,
    comingSoon: 0
  });
  const router = useRouter();
  const { t } = useLanguage();

  useEffect(() => {
    fetchVehicles();
  }, []);

  const fetchVehicles = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/vehicles?admin=true&limit=10&sort=createdAt&order=desc');
      const data = await response.json();

      if (data.success) {
        const vehicleData = data.data || [];
        setVehicles(vehicleData);
        
        // Calculate stats
        const newStats = {
          total: vehicleData.length,
          active: vehicleData.filter(v => v.status === 'active').length,
          draft: vehicleData.filter(v => v.status === 'draft').length,
          sold: vehicleData.filter(v => v.status === 'sold').length,
          pending: vehicleData.filter(v => v.status === 'pending').length,
          deleted: vehicleData.filter(v => v.status === 'deleted').length,
          inService: vehicleData.filter(v => v.status === 'in_service').length,
          financial: vehicleData.filter(v => v.status === 'financial').length,
          comingSoon: vehicleData.filter(v => v.status === 'coming_soon').length
        };
        setStats(newStats);
      } else {
        setError(data.error || 'Failed to fetch vehicles');
      }
    } catch (err) {
      setError('Error fetching vehicles: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const getImageUrl = (vehicle) => {
    if (vehicle.images && vehicle.images.length > 0) {
      return vehicle.images[0];
    }
    return vehicle.imageUrl || '/placeholder-car.jpg';
  };

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.spinner}></div>
        <p>Loading dashboard...</p>
      </div>
    );
  }

  return (
    <div className={styles.dashboardContainer}>
      {/* Mobile Header */}
      <div className={styles.mobileHeader}>
        <h1>{t('admin.dashboard', 'Dashboard')}</h1>
      </div>

      {/* Desktop Content - Keep existing functionality */}
      <div className={styles.desktopContent}>
        <div className={styles.header}>
          <h1>{t('admin.dashboard', 'Dashboard')}</h1>
          <p>Vehicle inventory overview and latest additions</p>
        </div>

        {/* Stats Overview */}
        <div className={styles.statsGrid}>
          <div className={styles.statCard}>
            <h3>Total Vehicles</h3>
            <p>{stats.total}</p>
          </div>
          <div className={styles.statCard}>
            <h3>Active Listings</h3>
            <p>{stats.active}</p>
          </div>
          <div className={styles.statCard}>
            <h3>Draft Listings</h3>
            <p>{stats.draft}</p>
          </div>
          <div className={styles.statCard}>
            <h3>Sold Vehicles</h3>
            <p>{stats.sold}</p>
          </div>
        </div>

        {/* Latest Inventory */}
        <div className={styles.latestSection}>
          <h2>Latest Inventory</h2>
          <div className={styles.vehicleGrid}>
            {vehicles.slice(0, 8).map((vehicle) => (
              <div 
                key={vehicle._id} 
                className={styles.vehicleCard}
                onClick={() => router.push(`/admin/inventory/${vehicle._id}`)}
              >
                <div className={styles.vehicleImage}>
                  <img 
                    src={getImageUrl(vehicle)} 
                    alt={`${vehicle.year} ${vehicle.make} ${vehicle.model}`}
                    onError={(e) => {
                      e.target.src = '/placeholder-car.jpg';
                    }}
                  />
                </div>
                <div className={styles.vehicleInfo}>
                  <h4>{vehicle.year} {vehicle.make} {vehicle.model}</h4>
                  <p className={styles.vehiclePrice}>
                    {vehicle.price ? `$${Number(vehicle.price).toLocaleString()}` : 'Price TBD'}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Mobile Chart and Latest Inventory */}
      <div className={styles.mobileContent}>
        {/* Inventory Status Chart */}
        <div className={styles.chartSection}>
          <div className={styles.chartContainer}>
            <svg viewBox="0 0 200 200" className={styles.donutChart}>
              {/* Chart will be implemented with proper calculations */}
              <circle cx="100" cy="100" r="80" fill="none" stroke="#e5e7eb" strokeWidth="20"/>
              <circle cx="100" cy="100" r="80" fill="none" stroke="#3b82f6" strokeWidth="20" 
                      strokeDasharray={`${(stats.active / stats.total) * 502} 502`} 
                      strokeDashoffset="0" transform="rotate(-90 100 100)"/>
            </svg>
          </div>
          
          {/* Chart Legend */}
          <div className={styles.chartLegend}>
            <div className={styles.legendItem}>
              <span className={styles.legendColor} style={{backgroundColor: '#3b82f6'}}></span>
              <span>All Inventory</span>
              <span className={styles.legendValue}>{stats.total}</span>
            </div>
            <div className={styles.legendItem}>
              <span className={styles.legendColor} style={{backgroundColor: '#10b981'}}></span>
              <span>Current Inventory</span>
              <span className={styles.legendValue}>{stats.active}</span>
            </div>
            <div className={styles.legendItem}>
              <span className={styles.legendColor} style={{backgroundColor: '#f59e0b'}}></span>
              <span>Coming Soon</span>
              <span className={styles.legendValue}>{stats.comingSoon}</span>
            </div>
            <div className={styles.legendItem}>
              <span className={styles.legendColor} style={{backgroundColor: '#8b5cf6'}}></span>
              <span>Sold</span>
              <span className={styles.legendValue}>{stats.sold}</span>
            </div>
          </div>
        </div>

        {/* Latest Inventory Cards */}
        <div className={styles.latestInventoryMobile}>
          <h2>Latest Inventory</h2>
          <div className={styles.mobileVehicleList}>
            {vehicles.slice(0, 5).map((vehicle) => (
              <div 
                key={vehicle._id} 
                className={styles.mobileVehicleCard}
                onClick={() => router.push(`/admin/inventory/${vehicle._id}`)}
              >
                <div className={styles.mobileVehicleImage}>
                  <img 
                    src={getImageUrl(vehicle)} 
                    alt={`${vehicle.year} ${vehicle.make} ${vehicle.model}`}
                    onError={(e) => {
                      e.target.src = '/placeholder-car.jpg';
                    }}
                  />
                </div>
                <div className={styles.mobileVehicleInfo}>
                  <div className={styles.vehicleYear}>Year: {vehicle.year}</div>
                  <div className={styles.vehicleMake}>Make: {vehicle.make}</div>
                  <div className={styles.vehicleModel}>Model: {vehicle.model}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
