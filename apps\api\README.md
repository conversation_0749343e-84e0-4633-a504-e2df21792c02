# FazeNAuto API Backend

This directory contains the backend business logic, models, and services for the FazeNAuto dealership platform, organized by domain.

## 🏗️ Architecture

The backend follows a **Domain-Driven Design** approach with clear separation of concerns:

```
apps/api/
├── domains/
│   ├── vehicles/           # Vehicle management domain
│   │   ├── models/        # Vehicle data models
│   │   ├── routes/        # Vehicle API endpoints
│   │   └── services/      # Vehicle business logic
│   ├── users/             # User management domain
│   │   ├── models/        # User data models
│   │   ├── routes/        # Authentication endpoints
│   │   └── services/      # User business logic
│   └── syndication/       # Marketplace syndication domain
│       ├── models/        # Syndication data models
│       ├── routes/        # Syndication API endpoints
│       └── services/      # Syndication business logic
├── lib/                   # Shared backend utilities
├── constants/             # Application constants
└── .env.local            # Backend environment variables
```

## 📊 Domains

### 🚗 Vehicles Domain
**Location**: `domains/vehicles/`

**Responsibilities**:
- Vehicle data management (CRUD operations)
- Vehicle image and video handling
- VIN decoding and validation
- Vehicle history integration
- Search and filtering

**Models**:
- `Vehicle.js` - Main vehicle data model

**API Endpoints**:
- `GET /api/vehicles` - List vehicles with pagination/filtering
- `POST /api/vehicles` - Create new vehicle
- `GET /api/vehicles/[id]` - Get vehicle details
- `PUT /api/vehicles/[id]` - Update vehicle
- `DELETE /api/vehicles/[id]` - Delete vehicle
- `POST /api/vehicles/upload` - Upload vehicle images/videos

### 👥 Users Domain
**Location**: `domains/users/`

**Responsibilities**:
- User authentication and authorization
- Role management (admin/dealer)
- User profile management
- Session handling

**Models**:
- `User.js` - User data model with roles

**API Endpoints**:
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `GET /api/auth/me` - Get current user
- `POST /api/auth/logout` - User logout

### 📤 Syndication Domain
**Location**: `domains/syndication/`

**Responsibilities**:
- Marketplace integration (Facebook, Craigslist, AutoTrader)
- CSV export functionality
- Syndication logging and tracking
- Platform-specific formatting

**Models**:
- `SyndicationLog.js` - Track syndication activities

**API Endpoints**:
- `POST /api/syndication` - Syndicate vehicles to platforms
- `GET /api/export/csv` - Export vehicles to CSV
- `POST /api/export/csv` - Custom CSV export
- `GET /api/facebook/token` - Facebook API token management

## 🔧 Shared Libraries

### Database Connection
**File**: `lib/dbConnect.js`
- MongoDB connection management
- Connection pooling and optimization
- Error handling and retry logic

### Caching
**File**: `lib/cache.js`
- In-memory caching for development
- Redis integration for production
- Cache key management and TTL configuration

### Database Indexes
**File**: `lib/dbIndexes.js`
- Database index definitions
- Performance optimization utilities
- Index validation and monitoring

## 🌍 Environment Configuration

### Required Environment Variables
```env
# Database
MONGO_URI=mongodb+srv://user:<EMAIL>/db

# AWS Configuration
CUSTOM_AWS_ACCESS_KEY_ID=your_access_key
CUSTOM_AWS_SECRET_ACCESS_KEY=your_secret_key
CUSTOM_AWS_REGION=us-east-1
CUSTOM_AWS_S3_BUCKET_NAME=fazenauto-vehicle-images

# Authentication
ADMIN_SECRET=your_admin_secret
AUTHORIZED_EMAILS=<EMAIL>,<EMAIL>

# Facebook Integration
FACEBOOK_APP_ID=your_app_id
FACEBOOK_APP_SECRET=your_app_secret
FACEBOOK_PAGE_ID=your_page_id
```

## 📝 Data Models

### Vehicle Model
```javascript
{
  vin: String (required, unique),
  make: String (required),
  model: String (required),
  year: Number (required),
  price: Number (required),
  mileage: Number (required),
  color: String (required),
  images: [String], // S3 URLs
  videoUrl: String, // S3 URL
  status: String, // active, sold, pending
  // ... additional fields
}
```

### User Model
```javascript
{
  email: String (required, unique),
  password: String (required, hashed),
  role: String (admin|dealer),
  createdAt: Date,
  updatedAt: Date
}
```

### Syndication Log Model
```javascript
{
  vehicleId: ObjectId (ref: Vehicle),
  platform: String (facebook_marketplace|craigslist|autotrader),
  status: String (pending|success|failed),
  externalId: String, // Platform-specific ID
  metadata: Object, // Platform-specific data
  createdAt: Date,
  updatedAt: Date
}
```

## 🔍 API Usage Examples

### Get Vehicles with Filtering
```javascript
GET /api/vehicles?make=Toyota&year=2020&page=1&limit=10

Response:
{
  "success": true,
  "data": [...vehicles],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 50,
    "pages": 5
  }
}
```

### Create Vehicle
```javascript
POST /api/vehicles
Content-Type: application/json

{
  "vin": "1HGBH41JXMN109186",
  "make": "Honda",
  "model": "Civic",
  "year": 2021,
  "price": 25000,
  "mileage": 15000,
  "color": "Blue"
}
```

### Export to CSV
```javascript
GET /api/export/csv?format=facebook&vehicleIds=id1,id2,id3

Response: CSV file download
```

## 🚀 Development

### Local Development
```bash
# Install dependencies
cd apps/api
npm install

# Start development server (if standalone)
npm run dev
```

### Testing Database Indexes
```bash
# From root directory
npm run init-indexes
npm run validate-indexes
```

## 🔒 Security Considerations

1. **Environment Variables** - Never commit `.env.local` files
2. **Password Hashing** - Uses bcryptjs for secure password storage
3. **Input Validation** - Validate all API inputs
4. **Rate Limiting** - Implement rate limiting for production
5. **CORS** - Configure CORS for frontend domains only

## 📈 Performance Optimization

1. **Database Indexes** - Optimized indexes for common queries
2. **Caching** - Cache frequently accessed data
3. **Pagination** - Implement pagination for large datasets
4. **Field Selection** - Allow clients to specify required fields
5. **Connection Pooling** - Efficient database connection management

## 🧪 Testing

### Manual Testing
```bash
# Test database connection
node -e "require('./lib/dbConnect').connectToDatabase().then(() => console.log('Connected!'))"

# Test vehicle creation
curl -X POST http://localhost:3000/api/vehicles \
  -H "Content-Type: application/json" \
  -d '{"vin":"TEST123","make":"Test","model":"Car","year":2023,"price":10000,"mileage":0,"color":"Red"}'
```

## 🔧 Troubleshooting

### Common Issues
1. **MongoDB Connection** - Check MONGO_URI and network access
2. **S3 Upload Errors** - Verify AWS credentials and bucket permissions
3. **Import Path Errors** - Check relative paths between domains
4. **Environment Variables** - Ensure all required vars are set

### Debug Logging
Enable debug logging by setting environment variables:
```bash
DEBUG=mongoose:* npm run dev
```

## 🤝 Integration with Frontend

The backend is designed to work seamlessly with the Next.js frontend:
- Models are imported via path aliases (`@/domains/*/models/*`)
- Shared utilities accessible via `@/lib/*`
- API routes handle frontend requests through Next.js API system

## 📚 Additional Resources

- [MongoDB Documentation](https://docs.mongodb.com/)
- [Mongoose ODM](https://mongoosejs.com/)
- [AWS SDK for JavaScript](https://docs.aws.amazon.com/sdk-for-javascript/)
- [Next.js API Routes](https://nextjs.org/docs/api-routes/introduction)
