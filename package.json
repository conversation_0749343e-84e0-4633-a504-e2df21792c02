{"name": "fazenauto-monorepo", "version": "0.1.0", "private": true, "type": "module", "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "cd apps/frontend && npm run dev", "build": "cd apps/frontend && npm run build", "start": "cd apps/frontend && npm run start", "lint": "cd apps/frontend && npm run lint", "init-indexes": "node scripts/init-db-indexes.js", "validate-indexes": "node scripts/validate-db-performance.js", "install-all": "npm install && cd apps/frontend && npm install && cd ../api && npm install && cd ../../packages/utils && npm install", "verify-build": "node scripts/verify-build.js"}, "dependencies": {"@aws-sdk/client-s3": "^3.817.0", "@aws-sdk/s3-request-presigner": "^3.844.0", "@tanstack/react-table": "^8.21.3", "bcryptjs": "^3.0.2", "dotenv": "^16.5.0", "form-data": "^4.0.3", "formidable": "^3.5.4", "hamburger-react": "^2.5.2", "mongoose": "^8.15.0", "multer": "^2.0.0", "next": "^15.3.2", "pdfkit": "^0.17.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "eslint": "^9", "eslint-config-next": "15.1.8"}, "engines": {"node": ">=20.17.0", "npm": ">=10.0.0"}}