import mongoose from 'mongoose';

const userSchema = new mongoose.Schema({
  email: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  password: {
    type: String,
    required: true,
    minlength: 6
  },
  role: {
    type: String,
    enum: ['admin', 'dealer', 'salesman', 'user'],
    default: 'user'
  },
  firstName: {
    type: String,
    trim: true
  },
  lastName: {
    type: String,
    trim: true
  },
  phone: {
    type: String,
    trim: true
  },
  dealerInfo: {
    businessName: {
      type: String,
      trim: true
    },
    rin: {
      type: String,
      trim: true,
      sparse: true // Allows multiple null values but unique non-null values
    },
    address: {
      street: String,
      city: String,
      province: String,
      postalCode: String,
      country: { type: String, default: 'Canada' }
    },
    businessPhone: String,
    website: String
  },
  isActive: {
    type: Boolean,
    default: true
  },
  lastLogin: {
    type: Date
  },
  emailVerified: {
    type: Boolean,
    default: false
  },
  emailVerificationToken: {
    type: String
  },
  passwordResetToken: {
    type: String
  },
  passwordResetExpires: {
    type: Date
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Indexes for better query performance
userSchema.index({ email: 1 }, { unique: true });
userSchema.index({ role: 1 });
userSchema.index({ 'dealerInfo.rin': 1 }, { sparse: true, unique: true });
userSchema.index({ isActive: 1 });
userSchema.index({ createdAt: -1 });

// Pre-save middleware to update the updatedAt field
userSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Virtual for full name
userSchema.virtual('fullName').get(function() {
  if (this.firstName && this.lastName) {
    return `${this.firstName} ${this.lastName}`;
  }
  return this.firstName || this.lastName || this.email.split('@')[0];
});

// Virtual for display name (used in UI)
userSchema.virtual('displayName').get(function() {
  if (this.dealerInfo && this.dealerInfo.businessName) {
    return this.dealerInfo.businessName;
  }
  return this.fullName;
});

// Method to check if user is admin
userSchema.methods.isAdmin = function() {
  return this.role === 'admin';
};

// Method to check if user is dealer
userSchema.methods.isDealer = function() {
  return this.role === 'dealer' || this.role === 'admin';
};

// Method to update last login
userSchema.methods.updateLastLogin = function() {
  this.lastLogin = new Date();
  return this.save();
};

// Static method to find by email
userSchema.statics.findByEmail = function(email) {
  return this.findOne({ email: email.toLowerCase() });
};

// Static method to find active users
userSchema.statics.findActive = function() {
  return this.find({ isActive: true });
};

// Static method to find by role
userSchema.statics.findByRole = function(role) {
  return this.find({ role, isActive: true });
};

// Static method to find dealers
userSchema.statics.findDealers = function() {
  return this.find({ 
    role: { $in: ['dealer', 'admin'] }, 
    isActive: true 
  });
};

// Method to generate display name from email
userSchema.methods.getDisplayNameFromEmail = function() {
  const emailName = this.email.split('@')[0];
  const name = emailName.includes('.') ? emailName.split('.')[0] : emailName;
  return name.charAt(0).toUpperCase() + name.slice(1);
};

// Method to check if user has dealer info
userSchema.methods.hasDealerInfo = function() {
  return this.dealerInfo && (this.dealerInfo.businessName || this.dealerInfo.rin);
};

// Method to validate RIN format (Ontario format: 12345)
userSchema.methods.validateRIN = function() {
  if (!this.dealerInfo || !this.dealerInfo.rin) return true;
  const rinPattern = /^\d{5}$/;
  return rinPattern.test(this.dealerInfo.rin);
};

const User = mongoose.models.User || mongoose.model('User', userSchema);

export default User;
