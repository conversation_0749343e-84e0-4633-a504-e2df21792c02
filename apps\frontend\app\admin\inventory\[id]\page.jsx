'use client';

import { useEffect, useState } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useLanguage } from '../../../../contexts/LanguageContext';
import styles from './VehicleDetail.module.css';

export default function VehicleDetailPage() {
  const [vehicle, setVehicle] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  
  const router = useRouter();
  const params = useParams();
  const { t } = useLanguage();
  const vehicleId = params.id;

  useEffect(() => {
    if (vehicleId) {
      fetchVehicle();
    }
  }, [vehicleId]);

  const fetchVehicle = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/vehicles/${vehicleId}`);
      const data = await response.json();

      if (data.success) {
        setVehicle(data.data);
      } else {
        setError(data.error || 'Failed to fetch vehicle');
      }
    } catch (err) {
      setError('Error fetching vehicle: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const getImages = () => {
    if (!vehicle) return [];
    const images = [];
    if (vehicle.images && vehicle.images.length > 0) {
      images.push(...vehicle.images);
    } else if (vehicle.imageUrl) {
      images.push(vehicle.imageUrl);
    }
    return images.length > 0 ? images : ['/placeholder-car.jpg'];
  };

  const handleImageSwipe = (direction) => {
    const images = getImages();
    if (images.length <= 1) return;
    
    if (direction === 'next') {
      setCurrentImageIndex((prev) => (prev + 1) % images.length);
    } else {
      setCurrentImageIndex((prev) => (prev - 1 + images.length) % images.length);
    }
  };

  const handleEdit = () => {
    router.push(`/admin/vehicles/edit/${vehicleId}`);
  };

  const actionGridItems = [
    { id: 'information', label: 'Information', icon: '📄' },
    { id: 'media', label: 'Media', icon: '🖼️' },
    { id: 'service', label: 'Service', icon: '🛠️' },
    { id: 'income', label: 'Income & Cost', icon: '💰' },
    { id: 'window-sticker', label: 'Window Sticker', icon: '🏷️' },
    { id: 'digital-file', label: 'Digital File', icon: '📂' }
  ];

  const featureButtons = [
    { id: 'syndication', label: 'Show Syndication', icon: '📤' },
    { id: 'visitors', label: 'Visitors', icon: '👥' },
    { id: 'image-processing', label: 'Show Image Processing', icon: '🖼️' },
    { id: 'ad-generator', label: 'Show Ad Generator', icon: '✨' }
  ];

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.spinner}></div>
        <p>Loading vehicle details...</p>
      </div>
    );
  }

  if (error || !vehicle) {
    return (
      <div className={styles.errorContainer}>
        <p>Error: {error || 'Vehicle not found'}</p>
        <button onClick={() => router.back()} className={styles.backButton}>
          Go Back
        </button>
      </div>
    );
  }

  const images = getImages();

  return (
    <div className={styles.detailContainer}>
      {/* Desktop Content - Keep existing functionality */}
      <div className={styles.desktopContent}>
        <div className={styles.header}>
          <button onClick={() => router.back()} className={styles.backButton}>
            ← Back to Inventory
          </button>
          <h1>{vehicle.year} {vehicle.make} {vehicle.model}</h1>
        </div>
        
        <div className={styles.desktopLayout}>
          <div className={styles.imageSection}>
            <img
              src={images[currentImageIndex]}
              alt={`${vehicle.year} ${vehicle.make} ${vehicle.model}`}
              className={styles.mainImage}
              onError={(e) => {
                e.target.src = '/placeholder-car.jpg';
              }}
            />

            {/* Feature Buttons */}
            <div className={styles.featureButtonsSection}>
              {featureButtons.map((button) => (
                <button
                  key={button.id}
                  className={styles.featureButton}
                  onClick={() => {
                    console.log(`Clicked ${button.id}`);
                  }}
                >
                  <span className={styles.featureIcon}>{button.icon}</span>
                  <span className={styles.featureLabel}>{button.label}</span>
                </button>
              ))}
            </div>
          </div>

          <div className={styles.detailsSection}>
            <div className={styles.vehicleInfo}>
              <h2 className={styles.desktopVehicleTitle}>{vehicle.year} {vehicle.make} {vehicle.model}</h2>
              <p className={styles.desktopVin}>VIN: {vehicle.vin || 'N/A'}</p>
              <p className={styles.desktopPrice}>
                {vehicle.price ? `$${Number(vehicle.price).toLocaleString()}` : 'Price TBD'}
              </p>

              {/* Vehicle ID and Media Information */}
              <div className={styles.vehicleMetaInfo}>
                <div className={styles.metaItem}>
                  <span className={styles.metaLabel}>MongoDB ID:</span>
                  <span className={styles.metaValue} title={vehicle._id}>
                    {vehicle._id ? vehicle._id.slice(-12) : 'N/A'}
                  </span>
                </div>
                <div className={styles.metaItem}>
                  <span className={styles.metaLabel}>Media:</span>
                  <span className={styles.metaValue}>
                    <span className={styles.mediaInfo}>
                      📷 {images.length} image{images.length !== 1 ? 's' : ''}
                      {vehicle.video && <span className={styles.videoIcon}>🎥 Video</span>}
                    </span>
                  </span>
                </div>
                {vehicle.uuid && (
                  <div className={styles.metaItem}>
                    <span className={styles.metaLabel}>UUID:</span>
                    <span className={styles.metaValue} title={vehicle.uuid}>
                      {vehicle.uuid.slice(-12)}
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* Action Grid for Desktop */}
            <div className={styles.desktopActionGrid}>
              {actionGridItems.map((item) => (
                <button
                  key={item.id}
                  className={styles.desktopActionTile}
                  onClick={() => {
                    console.log(`Clicked ${item.id}`);
                  }}
                >
                  <div className={styles.actionIcon}>{item.icon}</div>
                  <div className={styles.actionLabel}>{item.label}</div>
                </button>
              ))}
            </div>

            {/* BOS Buttons for Desktop */}
            <div className={styles.desktopBosButtons}>
              <button className={styles.bosButton}>
                Retail BOS
              </button>
              <button className={styles.bosButton}>
                Wholesale BOS
              </button>
            </div>

            <button onClick={handleEdit} className={styles.editButton}>
              Edit Vehicle
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Content */}
      <div className={styles.mobileContent}>
        {/* Hero Image Section */}
        <div className={styles.heroImageSection}>
          <div className={styles.imageContainer}>
            <img 
              src={images[currentImageIndex]} 
              alt={`${vehicle.year} ${vehicle.make} ${vehicle.model}`}
              className={styles.heroImage}
              onError={(e) => {
                e.target.src = '/placeholder-car.jpg';
              }}
            />
            
            {images.length > 1 && (
              <>
                <button 
                  className={`${styles.imageNav} ${styles.prevButton}`}
                  onClick={() => handleImageSwipe('prev')}
                >
                  ‹
                </button>
                <button 
                  className={`${styles.imageNav} ${styles.nextButton}`}
                  onClick={() => handleImageSwipe('next')}
                >
                  ›
                </button>
                
                <div className={styles.imageIndicators}>
                  {images.map((_, index) => (
                    <div 
                      key={index}
                      className={`${styles.indicator} ${index === currentImageIndex ? styles.active : ''}`}
                      onClick={() => setCurrentImageIndex(index)}
                    />
                  ))}
                </div>
              </>
            )}
          </div>
        </div>

        {/* Vehicle Details */}
        <div className={styles.vehicleDetailsSection}>
          <div className={styles.vehicleHeader}>
            <h1 className={styles.vehicleTitle}>
              {vehicle.year} {vehicle.make} {vehicle.model}
            </h1>
            <div className={styles.vehicleVin}>
              VIN: {vehicle.vin || 'VIN not available'}
            </div>
            <div className={styles.vehiclePrice}>
              {vehicle.price ? `$${Number(vehicle.price).toLocaleString()}` : 'Price TBD'}
            </div>

            {/* Vehicle Meta Information for Mobile */}
            <div className={styles.mobileVehicleMetaInfo}>
              <div className={styles.mobileMetaItem}>
                <span className={styles.mobileMetaLabel}>ID:</span>
                <span className={styles.mobileMetaValue} title={vehicle._id}>
                  {vehicle._id ? vehicle._id.slice(-8) : 'N/A'}
                </span>
              </div>
              <div className={styles.mobileMetaItem}>
                <span className={styles.mobileMetaLabel}>Media:</span>
                <span className={styles.mobileMetaValue}>
                  📷 {images.length} {vehicle.video && '🎥'}
                </span>
              </div>
            </div>
          </div>

          {/* Edit Button */}
          <button onClick={handleEdit} className={styles.mobileEditButton}>
            Edit
          </button>
        </div>

        {/* Feature Buttons for Mobile */}
        <div className={styles.mobileFeatureButtonsSection}>
          {featureButtons.map((button) => (
            <button
              key={button.id}
              className={styles.mobileFeatureButton}
              onClick={() => {
                console.log(`Clicked ${button.id}`);
              }}
            >
              <span className={styles.featureIcon}>{button.icon}</span>
              <span className={styles.featureLabel}>{button.label}</span>
            </button>
          ))}
        </div>

        {/* Action Grid */}
        <div className={styles.actionGridSection}>
          <div className={styles.actionGrid}>
            {actionGridItems.map((item) => (
              <button 
                key={item.id}
                className={styles.actionGridTile}
                onClick={() => {
                  // Handle action grid item clicks
                  console.log(`Clicked ${item.id}`);
                }}
              >
                <div className={styles.actionIcon}>{item.icon}</div>
                <div className={styles.actionLabel}>{item.label}</div>
              </button>
            ))}
          </div>
        </div>

        {/* BOS Buttons */}
        <div className={styles.bosButtonsSection}>
          <button className={styles.bosButton}>
            Retail BOS
          </button>
          <button className={styles.bosButton}>
            Wholesale BOS
          </button>
        </div>

        {/* Detail Accordion (Optional) */}
        <div className={styles.accordionSection}>
          <div className={styles.accordionItem}>
            <button className={styles.accordionHeader}>
              <span>Vehicle Details</span>
              <span className={styles.accordionIcon}>▼</span>
            </button>
            <div className={styles.accordionContent}>
              <div className={styles.detailRow}>
                <span className={styles.detailLabel}>Mileage:</span>
                <span className={styles.detailValue}>
                  {vehicle.mileage ? `${Number(vehicle.mileage).toLocaleString()} km` : 'N/A'}
                </span>
              </div>
              <div className={styles.detailRow}>
                <span className={styles.detailLabel}>Engine:</span>
                <span className={styles.detailValue}>{vehicle.engine || 'N/A'}</span>
              </div>
              <div className={styles.detailRow}>
                <span className={styles.detailLabel}>Transmission:</span>
                <span className={styles.detailValue}>{vehicle.transmission || 'N/A'}</span>
              </div>
              <div className={styles.detailRow}>
                <span className={styles.detailLabel}>Status:</span>
                <span className={`${styles.detailValue} ${styles.statusValue}`}>
                  {vehicle.status}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
