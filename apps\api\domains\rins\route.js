import { NextResponse } from 'next/server';
import { connectToDatabase } from '../../lib/dbConnect.js';
import Rin from '../../lib/models/Rin.js';

/**
 * GET /api/rins - Fetch all active RIN records
 */
export async function GET(request) {
  try {
    await connectToDatabase();
    
    const { searchParams } = new URL(request.url);
    const includeInactive = searchParams.get('includeInactive') === 'true';
    
    // Build query
    const query = includeInactive ? {} : { isActive: true };
    
    // Fetch RINs with sorting
    const rins = await Rin.find(query)
      .select('businessName address phone email rin omvicNumber licenseNumber contactPerson businessType isActive')
      .sort({ businessName: 1 })
      .lean();
    
    return NextResponse.json({
      success: true,
      data: rins,
      count: rins.length
    });
    
  } catch (error) {
    console.error('❌ Error fetching RINs:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch RIN records',
      details: error.message
    }, { status: 500 });
  }
}

/**
 * POST /api/rins - Create a new RIN record
 */
export async function POST(request) {
  try {
    await connectToDatabase();
    
    const body = await request.json();
    const {
      businessName,
      address,
      phone,
      email,
      rin,
      omvicNumber,
      licenseNumber,
      contactPerson,
      businessType,
      notes
    } = body;
    
    // Validate required fields
    if (!businessName || !address || !phone || !email || !rin) {
      return NextResponse.json({
        success: false,
        error: 'Missing required fields: businessName, address, phone, email, rin'
      }, { status: 400 });
    }
    
    // Check if RIN already exists
    const existingRin = await Rin.findOne({ rin });
    if (existingRin) {
      return NextResponse.json({
        success: false,
        error: 'RIN already exists'
      }, { status: 409 });
    }
    
    // Create new RIN record
    const newRin = new Rin({
      businessName,
      address,
      phone,
      email,
      rin,
      omvicNumber,
      licenseNumber,
      contactPerson,
      businessType,
      notes
    });
    
    await newRin.save();
    
    return NextResponse.json({
      success: true,
      data: newRin,
      message: 'RIN record created successfully'
    }, { status: 201 });
    
  } catch (error) {
    console.error('❌ Error creating RIN:', error);
    
    // Handle duplicate key errors
    if (error.code === 11000) {
      const field = Object.keys(error.keyPattern)[0];
      return NextResponse.json({
        success: false,
        error: `${field} already exists`
      }, { status: 409 });
    }
    
    return NextResponse.json({
      success: false,
      error: 'Failed to create RIN record',
      details: error.message
    }, { status: 500 });
  }
}

/**
 * PUT /api/rins - Update an existing RIN record
 */
export async function PUT(request) {
  try {
    await connectToDatabase();
    
    const body = await request.json();
    const { _id, ...updateData } = body;
    
    if (!_id) {
      return NextResponse.json({
        success: false,
        error: 'RIN ID is required for update'
      }, { status: 400 });
    }
    
    const updatedRin = await Rin.findByIdAndUpdate(
      _id,
      updateData,
      { new: true, runValidators: true }
    );
    
    if (!updatedRin) {
      return NextResponse.json({
        success: false,
        error: 'RIN record not found'
      }, { status: 404 });
    }
    
    return NextResponse.json({
      success: true,
      data: updatedRin,
      message: 'RIN record updated successfully'
    });
    
  } catch (error) {
    console.error('❌ Error updating RIN:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to update RIN record',
      details: error.message
    }, { status: 500 });
  }
}

/**
 * DELETE /api/rins - Soft delete a RIN record (set isActive to false)
 */
export async function DELETE(request) {
  try {
    await connectToDatabase();
    
    const { searchParams } = new URL(request.url);
    const rinId = searchParams.get('id');
    
    if (!rinId) {
      return NextResponse.json({
        success: false,
        error: 'RIN ID is required'
      }, { status: 400 });
    }
    
    const updatedRin = await Rin.findByIdAndUpdate(
      rinId,
      { isActive: false },
      { new: true }
    );
    
    if (!updatedRin) {
      return NextResponse.json({
        success: false,
        error: 'RIN record not found'
      }, { status: 404 });
    }
    
    return NextResponse.json({
      success: true,
      message: 'RIN record deactivated successfully'
    });
    
  } catch (error) {
    console.error('❌ Error deactivating RIN:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to deactivate RIN record',
      details: error.message
    }, { status: 500 });
  }
}
