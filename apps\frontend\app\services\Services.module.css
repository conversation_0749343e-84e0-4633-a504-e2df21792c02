/* Services Page Styles */

.servicesPage {
  min-height: 100vh;
  background: #f8f9fa;
  transition: background 0.3s ease;
}

/* Dark mode: Services page should use gradient background */
[data-theme="dark"] .servicesPage {
  background: var(--bg-primary);
}

/* Hero Section */
.heroSection {
  background: linear-gradient(135deg, #0f1419 0%, #1a202c 50%, #2d3748 100%);
  padding: 4rem 1rem;
  color: white;
  text-align: center;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.heroContent {
  max-width: 800px;
  margin: 0 auto;
}

.heroTitle {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.heroSubtitle {
  font-size: 1.3rem;
  line-height: 1.6;
  opacity: 0.95;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

/* Section Headers */
.sectionHeader {
  text-align: center;
  margin-bottom: 3rem;
}

.sectionTitle {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 1rem;
  transition: color 0.3s ease;
}

.sectionSubtitle {
  font-size: 1.2rem;
  color: #4a5568;
  max-width: 600px;
  margin: 0 auto;
  transition: color 0.3s ease;
}

/* Dark mode: Section headers */
[data-theme="dark"] .sectionTitle {
  color: #ffffff;
}

[data-theme="dark"] .sectionSubtitle {
  color: #cbd5e1;
}

/* Current Services Section */
.currentServices {
  padding: 4rem 1rem;
  background: transparent;
  transition: background 0.3s ease;
}

/* Dark mode: Current services section - use same gradient as page background */
[data-theme="dark"] .currentServices {
  background: transparent;
}

.servicesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.serviceCard {
  background: white;
  padding: 2.5rem 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid #e2e8f0;
}

/* Dark mode: Service cards */
[data-theme="dark"] .serviceCard {
  background: #161b22;
  border: 1px solid #333;
}

.serviceCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.serviceIcon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
  display: block;
  text-align: center;
}

.serviceTitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 1rem;
  text-align: center;
  transition: color 0.3s ease;
}

.serviceDescription {
  color: #4a5568;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  text-align: center;
  transition: color 0.3s ease;
}

/* Dark mode: Service card text */
[data-theme="dark"] .serviceTitle {
  color: #ffffff;
}

[data-theme="dark"] .serviceDescription {
  color: #cbd5e1;
}

.featuresList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.featureItem {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 0;
  color: #2d3748;
  font-weight: 500;
  transition: color 0.3s ease;
}

/* Dark mode: Feature items */
[data-theme="dark"] .featureItem {
  color: #cbd5e1;
}

/* Coming Soon Section */
.comingSoonSection {
  padding: 4rem 1rem;
  background: #f8f9fa;
  transition: background 0.3s ease;
}

/* Dark mode: Coming soon section */
[data-theme="dark"] .comingSoonSection {
  background: linear-gradient(to bottom right, #0d1117, #161b22);
}

.comingSoonGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  max-width: 800px;
  margin: 0 auto;
}

.comingSoonCard {
  background: white;
  padding: 2.5rem 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  text-align: center;
  position: relative;
  border: 2px dashed #e53e3e;
  transition: all 0.3s ease;
}

/* Dark mode: Coming soon cards */
[data-theme="dark"] .comingSoonCard {
  background: #161b22;
  border: 2px dashed #e53e3e; /* Keep red accent */
}

.comingSoonCard:hover {
  transform: translateY(-3px);
  border-color: #c53030;
}

.statusBadge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: #e53e3e;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* CTA Section */
.ctaSection {
  padding: 4rem 1rem;
  background: #2d3748;
  color: white;
  text-align: center;
  transition: background 0.3s ease;
}

/* Dark mode: CTA section */
[data-theme="dark"] .ctaSection {
  background: linear-gradient(to bottom right, #0d1117, #161b22);
}

.ctaContent {
  max-width: 800px;
  margin: 0 auto;
}

.ctaTitle {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: white;
}

.ctaSubtitle {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.ctaButtons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.primaryBtn {
  background: #48bb78;
  color: white;
  padding: 1rem 2rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(72, 187, 120, 0.3);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.primaryBtn:hover {
  background: #38a169;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(72, 187, 120, 0.4);
}

.secondaryBtn {
  background: transparent;
  color: white;
  padding: 1rem 2rem;
  border: 2px solid white;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.secondaryBtn:hover {
  background: white;
  color: #2d3748;
  transform: translateY(-2px);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .heroTitle {
    font-size: 2.5rem;
  }
  
  .heroSubtitle {
    font-size: 1.1rem;
  }
  
  .sectionTitle {
    font-size: 2rem;
  }
  
  .servicesGrid {
    grid-template-columns: 1fr;
  }
  
  .comingSoonGrid {
    grid-template-columns: 1fr;
  }
  
  .ctaTitle {
    font-size: 2rem;
  }
  
  .ctaButtons {
    flex-direction: column;
    align-items: center;
  }
  
  .primaryBtn,
  .secondaryBtn {
    width: 100%;
    max-width: 300px;
  }
}
