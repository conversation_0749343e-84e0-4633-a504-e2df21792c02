import { NextResponse } from 'next/server';
import { trackVisitor, getVisitorStats } from '../../../middleware/trackVisitor';
import { headers } from 'next/headers';



// GET - Fetch visitor statistics and data
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const timeRange = searchParams.get('timeRange') || '7d';
    const statsOnly = searchParams.get('statsOnly') === 'true';

    if (statsOnly) {
      // Return aggregated statistics
      const stats = await getVisitorStats(timeRange);
      return NextResponse.json({
        success: true,
        data: stats
      });
    }

    // Return detailed visitor data (for admin dashboard)
    const stats = await getVisitorStats(timeRange);

    return NextResponse.json({
      success: true,
      data: {
        summary: stats,
        message: 'Use statsOnly=true for aggregated data only'
      }
    });

  } catch (error) {
    console.error('Error fetching visitor data:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch visitor data' },
      { status: 500 }
    );
  }
}

// POST - Track a new visitor (called automatically on page visits)
export async function POST(request) {
  try {
    // Use the visitor tracking middleware
    const result = await trackVisitor(request);

    if (result.success) {
      return NextResponse.json({
        success: true,
        message: result.message,
        data: {
          ip: result.ip,
          visitCount: result.visitCount,
          isNewVisitor: result.isNewVisitor,
          location: result.location
        }
      });
    } else {
      return NextResponse.json(
        {
          success: false,
          error: result.error || 'Failed to track visitor',
          ip: result.ip
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error in visitor tracking API:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to track visitor' },
      { status: 500 }
    );
  }
}
