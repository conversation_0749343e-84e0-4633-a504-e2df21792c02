'use client';

import Link from 'next/link';
import { useLanguage } from '../contexts/LanguageContext';
import styles from './not-found.module.css';

export default function NotFound() {
  const { t } = useLanguage();

  return (
    <div className={styles.container}>
      <div className={styles.content}>
        <h1 className={styles.title}>404</h1>
        <h2 className={styles.subtitle}>
          {t('error.pageNotFound', 'Page Not Found')}
        </h2>
        <p className={styles.description}>
          {t('error.pageNotFoundDescription', 'The page you are looking for does not exist.')}
        </p>
        <Link href="/" className={styles.homeLink}>
          {t('common.goHome', 'Go Home')}
        </Link>
      </div>
    </div>
  );
}
