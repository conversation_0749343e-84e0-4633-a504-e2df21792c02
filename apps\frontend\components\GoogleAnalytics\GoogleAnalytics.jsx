'use client';

import { useEffect } from 'react';
import { usePathname, useSearchParams } from 'next/navigation';

const GA_MEASUREMENT_ID = 'G-JBNQD8SDQ1';

// Initialize Google Analytics
export function initGA() {
  if (typeof window !== 'undefined' && !window.gtag) {
    // Load gtag script
    const script = document.createElement('script');
    script.async = true;
    script.src = `https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`;
    document.head.appendChild(script);

    // Initialize gtag
    window.dataLayer = window.dataLayer || [];
    window.gtag = function gtag() {
      window.dataLayer.push(arguments);
    };
    
    window.gtag('js', new Date());
    window.gtag('config', GA_MEASUREMENT_ID, {
      page_title: document.title,
      page_location: window.location.href,
    });
  }
}

// Track page views
export function trackPageView(url, title) {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('config', GA_MEASUREMENT_ID, {
      page_title: title,
      page_location: url,
    });
  }
}

// Track custom events
export function trackEvent(eventName, parameters = {}) {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', eventName, {
      event_category: parameters.category || 'engagement',
      event_label: parameters.label,
      value: parameters.value,
      ...parameters,
    });
  }
}

// Track vehicle interactions
export function trackVehicleEvent(action, vehicleId, vehicleMake, vehicleModel) {
  trackEvent('vehicle_interaction', {
    category: 'vehicles',
    action: action,
    label: `${vehicleMake} ${vehicleModel}`,
    vehicle_id: vehicleId,
    vehicle_make: vehicleMake,
    vehicle_model: vehicleModel,
  });
}

// Enhanced contact form tracking
export function trackContactForm(formType, vehicleId = null, vehicleData = null) {
  const eventData = {
    category: 'lead_generation',
    action: 'form_submit',
    label: formType,
    form_type: formType,
  };

  if (vehicleId) {
    eventData.vehicle_id = vehicleId;
  }

  if (vehicleData) {
    eventData.vehicle_make = vehicleData.make;
    eventData.vehicle_model = vehicleData.model;
    eventData.vehicle_year = vehicleData.year;
    eventData.dealer_id = vehicleData.dealerId || null;
  }

  trackEvent('form_submission', eventData);
}

// Enhanced test drive tracking
export function trackTestDrive(vehicleId, vehicleMake, vehicleModel, vehicleData = null) {
  const eventData = {
    category: 'lead_generation',
    action: 'test_drive_request',
    label: `${vehicleMake} ${vehicleModel}`,
    vehicle_id: vehicleId,
    vehicle_make: vehicleMake,
    vehicle_model: vehicleModel,
    lead_type: 'test_drive'
  };

  if (vehicleData) {
    eventData.vehicle_year = vehicleData.year;
    eventData.dealer_id = vehicleData.dealerId || null;
    eventData.vehicle_price = vehicleData.price;
  }

  trackEvent('test_drive_interest', eventData);
}

// Enhanced search tracking
export function trackSearch(searchTerm, filters = {}, resultsCount = null) {
  trackEvent('search', {
    category: 'search',
    search_term: searchTerm,
    search_filters: Object.keys(filters).length > 0 ? JSON.stringify(filters) : null,
    results_count: resultsCount,
    filter_count: Object.keys(filters).length,
  });
}

// Click-to-contact tracking
export function trackContactClick(method, location, vehicleId = null, vehicleData = null) {
  const eventData = {
    category: 'contact_interaction',
    action: 'contact_click',
    label: `${method}_${location}`,
    contact_method: method,
    click_location: location,
  };

  if (vehicleId) {
    eventData.vehicle_id = vehicleId;
  }

  if (vehicleData) {
    eventData.vehicle_make = vehicleData.make;
    eventData.vehicle_model = vehicleData.model;
    eventData.vehicle_year = vehicleData.year;
    eventData.dealer_id = vehicleData.dealerId || null;
  }

  trackEvent('contact_click', eventData);
}

// Export additional analytics functions for easy importing
export {
  trackVehicleView,
  trackVehicleInterest,
  trackVehicleEngagement,
  trackFinancingInterest,
  trackInventoryBrowsing,
  trackSocialShare,
  gtag
} from '../../lib/analytics';

// Main GoogleAnalytics component
export default function GoogleAnalytics() {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  useEffect(() => {
    // Initialize GA on component mount
    initGA();
  }, []);

  useEffect(() => {
    // Track page views on route changes
    if (pathname) {
      const url = window.location.origin + pathname + (searchParams.toString() ? `?${searchParams.toString()}` : '');
      trackPageView(url, document.title);
    }
  }, [pathname, searchParams]);

  return null; // This component doesn't render anything
}
