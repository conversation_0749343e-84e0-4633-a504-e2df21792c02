.container {
  min-height: 100vh;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  position: relative;
}

.container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(240, 248, 255, 0.3) 0%, rgba(230, 230, 250, 0.2) 100%);
  z-index: -1;
}

.card {
  background: var(--bg-primary);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  padding: 2rem;
  border-radius: 16px;
  box-shadow: var(--card-shadow-hover);
  width: 100%;
  max-width: 400px;
  border: 1px solid var(--border-primary);
}

.header {
  text-align: center;
  margin-bottom: 2rem;
}

.header h1 {
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  font-size: 1.75rem;
  font-weight: bold;
}

.header p {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.errorAlert {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 0.75rem;
  border-radius: 6px;
  margin-bottom: 1rem;
  font-size: 0.875rem;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
}

.formGroup label {
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-primary, #374151);
  font-size: 0.875rem;
}

.input {
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-primary, #d1d5db);
  border-radius: 8px;
  font-size: 0.875rem;
  transition: all 0.2s;
  background: var(--bg-primary, white);
  color: var(--text-primary, #111827);
}

/* Light mode specific styling for login inputs */
[data-theme="light"] .input {
  border: var(--input-border-light);
  box-sizing: border-box;
  outline: none;
}

[data-theme="light"] .input:focus {
  border: var(--input-focus-border-light);
  box-sizing: border-box;
  outline: none;
  box-shadow: none;
}

.input:focus {
  outline: none;
  border-color: var(--accent-primary, #3b82f6);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.input::placeholder {
  color: var(--text-tertiary, #9ca3af);
}

.submitBtn {
  padding: 0.75rem 1rem;
  background: var(--accent-primary, #3b82f6);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-top: 0.5rem;
}

.submitBtn:hover:not(:disabled) {
  background: var(--accent-hover, #2563eb);
}

.submitBtn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.footer {
  text-align: center;
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
}

.footer p {
  color: #6b7280;
  font-size: 0.75rem;
}

/* Responsive */
@media (max-width: 768px) {
  .container {
    padding: 0.5rem;
  }
  
  .card {
    padding: 1.5rem;
  }
  
  .header h1 {
    font-size: 1.5rem;
  }
}
