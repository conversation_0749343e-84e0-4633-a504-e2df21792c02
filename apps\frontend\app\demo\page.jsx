'use client';

import { useEffect, Suspense } from 'react';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import ThemeToggle from '../../components/ThemeToggle/ThemeToggle';
import LanguageSelector from '../../components/LanguageSelector/LanguageSelector';

// Force dynamic rendering to avoid build-time issues
export const dynamic = 'force-dynamic';
import Settings from '../../components/Settings/Settings';
import { trackPageView, trackEvent } from '../../lib/analytics';
import styles from './demo.module.css';

export default function DemoPage() {
  const { currentTheme } = useTheme();
  const { t, currentLanguage } = useLanguage();

  useEffect(() => {
    // Track page view for analytics
    trackPageView(window.location.href, 'Demo Page');
    
    // Track demo page visit
    trackEvent('demo_page_visit', {
      category: 'engagement',
      theme: currentTheme,
      language: currentLanguage
    });
  }, [currentTheme, currentLanguage]);

  const handleTestAnalytics = () => {
    trackEvent('demo_button_click', {
      category: 'demo',
      label: 'Test Analytics Button',
      theme: currentTheme,
      language: currentLanguage
    });
  };

  return (
    <Suspense fallback={<div>Loading...</div>}>
      <div className={styles.container}>
      <div className={styles.header}>
        <h1 className={styles.title}>
          {t('settings.title', 'Settings')} & {t('settings.language', 'Language')} Demo
        </h1>
        <p className={styles.subtitle}>
          {t('settings.subtitle', 'Manage your account preferences and settings')}
        </p>
      </div>

      <div className={styles.content}>
        {/* Feature Showcase */}
        <div className={styles.showcase}>
          <div className={styles.featureCard}>
            <h2 className={styles.cardTitle}>
              {t('settings.theme', 'Theme')} Switching
            </h2>
            <p className={styles.cardDescription}>
              {t('settings.theme.description', 'Choose your preferred theme')}
            </p>
            <div className={styles.cardDemo}>
              <ThemeToggle showLabel={true} size="large" />
            </div>
            <div className={styles.themeInfo}>
              <span className={styles.infoLabel}>
                Current Theme:
              </span>
              <span className={styles.infoValue}>
                {currentTheme === 'dark' ? 'Dark' : 'Light'}
              </span>
            </div>
          </div>

          <div className={styles.featureCard}>
            <h2 className={styles.cardTitle}>
              {t('settings.language', 'Language')} Switching
            </h2>
            <p className={styles.cardDescription}>
              {t('settings.language.description', 'Select your preferred language')}
            </p>
            <div className={styles.cardDemo}>
              <LanguageSelector showLabel={true} showFlags={true} variant="select" />
            </div>
            <div className={styles.languageInfo}>
              <span className={styles.infoLabel}>
                Current Language:
              </span>
              <span className={styles.infoValue}>
                {currentLanguage === 'en' ? 'English' :
                 currentLanguage === 'es' ? 'Español' :
                 currentLanguage === 'fr' ? 'Français' : currentLanguage}
              </span>
            </div>
          </div>

          <div className={styles.featureCard}>
            <h2 className={styles.cardTitle}>
              Google Analytics 4
            </h2>
            <p className={styles.cardDescription}>
              Track user interactions and page views with GA4
            </p>
            <div className={styles.cardDemo}>
              <button
                onClick={handleTestAnalytics}
                className={`enhanced-button ${styles.testButton}`}
              >
                Test Analytics Event
              </button>
            </div>
            <div className={styles.analyticsInfo}>
              <span className={styles.infoLabel}>GA4 ID:</span>
              <span className={styles.infoValue}>G-JBNQD8SDQ1</span>
            </div>
          </div>
        </div>

        {/* Sample Content */}
        <div className={styles.sampleContent}>
          <h2 className={styles.sectionTitle}>
            Sample Content
          </h2>
          <div className={styles.contentGrid}>
            <div className={styles.contentCard}>
              <h3>{t('vehicles.title', 'Vehicles')}</h3>
              <p>Browse our quality pre-owned vehicles with transparent pricing.</p>
              <div className={styles.cardActions}>
                <button className="button-primary">
                  View Details
                </button>
                <button className="button-secondary">
                  Contact Us
                </button>
              </div>
            </div>

            <div className={styles.contentCard}>
              <h3>{t('nav.customers', 'Customers')}</h3>
              <p>Professional automotive services with customer satisfaction guaranteed.</p>
              <div className={styles.cardActions}>
                <button className="button-primary">
                  {t('common.search', 'Search')}
                </button>
                <button className="button-secondary">
                  {t('nav.contact', 'Contact')}
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Settings Component Demo */}
        <div className={styles.settingsDemo}>
          <h2 className={styles.sectionTitle}>
            {t('settings.title', 'Settings')} Component
          </h2>
          <Settings />
        </div>
      </div>
    </div>
    </Suspense>
  );
}
