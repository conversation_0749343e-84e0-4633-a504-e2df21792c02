.container {
  max-width: none;
  margin: 0;
  padding: 2rem 1rem;
  width: 100%;
}

/* Responsive padding adjustments for larger screens */
@media (min-width: 769px) {
  .container {
    padding: 2rem 3rem;
  }
}

@media (min-width: 1200px) {
  .container {
    padding: 2rem 4rem;
  }
}

.header {
  margin-bottom: 2rem;
  text-align: center;
}

.title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.subtitle {
  font-size: 1rem;
  color: var(--text-secondary);
}

.content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.section {
  background: none;
  border: none;
  border-radius: 0;
  padding: 1.5rem 0;
  box-shadow: none;
  border-bottom: 1px solid var(--border-primary);
}

.sectionTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: none;
}

.setting {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.settingInfo {
  flex: 1;
}

.settingLabel {
  font-size: 1rem;
  font-weight: 500;
  color: var(--text-primary);
  display: block;
  margin-bottom: 0.25rem;
}

.settingDescription {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0;
}

.currentSettings {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.currentSetting {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  background: none;
  border-radius: 0;
  border: none;
  border-bottom: 1px solid var(--border-primary);
}

.currentLabel {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.currentValue {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
}

/* Responsive design */
@media (max-width: 640px) {
  .container {
    padding: 1rem;
  }

  .setting {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .currentSetting {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}
