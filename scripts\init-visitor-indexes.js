/**
 * Initialize Visitor Collection Indexes
 * 
 * This script creates optimized indexes for the visitor tracking system:
 * - Primary index on IP for fast lookups
 * - TTL index on createdAt for automatic cleanup (90 days)
 * - Compound indexes for analytics queries
 * - Performance indexes for common query patterns
 */

const mongoose = require('mongoose');
require('dotenv').config();

async function initializeVisitorIndexes() {
  try {
    console.log('🚀 Initializing visitor collection indexes...');
    
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');
    
    const db = mongoose.connection.db;
    const visitorsCollection = db.collection('visitors');
    
    // Drop existing indexes (except _id) to start fresh
    try {
      const existingIndexes = await visitorsCollection.indexes();
      console.log('📋 Existing indexes:', existingIndexes.map(idx => idx.name));
      
      // Drop all indexes except _id_
      for (const index of existingIndexes) {
        if (index.name !== '_id_') {
          await visitorsCollection.dropIndex(index.name);
          console.log(`🗑️  Dropped index: ${index.name}`);
        }
      }
    } catch (error) {
      console.log('ℹ️  No existing indexes to drop or error dropping:', error.message);
    }
    
    console.log('\n📊 Creating optimized indexes...');
    
    // 1. Primary IP index (unique, for fast IP lookups)
    await visitorsCollection.createIndex(
      { ip: 1 }, 
      { 
        unique: true, 
        name: 'ip_unique',
        background: true 
      }
    );
    console.log('✅ Created unique IP index');
    
    // 2. TTL index on createdAt (auto-delete after 90 days)
    await visitorsCollection.createIndex(
      { createdAt: 1 }, 
      { 
        expireAfterSeconds: 7776000, // 90 days in seconds
        name: 'ttl_cleanup',
        background: true 
      }
    );
    console.log('✅ Created TTL index (90 days auto-cleanup)');
    
    // 3. Last visit index (for sorting recent visitors)
    await visitorsCollection.createIndex(
      { lastVisit: -1 }, 
      { 
        name: 'lastVisit_desc',
        background: true 
      }
    );
    console.log('✅ Created lastVisit index');
    
    // 4. Compound index for IP + lastVisit (common query pattern)
    await visitorsCollection.createIndex(
      { ip: 1, lastVisit: -1 }, 
      { 
        name: 'ip_lastVisit',
        background: true 
      }
    );
    console.log('✅ Created compound IP + lastVisit index');
    
    // 5. Country index for geographic analytics
    await visitorsCollection.createIndex(
      { country: 1, createdAt: -1 }, 
      { 
        name: 'country_analytics',
        background: true 
      }
    );
    console.log('✅ Created country analytics index');
    
    // 6. Visit count index for analytics
    await visitorsCollection.createIndex(
      { visitCount: -1 }, 
      { 
        name: 'visitCount_desc',
        background: true 
      }
    );
    console.log('✅ Created visitCount index');
    
    // 7. First visit index for new visitor analytics
    await visitorsCollection.createIndex(
      { firstVisit: -1 }, 
      { 
        name: 'firstVisit_desc',
        background: true 
      }
    );
    console.log('✅ Created firstVisit index');
    
    // 8. Compound index for time-based analytics
    await visitorsCollection.createIndex(
      { createdAt: -1, country: 1 }, 
      { 
        name: 'analytics_time_country',
        background: true 
      }
    );
    console.log('✅ Created time-based analytics index');
    
    // Verify all indexes were created
    console.log('\n🔍 Verifying created indexes...');
    const finalIndexes = await visitorsCollection.indexes();
    
    console.log('\n📋 Final index list:');
    finalIndexes.forEach((index, i) => {
      console.log(`${i + 1}. ${index.name}:`, JSON.stringify(index.key));
      if (index.expireAfterSeconds) {
        console.log(`   ⏰ TTL: ${index.expireAfterSeconds} seconds (${Math.round(index.expireAfterSeconds / 86400)} days)`);
      }
      if (index.unique) {
        console.log(`   🔑 Unique: true`);
      }
    });
    
    // Test index performance with explain
    console.log('\n⚡ Testing index performance...');
    
    // Test IP lookup performance
    const ipExplain = await visitorsCollection.find({ ip: '***********' }).explain('executionStats');
    console.log(`IP lookup - Execution time: ${ipExplain.executionStats.executionTimeMillis}ms`);
    console.log(`IP lookup - Index used: ${ipExplain.executionStats.totalDocsExamined === 0 ? 'YES' : 'NO'}`);
    
    // Test time range query performance
    const timeRangeExplain = await visitorsCollection
      .find({ lastVisit: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) } })
      .explain('executionStats');
    console.log(`Time range query - Execution time: ${timeRangeExplain.executionStats.executionTimeMillis}ms`);
    console.log(`Time range query - Index used: ${timeRangeExplain.executionStats.totalDocsExamined <= timeRangeExplain.executionStats.totalDocsReturned ? 'YES' : 'NO'}`);
    
    console.log('\n🎉 Visitor collection indexes initialized successfully!');
    console.log('\n📈 Performance optimizations:');
    console.log('   • Fast IP lookups with unique index');
    console.log('   • Automatic cleanup after 90 days');
    console.log('   • Optimized analytics queries');
    console.log('   • Geographic distribution analysis');
    console.log('   • Visit pattern tracking');
    
    console.log('\n💡 Usage tips:');
    console.log('   • Queries by IP will be instant');
    console.log('   • Time-based analytics are optimized');
    console.log('   • Old data automatically deleted');
    console.log('   • Geographic queries are efficient');
    
  } catch (error) {
    console.error('❌ Error initializing visitor indexes:', error);
    process.exit(1);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
  }
}

// Run the initialization
if (require.main === module) {
  initializeVisitorIndexes()
    .then(() => {
      console.log('✅ Visitor index initialization completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Visitor index initialization failed:', error);
      process.exit(1);
    });
}

module.exports = { initializeVisitorIndexes };
