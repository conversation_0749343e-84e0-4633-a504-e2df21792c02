{"name": "@fazenauto/frontend", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@aws-sdk/client-s3": "^3.817.0", "@aws-sdk/s3-request-presigner": "^3.844.0", "@heroicons/react": "^2.0.18", "@tanstack/react-table": "^8.21.3", "bcryptjs": "^3.0.2", "dotenv": "^16.5.0", "formidable": "^3.5.4", "hamburger-react": "^2.5.2", "mongoose": "^8.15.0", "next": "^15.3.2", "pdfkit": "^0.17.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "eslint": "^9", "eslint-config-next": "15.1.8"}, "engines": {"node": ">=20.17.0", "npm": ">=10.0.0"}}