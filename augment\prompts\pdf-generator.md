# PDF Generator Prompt

## Description
This prompt helps generate PDF documents for invoices, compliance forms, and reports in the FazeNAuto platform.

## Prompt Template

```
Create a PDF generator for [DOCUMENT_TYPE] with the following requirements:

**Document Details:**
- Type: [INVOICE/BILL_OF_SALE/OMVIC_FORM/HST_FORM/REPORT]
- Purpose: [DESCRIBE_PURPOSE]
- Template: [DESCRIBE_LAYOUT_REQUIREMENTS]
- Branding: FazeNAuto with red 'N' accent

**Data Requirements:**
- Input data: [DESCRIBE_INPUT_STRUCTURE]
- Dynamic fields: [LIST_DYNAMIC_FIELDS]
- Calculations: [DESCRIBE_CALCULATIONS]
- Validation: [DESCRIBE_VALIDATION_RULES]

**Layout Specifications:**
- Page size: [A4/LETTER]
- Orientation: [PORTRAIT/LANDSCAPE]
- Margins: [SPECIFY_MARGINS]
- Header/Footer: [DESCRIBE_HEADER_FOOTER]
- Sections: [LIST_DOCUMENT_SECTIONS]

**Styling Requirements:**
- Font: Professional (Arial/Helvetica)
- Colors: Dark theme with red accents (#ef4444)
- Logo: FazeNAuto branding
- Tables: Clean, bordered tables
- Signatures: Signature fields where needed

**Business Logic:**
- Calculations: [TAX_CALCULATIONS/TOTALS/FEES]
- Conditional content: [DESCRIBE_CONDITIONS]
- Multi-page handling: [YES/NO]
- Watermarks: [DRAFT/FINAL/NONE]

**Follow these FazeNAuto conventions:**
1. Use PDFKit or jsPDF for PDF generation
2. Include FazeNAuto branding and contact info
3. Use consistent styling and layout
4. Add proper error handling
5. Include data validation
6. Support both preview and download
7. Add audit trail (creation date, user)
8. Use environment-specific settings
9. Include proper number formatting
10. Add accessibility features

**Example structure:**
```javascript
import PDFDocument from 'pdfkit';
import fs from 'fs';

class DocumentGenerator {
  constructor(data) {
    this.data = data;
    this.doc = new PDFDocument();
  }

  async generate() {
    try {
      this.addHeader();
      this.addContent();
      this.addFooter();
      return this.finalize();
    } catch (error) {
      console.error('PDF generation failed:', error);
      throw error;
    }
  }

  addHeader() {
    // Header implementation
  }

  addContent() {
    // Content implementation
  }

  addFooter() {
    // Footer implementation
  }
}
```

Generate the complete PDF generator implementation.
```

## Example Usage

### Creating an Invoice PDF Generator
```
Create a PDF generator for INVOICE with the following requirements:

**Document Details:**
- Type: INVOICE
- Purpose: Generate professional invoices for vehicle sales
- Template: Standard business invoice layout
- Branding: FazeNAuto with red 'N' accent

**Data Requirements:**
- Input data: Invoice object with buyer, seller, vehicle, pricing details
- Dynamic fields: Invoice number, date, vehicle details, pricing breakdown
- Calculations: Subtotal, HST, total, deposit, balance due
- Validation: Required fields, valid amounts, proper formatting

**Layout Specifications:**
- Page size: A4
- Orientation: PORTRAIT
- Margins: 1 inch all sides
- Header/Footer: Company info in header, terms in footer
- Sections: Header, buyer/seller info, vehicle details, pricing table, payment terms

**Styling Requirements:**
- Font: Professional (Arial/Helvetica)
- Colors: Dark theme with red accents (#ef4444)
- Logo: FazeNAuto branding
- Tables: Clean, bordered tables for pricing
- Signatures: Buyer and seller signature fields

**Business Logic:**
- Calculations: HST (13% in Ontario), subtotal + tax = total
- Conditional content: Show deposit section if deposit paid
- Multi-page handling: YES for long vehicle feature lists
- Watermarks: DRAFT for preview, none for final
```

## Expected Output Structure

### 1. Main PDF Generator Class
```javascript
import PDFDocument from 'pdfkit';
import path from 'path';

class InvoiceGenerator {
  constructor(invoiceData) {
    this.data = invoiceData;
    this.doc = new PDFDocument({
      size: 'A4',
      margin: 72, // 1 inch margins
      info: {
        Title: `Invoice ${invoiceData.invoiceNumber}`,
        Author: 'FazeNAuto',
        Subject: 'Vehicle Sale Invoice',
        Creator: 'FazeNAuto Invoice System'
      }
    });
    
    this.currentY = 72;
    this.pageWidth = 595.28 - 144; // A4 width minus margins
  }

  async generate() {
    try {
      // Validate input data
      this.validateData();
      
      // Generate PDF content
      this.addHeader();
      this.addCompanyInfo();
      this.addInvoiceDetails();
      this.addBuyerSellerInfo();
      this.addVehicleDetails();
      this.addPricingTable();
      this.addPaymentTerms();
      this.addSignatureSection();
      this.addFooter();
      
      return this.finalize();
    } catch (error) {
      console.error('Invoice generation failed:', error);
      throw new Error(`Failed to generate invoice: ${error.message}`);
    }
  }

  validateData() {
    const required = ['invoiceNumber', 'date', 'buyer', 'vehicle', 'pricing'];
    for (const field of required) {
      if (!this.data[field]) {
        throw new Error(`Missing required field: ${field}`);
      }
    }
  }

  addHeader() {
    // Add FazeNAuto logo and branding
    this.doc
      .fontSize(24)
      .fillColor('#000000')
      .text('Faze', 72, this.currentY)
      .fillColor('#ef4444')
      .text('N', 130, this.currentY)
      .fillColor('#000000')
      .text('Auto', 150, this.currentY);
    
    // Add invoice title
    this.doc
      .fontSize(20)
      .fillColor('#333333')
      .text('INVOICE', 400, this.currentY, { align: 'right' });
    
    this.currentY += 40;
    this.addHorizontalLine();
  }

  addCompanyInfo() {
    const companyInfo = [
      'FazeNAuto',
      '1120 Meighen Way',
      'Phone: ************',
      'Email: <EMAIL>'
    ];
    
    this.doc.fontSize(10).fillColor('#666666');
    companyInfo.forEach(line => {
      this.doc.text(line, 72, this.currentY);
      this.currentY += 12;
    });
    
    this.currentY += 20;
  }

  addInvoiceDetails() {
    const details = [
      `Invoice #: ${this.data.invoiceNumber}`,
      `Date: ${this.formatDate(this.data.date)}`,
      `Due Date: ${this.formatDate(this.data.dueDate || this.data.date)}`
    ];
    
    this.doc.fontSize(12).fillColor('#333333');
    details.forEach(detail => {
      this.doc.text(detail, 400, this.currentY, { align: 'right' });
      this.currentY += 15;
    });
    
    this.currentY += 20;
  }

  addBuyerSellerInfo() {
    // Buyer info (left side)
    this.doc
      .fontSize(14)
      .fillColor('#000000')
      .text('Bill To:', 72, this.currentY);
    
    this.currentY += 20;
    
    const buyerInfo = [
      this.data.buyer.name,
      this.data.buyer.address,
      this.data.buyer.city + ', ' + this.data.buyer.province + ' ' + this.data.buyer.postalCode,
      this.data.buyer.phone,
      this.data.buyer.email
    ];
    
    this.doc.fontSize(10).fillColor('#333333');
    buyerInfo.forEach(line => {
      if (line) {
        this.doc.text(line, 72, this.currentY);
        this.currentY += 12;
      }
    });
    
    this.currentY += 20;
  }

  addVehicleDetails() {
    this.doc
      .fontSize(14)
      .fillColor('#000000')
      .text('Vehicle Details:', 72, this.currentY);
    
    this.currentY += 20;
    
    const vehicle = this.data.vehicle;
    const vehicleDetails = [
      `${vehicle.year} ${vehicle.make} ${vehicle.model}`,
      `VIN: ${vehicle.vin}`,
      `Color: ${vehicle.color}`,
      `Mileage: ${this.formatNumber(vehicle.mileage)} km`,
      `Engine: ${vehicle.engine}`,
      `Transmission: ${vehicle.transmission}`
    ];
    
    this.doc.fontSize(10).fillColor('#333333');
    vehicleDetails.forEach(detail => {
      this.doc.text(detail, 72, this.currentY);
      this.currentY += 12;
    });
    
    this.currentY += 20;
  }

  addPricingTable() {
    this.doc
      .fontSize(14)
      .fillColor('#000000')
      .text('Pricing Details:', 72, this.currentY);
    
    this.currentY += 20;
    
    // Table headers
    const tableTop = this.currentY;
    const itemX = 72;
    const amountX = 450;
    
    this.doc
      .fontSize(12)
      .fillColor('#000000')
      .text('Description', itemX, tableTop)
      .text('Amount', amountX, tableTop);
    
    this.currentY += 20;
    this.addHorizontalLine();
    this.currentY += 10;
    
    // Pricing rows
    const pricing = this.data.pricing;
    const rows = [
      { description: 'Vehicle Price', amount: pricing.vehiclePrice },
      { description: 'HST (13%)', amount: pricing.hst },
      { description: 'Total', amount: pricing.total, bold: true }
    ];
    
    if (pricing.deposit) {
      rows.push(
        { description: 'Deposit Paid', amount: -pricing.deposit },
        { description: 'Balance Due', amount: pricing.total - pricing.deposit, bold: true }
      );
    }
    
    rows.forEach(row => {
      const fontSize = row.bold ? 12 : 10;
      const color = row.bold ? '#000000' : '#333333';
      
      this.doc
        .fontSize(fontSize)
        .fillColor(color)
        .text(row.description, itemX, this.currentY)
        .text(this.formatCurrency(row.amount), amountX, this.currentY);
      
      this.currentY += 15;
    });
    
    this.currentY += 20;
  }

  addPaymentTerms() {
    this.doc
      .fontSize(12)
      .fillColor('#000000')
      .text('Payment Terms:', 72, this.currentY);
    
    this.currentY += 15;
    
    const terms = [
      'Payment is due upon delivery of vehicle.',
      'Vehicle sold as-is without warranty.',
      'All sales are final.',
      'HST included where applicable.'
    ];
    
    this.doc.fontSize(10).fillColor('#666666');
    terms.forEach(term => {
      this.doc.text(`• ${term}`, 72, this.currentY);
      this.currentY += 12;
    });
    
    this.currentY += 20;
  }

  addSignatureSection() {
    const signatureY = this.currentY;
    
    // Buyer signature
    this.doc
      .fontSize(10)
      .fillColor('#333333')
      .text('Buyer Signature:', 72, signatureY)
      .text('Date:', 72, signatureY + 40);
    
    // Seller signature
    this.doc
      .text('Seller Signature:', 300, signatureY)
      .text('Date:', 300, signatureY + 40);
    
    // Signature lines
    this.doc
      .moveTo(72, signatureY + 25)
      .lineTo(250, signatureY + 25)
      .stroke()
      .moveTo(300, signatureY + 25)
      .lineTo(480, signatureY + 25)
      .stroke();
    
    this.currentY = signatureY + 80;
  }

  addFooter() {
    const footerY = 750; // Near bottom of page
    
    this.doc
      .fontSize(8)
      .fillColor('#999999')
      .text('FazeNAuto - Your trusted automotive partner', 72, footerY, {
        align: 'center',
        width: this.pageWidth
      });
  }

  addHorizontalLine() {
    this.doc
      .moveTo(72, this.currentY)
      .lineTo(72 + this.pageWidth, this.currentY)
      .stroke('#cccccc');
  }

  formatDate(date) {
    return new Date(date).toLocaleDateString('en-CA');
  }

  formatCurrency(amount) {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD'
    }).format(amount);
  }

  formatNumber(number) {
    return new Intl.NumberFormat('en-CA').format(number);
  }

  finalize() {
    this.doc.end();
    return this.doc;
  }
}

export default InvoiceGenerator;
```

### 2. API Route Integration
```javascript
// /api/invoices/[id]/pdf/route.js
import { NextResponse } from 'next/server';
import InvoiceGenerator from '@/lib/pdf/InvoiceGenerator';
import Invoice from '@/models/Invoice';

export async function GET(request, { params }) {
  try {
    const invoice = await Invoice.findById(params.id);
    
    if (!invoice) {
      return NextResponse.json(
        { error: 'Invoice not found' },
        { status: 404 }
      );
    }
    
    const generator = new InvoiceGenerator(invoice);
    const pdfDoc = await generator.generate();
    
    const chunks = [];
    pdfDoc.on('data', chunk => chunks.push(chunk));
    
    return new Promise((resolve) => {
      pdfDoc.on('end', () => {
        const pdfBuffer = Buffer.concat(chunks);
        
        resolve(new NextResponse(pdfBuffer, {
          headers: {
            'Content-Type': 'application/pdf',
            'Content-Disposition': `attachment; filename="invoice-${invoice.invoiceNumber}.pdf"`
          }
        }));
      });
    });
    
  } catch (error) {
    console.error('PDF generation error:', error);
    return NextResponse.json(
      { error: 'Failed to generate PDF' },
      { status: 500 }
    );
  }
}
```

## Common PDF Patterns

### Table Generation
```javascript
addTable(headers, rows, startY) {
  const tableTop = startY;
  const rowHeight = 20;
  const colWidth = this.pageWidth / headers.length;
  
  // Headers
  headers.forEach((header, i) => {
    this.doc
      .fontSize(12)
      .fillColor('#000000')
      .text(header, 72 + (i * colWidth), tableTop);
  });
  
  // Rows
  rows.forEach((row, rowIndex) => {
    const y = tableTop + ((rowIndex + 1) * rowHeight);
    
    row.forEach((cell, colIndex) => {
      this.doc
        .fontSize(10)
        .fillColor('#333333')
        .text(cell, 72 + (colIndex * colWidth), y);
    });
  });
  
  return tableTop + ((rows.length + 1) * rowHeight);
}
```

### Multi-page Handling
```javascript
checkPageBreak(requiredSpace = 100) {
  if (this.currentY + requiredSpace > 700) {
    this.doc.addPage();
    this.currentY = 72;
    this.addHeader();
  }
}
```

---

*Use this prompt to generate professional PDF documents for the FazeNAuto platform! 📄*
