.container {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.select {
  background: var(--bg-primary);
  color: var(--text-primary);
  border: 1px solid var(--border-primary);
  border-radius: 0.375rem;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
}

.select:hover {
  background: var(--bg-secondary);
  border-color: var(--border-secondary);
}

.select:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

/* Light mode specific styling */
[data-theme="light"] .select {
  background: #ffffff;
  border: 1px solid #d1d5db;
  color: #1f2937 !important;
}

[data-theme="light"] .select:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

[data-theme="light"] .select option {
  background: #ffffff;
  color: #1f2937 !important;
}

/* Collapsed state styling for light mode */
[data-theme="light"] .select.collapsed-select {
  background: #ffffff !important;
  color: #1f2937 !important;
  border: 1px solid #d1d5db;
  font-weight: 600;
  text-align: center;
}

[data-theme="light"] .select.collapsed-select option {
  background: #ffffff !important;
  color: #1f2937 !important;
  font-weight: 600;
}

/* Additional styling for sidebar context */
.sidebar .select {
  color: inherit;
}

[data-theme="light"] .sidebar .select.collapsed-select {
  background: #ffffff !important;
  color: #1f2937 !important;
  border: 1px solid #d1d5db !important;
}

/* Dark mode specific styling */
[data-theme="dark"] .select {
  background: #0d1117;
  border: 1px solid #333;
  color: #ffffff;
}

[data-theme="dark"] .select:hover {
  background: #161b22;
  border-color: #444;
}

[data-theme="dark"] .select option {
  background: #161b22;
  color: #ffffff;
}

.dropdown {
  position: relative;
}

.dropdownButton {
  background: var(--bg-primary);
  color: var(--text-primary);
  border: 1px solid var(--border-primary);
  border-radius: 0.375rem;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 120px;
}

.dropdownButton:hover {
  background: var(--bg-secondary);
  border-color: var(--border-secondary);
}

.dropdownButton:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

/* Dark mode specific styling for dropdown button */
[data-theme="dark"] .dropdownButton {
  background: linear-gradient(to bottom right, #0d1117, #161b22);
  border: 1px solid #333;
  color: #ffffff;
}

[data-theme="dark"] .dropdownButton:hover {
  background: linear-gradient(to bottom right, #161b22, #1a1f2b);
  border-color: #444;
}

/* Dark mode gradient background for dropdown list */
[data-theme="dark"] .dropdownList {
  background: linear-gradient(to bottom right, #0d1117, #161b22) !important;
  border: 1px solid #333;
}

.chevron {
  width: 1rem;
  height: 1rem;
  margin-left: auto;
  transition: transform 0.2s ease;
}

.dropdownButton:hover .chevron {
  transform: translateY(1px);
}

/* RTL Support for Language Selector */
[dir="rtl"] .container {
  direction: rtl;
}

[dir="rtl"] .dropdownButton {
  flex-direction: row-reverse;
}

[dir="rtl"] .chevron {
  margin-left: 0;
  margin-right: auto;
}
