'use client';

import { useState, useRef } from 'react';
import styles from './MobilePostingUI.module.css';

export default function MobilePostingUI({ onPostingComplete }) {
  const [step, setStep] = useState(1);
  const [vin, setVin] = useState('');
  const [images, setImages] = useState([]);
  const [generatedAd, setGeneratedAd] = useState(null);
  const [processing, setProcessing] = useState(false);
  const fileInputRef = useRef(null);
  const cameraInputRef = useRef(null);

  const steps = [
    { id: 1, name: 'VIN Entry', icon: '🔍' },
    { id: 2, name: 'Photos', icon: '📷' },
    { id: 3, name: 'Generate Ad', icon: '🎯' },
    { id: 4, name: 'Post', icon: '🚀' }
  ];

  const handleVinChange = (e) => {
    const value = e.target.value.toUpperCase().replace(/[^A-HJ-NPR-Z0-9]/g, '');
    if (value.length <= 17) {
      setVin(value);
    }
  };

  const handleImageCapture = (event) => {
    const files = Array.from(event.target.files);
    if (files.length + images.length > 20) {
      alert('Maximum 20 images allowed');
      return;
    }

    const newImages = files.map(file => ({
      file,
      url: URL.createObjectURL(file),
      id: Date.now() + Math.random()
    }));

    setImages(prev => [...prev, ...newImages]);
  };

  const removeImage = (imageId) => {
    setImages(prev => {
      const updated = prev.filter(img => img.id !== imageId);
      // Clean up object URLs
      const removed = prev.find(img => img.id === imageId);
      if (removed) {
        URL.revokeObjectURL(removed.url);
      }
      return updated;
    });
  };

  const generateAd = async () => {
    if (vin.length !== 17) {
      alert('Please enter a valid 17-character VIN');
      return;
    }

    setProcessing(true);

    try {
      const response = await fetch('/api/ad-generator', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          vin,
          platforms: ['facebook', 'kijiji'],
          includeEmojis: true,
          tone: 'casual',
          additionalFeatures: []
        })
      });

      const data = await response.json();
      
      if (data.success) {
        setGeneratedAd(data);
        setStep(4);
      } else {
        alert(`Ad generation failed: ${data.error}`);
      }

    } catch (error) {
      console.error('Ad generation failed:', error);
      alert('Failed to generate ad. Please try again.');
    } finally {
      setProcessing(false);
    }
  };

  const copyToClipboard = async (text, platform) => {
    try {
      await navigator.clipboard.writeText(text);
      
      // Show success feedback
      const button = document.activeElement;
      const originalText = button.textContent;
      button.textContent = '✅ Copied!';
      button.style.background = '#059669';
      
      setTimeout(() => {
        button.textContent = originalText;
        button.style.background = '';
      }, 2000);
      
    } catch (error) {
      console.error('Failed to copy:', error);
      alert('Failed to copy to clipboard');
    }
  };

  const openPlatform = (platform) => {
    const urls = {
      facebook: 'https://www.facebook.com/marketplace/create/vehicle',
      kijiji: 'https://www.kijiji.ca/p-post-ad.html',
      craigslist: 'https://craigslist.org'
    };

    if (urls[platform]) {
      window.open(urls[platform], '_blank');
    }
  };

  const renderStep = () => {
    switch (step) {
      case 1:
        return (
          <div className={styles.stepContent}>
            <div className={styles.stepHeader}>
              <h3>🔍 Enter Vehicle VIN</h3>
              <p>Scan or type the 17-character VIN</p>
            </div>

            <div className={styles.vinInput}>
              <input
                type="text"
                value={vin}
                onChange={handleVinChange}
                placeholder="Enter VIN (17 characters)"
                className={styles.vinField}
                autoFocus
              />
              <div className={styles.vinStatus}>
                {vin.length > 0 && (
                  <span className={vin.length === 17 ? styles.valid : styles.invalid}>
                    {vin.length}/17
                  </span>
                )}
              </div>
            </div>

            <div className={styles.stepActions}>
              <button
                onClick={() => setStep(2)}
                disabled={vin.length !== 17}
                className={`enhanced-button ${styles.nextBtn}`}
              >
                Next: Add Photos 📷
              </button>
            </div>
          </div>
        );

      case 2:
        return (
          <div className={styles.stepContent}>
            <div className={styles.stepHeader}>
              <h3>📷 Add Vehicle Photos</h3>
              <p>Take photos or select from gallery (max 20)</p>
            </div>

            <div className={styles.photoActions}>
              <input
                ref={cameraInputRef}
                type="file"
                accept="image/*"
                capture="environment"
                multiple
                onChange={handleImageCapture}
                className={styles.hiddenInput}
              />
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                multiple
                onChange={handleImageCapture}
                className={styles.hiddenInput}
              />
              
              <button
                onClick={() => cameraInputRef.current?.click()}
                className={`enhanced-button ${styles.photoBtn}`}
              >
                📸 Take Photos
              </button>

              <button
                onClick={() => fileInputRef.current?.click()}
                className={`enhanced-button ${styles.photoBtn}`}
              >
                🖼️ Choose from Gallery
              </button>
            </div>

            {images.length > 0 && (
              <div className={styles.imageGrid}>
                {images.map(image => (
                  <div key={image.id} className={styles.imageItem}>
                    <img src={image.url} alt="Vehicle" />
                    <button
                      onClick={() => removeImage(image.id)}
                      className={`enhanced-button ${styles.removeBtn}`}
                    >
                      ×
                    </button>
                  </div>
                ))}
              </div>
            )}

            <div className={styles.stepActions}>
              <button
                onClick={() => setStep(1)}
                className={`enhanced-button ${styles.backBtn}`}
              >
                ← Back
              </button>
              <button
                onClick={() => setStep(3)}
                disabled={images.length === 0}
                className={`enhanced-button ${styles.nextBtn}`}
              >
                Next: Generate Ad 🎯
              </button>
            </div>
          </div>
        );

      case 3:
        return (
          <div className={styles.stepContent}>
            <div className={styles.stepHeader}>
              <h3>🎯 Generate Listing</h3>
              <p>Create optimized ads for multiple platforms</p>
            </div>

            <div className={styles.generateSection}>
              <div className={styles.summary}>
                <div className={styles.summaryItem}>
                  <span className={styles.label}>VIN:</span>
                  <span className={styles.value}>{vin}</span>
                </div>
                <div className={styles.summaryItem}>
                  <span className={styles.label}>Photos:</span>
                  <span className={styles.value}>{images.length} images</span>
                </div>
              </div>

              <button
                onClick={generateAd}
                disabled={processing}
                className={`enhanced-button ${styles.generateBtn}`}
              >
                {processing ? '🔄 Generating...' : '🎯 Generate Ads'}
              </button>
            </div>

            <div className={styles.stepActions}>
              <button
                onClick={() => setStep(2)}
                className={`enhanced-button ${styles.backBtn}`}
              >
                ← Back
              </button>
            </div>
          </div>
        );

      case 4:
        return (
          <div className={styles.stepContent}>
            <div className={styles.stepHeader}>
              <h3>🚀 Ready to Post</h3>
              <p>Copy ads and post to your favorite platforms</p>
            </div>

            {generatedAd && (
              <div className={styles.generatedAds}>
                {Object.entries(generatedAd.platformAds).map(([platform, ad]) => (
                  <div key={platform} className={styles.platformAd}>
                    <div className={styles.platformHeader}>
                      <h4>
                        {platform === 'facebook' ? '📘 Facebook' : '🏷️ Kijiji'}
                      </h4>
                    </div>
                    
                    <div className={styles.adPreview}>
                      <div className={styles.adTitle}>
                        <strong>{ad.title}</strong>
                      </div>
                      <div className={styles.adDescription}>
                        {ad.description?.substring(0, 150)}...
                      </div>
                    </div>

                    <div className={styles.adActions}>
                      <button
                        onClick={() => copyToClipboard(`${ad.title}\n\n${ad.description}`, platform)}
                        className={`enhanced-button ${styles.copyBtn}`}
                      >
                        📋 Copy Ad
                      </button>
                      <button
                        onClick={() => openPlatform(platform)}
                        className={`enhanced-button ${styles.postBtn}`}
                      >
                        🚀 Post Now
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}

            <div className={styles.stepActions}>
              <button
                onClick={() => {
                  setStep(1);
                  setVin('');
                  setImages([]);
                  setGeneratedAd(null);
                }}
                className={`enhanced-button ${styles.newPostingBtn}`}
              >
                ➕ New Posting
              </button>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className={styles.mobilePostingUI}>
      {/* Progress Steps */}
      <div className={styles.progressSteps}>
        {steps.map(stepItem => (
          <div
            key={stepItem.id}
            className={`${styles.progressStep} ${step >= stepItem.id ? styles.active : ''} ${step === stepItem.id ? styles.current : ''}`}
          >
            <div className={styles.stepIcon}>{stepItem.icon}</div>
            <div className={styles.stepName}>{stepItem.name}</div>
          </div>
        ))}
      </div>

      {/* Step Content */}
      <div className={styles.stepContainer}>
        {renderStep()}
      </div>

      {/* Mobile Tips */}
      <div className={styles.mobileTips}>
        <h4>💡 Mobile Tips</h4>
        <ul>
          <li>Use your camera to scan VINs directly</li>
          <li>Take photos in good lighting for best results</li>
          <li>Copy ads and paste directly into Facebook/Kijiji</li>
          <li>Save this page to your home screen for quick access</li>
        </ul>
      </div>
    </div>
  );
}
