import PDFDocument from 'pdfkit';
import fs from 'fs';
import path from 'path';

/**
 * PDF Generator for Dealership Compliance Forms
 * Generates Bill of Sale, UVIP, OMVIC disclosure, and tax forms
 */
export class PDFGenerator {
  constructor() {
    this.dealerInfo = {
      name: 'FazeNAuto',
      address: '1120 Meighen Way',
      phone: '************',
      email: '<EMAIL>',
      omvicNumber: 'PENDING', // You'll need to get this when registered
      hstNumber: 'PENDING'    // You'll need to get this for tax purposes
    };
  }

  /**
   * Generate Bill of Sale PDF
   * @param {Object} saleData - Sale transaction data
   * @returns {Promise<Buffer>} - PDF buffer
   */
  async generateBillOfSale(saleData) {
    const {
      vehicle,
      buyer,
      seller = this.dealerInfo,
      salePrice,
      saleDate = new Date(),
      paymentMethod = 'Cash',
      tradeInValue = 0,
      taxes = {},
      warranties = []
    } = saleData;

    const doc = new PDFDocument({ margin: 50 });
    const chunks = [];

    doc.on('data', chunk => chunks.push(chunk));
    
    return new Promise((resolve, reject) => {
      doc.on('end', () => resolve(Buffer.concat(chunks)));
      doc.on('error', reject);

      // Header
      doc.fontSize(20).text('BILL OF SALE', { align: 'center' });
      doc.moveDown();

      // Dealer Information
      doc.fontSize(14).text('DEALER INFORMATION', { underline: true });
      doc.fontSize(12)
         .text(`Business Name: ${seller.name}`)
         .text(`Address: ${seller.address}`)
         .text(`Phone: ${seller.phone}`)
         .text(`Email: ${seller.email}`)
         .text(`OMVIC Registration: ${seller.omvicNumber}`)
         .moveDown();

      // Buyer Information
      doc.fontSize(14).text('BUYER INFORMATION', { underline: true });
      doc.fontSize(12)
         .text(`Name: ${buyer.name}`)
         .text(`Address: ${buyer.address}`)
         .text(`Phone: ${buyer.phone}`)
         .text(`Email: ${buyer.email || 'N/A'}`)
         .text(`Driver's License: ${buyer.driversLicense}`)
         .moveDown();

      // Vehicle Information
      doc.fontSize(14).text('VEHICLE INFORMATION', { underline: true });
      doc.fontSize(12)
         .text(`Year: ${vehicle.year}`)
         .text(`Make: ${vehicle.make}`)
         .text(`Model: ${vehicle.model}`)
         .text(`VIN: ${vehicle.vin}`)
         .text(`Odometer: ${vehicle.mileage} km`)
         .text(`Color: ${vehicle.color}`)
         .text(`Body Style: ${vehicle.bodyStyle || 'N/A'}`)
         .moveDown();

      // Financial Details
      doc.fontSize(14).text('FINANCIAL DETAILS', { underline: true });
      const subtotal = salePrice - tradeInValue;
      const hst = taxes.hst || (subtotal * 0.13); // 13% HST for Ontario
      const total = subtotal + hst;

      doc.fontSize(12)
         .text(`Sale Price: $${salePrice.toLocaleString()}`)
         .text(`Trade-in Value: -$${tradeInValue.toLocaleString()}`)
         .text(`Subtotal: $${subtotal.toLocaleString()}`)
         .text(`HST (13%): $${hst.toLocaleString()}`)
         .text(`Total Amount: $${total.toLocaleString()}`, { underline: true })
         .text(`Payment Method: ${paymentMethod}`)
         .moveDown();

      // Warranties
      if (warranties.length > 0) {
        doc.fontSize(14).text('WARRANTIES', { underline: true });
        warranties.forEach(warranty => {
          doc.fontSize(12).text(`• ${warranty.type}: ${warranty.description}`);
        });
        doc.moveDown();
      }

      // Terms and Conditions
      doc.fontSize(14).text('TERMS AND CONDITIONS', { underline: true });
      doc.fontSize(10)
         .text('1. This vehicle is sold "AS IS" without warranty unless otherwise specified above.')
         .text('2. The buyer acknowledges receipt of all required disclosure documents.')
         .text('3. The buyer is responsible for vehicle registration and insurance.')
         .text('4. This sale is final and no returns are accepted.')
         .text('5. The buyer has inspected the vehicle and accepts its current condition.')
         .moveDown();

      // Signatures
      doc.fontSize(12).text('SIGNATURES', { underline: true });
      doc.moveDown();
      
      doc.text('Buyer Signature: _________________________ Date: _____________');
      doc.moveDown();
      doc.text('Dealer Representative: _________________________ Date: _____________');
      doc.moveDown();
      doc.text(`${seller.name} - ${seller.phone}`);

      // Footer
      doc.fontSize(8)
         .text(`Generated on ${new Date().toLocaleDateString()} by FazeNAuto Management System`, 
               { align: 'center' });

      doc.end();
    });
  }

  /**
   * Generate Used Vehicle Information Package (UVIP) Request Form
   * @param {Object} vehicleData - Vehicle information
   * @returns {Promise<Buffer>} - PDF buffer
   */
  async generateUVIPRequest(vehicleData) {
    const { vehicle, buyer } = vehicleData;
    const doc = new PDFDocument({ margin: 50 });
    const chunks = [];

    doc.on('data', chunk => chunks.push(chunk));
    
    return new Promise((resolve, reject) => {
      doc.on('end', () => resolve(Buffer.concat(chunks)));
      doc.on('error', reject);

      // Header
      doc.fontSize(18).text('USED VEHICLE INFORMATION PACKAGE (UVIP)', { align: 'center' });
      doc.fontSize(12).text('Request Form', { align: 'center' });
      doc.moveDown();

      // Instructions
      doc.fontSize(10)
         .text('This form is to be completed and submitted to ServiceOntario to obtain the UVIP for the vehicle being sold.')
         .moveDown();

      // Vehicle Information
      doc.fontSize(14).text('VEHICLE INFORMATION', { underline: true });
      doc.fontSize(12)
         .text(`VIN: ${vehicle.vin}`)
         .text(`Year: ${vehicle.year}`)
         .text(`Make: ${vehicle.make}`)
         .text(`Model: ${vehicle.model}`)
         .text(`License Plate: ________________`)
         .moveDown();

      // Dealer Information
      doc.fontSize(14).text('DEALER INFORMATION', { underline: true });
      doc.fontSize(12)
         .text(`Business Name: ${this.dealerInfo.name}`)
         .text(`Address: ${this.dealerInfo.address}`)
         .text(`Phone: ${this.dealerInfo.phone}`)
         .text(`OMVIC Registration: ${this.dealerInfo.omvicNumber}`)
         .moveDown();

      // Required Documents Checklist
      doc.fontSize(14).text('REQUIRED DOCUMENTS CHECKLIST', { underline: true });
      doc.fontSize(12)
         .text('☐ Vehicle Registration')
         .text('☐ Proof of Insurance')
         .text('☐ Safety Standards Certificate (if required)')
         .text('☐ Emissions Test Certificate (if required)')
         .text('☐ Lien Information (if applicable)')
         .moveDown();

      // Instructions for ServiceOntario
      doc.fontSize(14).text('INSTRUCTIONS', { underline: true });
      doc.fontSize(10)
         .text('1. Take this form and required documents to any ServiceOntario location')
         .text('2. Pay the UVIP fee (currently $20)')
         .text('3. The UVIP must be provided to the buyer before the sale')
         .text('4. Keep a copy for your records')
         .moveDown();

      // Footer
      doc.fontSize(8)
         .text(`Generated on ${new Date().toLocaleDateString()} by FazeNAuto Management System`, 
               { align: 'center' });

      doc.end();
    });
  }

  /**
   * Generate OMVIC Disclosure Form
   * @param {Object} disclosureData - Disclosure information
   * @returns {Promise<Buffer>} - PDF buffer
   */
  async generateOMVICDisclosure(disclosureData) {
    const { vehicle, disclosures = [] } = disclosureData;
    const doc = new PDFDocument({ margin: 50 });
    const chunks = [];

    doc.on('data', chunk => chunks.push(chunk));
    
    return new Promise((resolve, reject) => {
      doc.on('end', () => resolve(Buffer.concat(chunks)));
      doc.on('error', reject);

      // Header
      doc.fontSize(18).text('OMVIC DISCLOSURE STATEMENT', { align: 'center' });
      doc.moveDown();

      // Dealer Information
      doc.fontSize(14).text('DEALER INFORMATION', { underline: true });
      doc.fontSize(12)
         .text(`Business Name: ${this.dealerInfo.name}`)
         .text(`Address: ${this.dealerInfo.address}`)
         .text(`Phone: ${this.dealerInfo.phone}`)
         .text(`OMVIC Registration: ${this.dealerInfo.omvicNumber}`)
         .moveDown();

      // Vehicle Information
      doc.fontSize(14).text('VEHICLE INFORMATION', { underline: true });
      doc.fontSize(12)
         .text(`Year: ${vehicle.year}`)
         .text(`Make: ${vehicle.make}`)
         .text(`Model: ${vehicle.model}`)
         .text(`VIN: ${vehicle.vin}`)
         .text(`Odometer: ${vehicle.mileage} km`)
         .moveDown();

      // Mandatory Disclosures
      doc.fontSize(14).text('MANDATORY DISCLOSURES', { underline: true });
      doc.fontSize(12);

      const standardDisclosures = [
        'This vehicle is being sold "AS IS" and is not represented as being in road worthy condition.',
        'No warranty or guarantee is given by the dealer unless specified in writing.',
        'The buyer is advised to have the vehicle inspected by a qualified mechanic.',
        'The dealer is not responsible for any repairs needed after the sale.',
        ...disclosures
      ];

      standardDisclosures.forEach((disclosure, index) => {
        doc.text(`${index + 1}. ${disclosure}`);
      });

      doc.moveDown();

      // Buyer Acknowledgment
      doc.fontSize(14).text('BUYER ACKNOWLEDGMENT', { underline: true });
      doc.fontSize(12)
         .text('I acknowledge that I have read and understand all disclosures above.')
         .moveDown();

      doc.text('Buyer Signature: _________________________ Date: _____________');
      doc.moveDown();
      doc.text('Dealer Representative: _________________________ Date: _____________');

      // Footer
      doc.fontSize(8)
         .text(`Generated on ${new Date().toLocaleDateString()} by FazeNAuto Management System`, 
               { align: 'center' });

      doc.end();
    });
  }

  /**
   * Generate HST Return Reminder
   * @param {Object} taxData - Tax period data
   * @returns {Promise<Buffer>} - PDF buffer
   */
  async generateHSTReminder(taxData) {
    const { 
      period, 
      sales = [], 
      totalHSTCollected = 0,
      dueDate 
    } = taxData;

    const doc = new PDFDocument({ margin: 50 });
    const chunks = [];

    doc.on('data', chunk => chunks.push(chunk));
    
    return new Promise((resolve, reject) => {
      doc.on('end', () => resolve(Buffer.concat(chunks)));
      doc.on('error', reject);

      // Header
      doc.fontSize(18).text('HST RETURN REMINDER', { align: 'center' });
      doc.fontSize(14).text(`Period: ${period}`, { align: 'center' });
      doc.moveDown();

      // Business Information
      doc.fontSize(14).text('BUSINESS INFORMATION', { underline: true });
      doc.fontSize(12)
         .text(`Business Name: ${this.dealerInfo.name}`)
         .text(`HST Registration: ${this.dealerInfo.hstNumber}`)
         .text(`Address: ${this.dealerInfo.address}`)
         .moveDown();

      // Summary
      doc.fontSize(14).text('SUMMARY', { underline: true });
      doc.fontSize(12)
         .text(`Total Sales: ${sales.length}`)
         .text(`Total HST Collected: $${totalHSTCollected.toLocaleString()}`)
         .text(`Due Date: ${dueDate}`)
         .moveDown();

      // Sales Details
      if (sales.length > 0) {
        doc.fontSize(14).text('SALES DETAILS', { underline: true });
        doc.fontSize(10);
        
        sales.forEach(sale => {
          doc.text(`${sale.date} - ${sale.vehicle} - Sale: $${sale.amount} - HST: $${sale.hst}`);
        });
      }

      doc.moveDown();

      // Reminder Notes
      doc.fontSize(14).text('REMINDER NOTES', { underline: true });
      doc.fontSize(12)
         .text('• File your HST return by the due date to avoid penalties')
         .text('• Keep all sales records and receipts')
         .text('• Consult with your accountant if needed')
         .text('• Use CRA My Business Account for online filing');

      doc.end();
    });
  }

  /**
   * Save PDF to file system
   * @param {Buffer} pdfBuffer - PDF buffer
   * @param {String} filename - Output filename
   * @param {String} directory - Output directory
   * @returns {Promise<String>} - File path
   */
  async savePDF(pdfBuffer, filename, directory = 'generated-forms') {
    try {
      // Ensure directory exists
      if (!fs.existsSync(directory)) {
        fs.mkdirSync(directory, { recursive: true });
      }

      const filePath = path.join(directory, filename);
      fs.writeFileSync(filePath, pdfBuffer);
      
      return filePath;
    } catch (error) {
      console.error('Failed to save PDF:', error);
      throw error;
    }
  }
}

export default PDFGenerator;
