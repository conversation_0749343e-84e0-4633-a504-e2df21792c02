'use client';
import { useLanguage } from '../../contexts/LanguageContext';
import styles from './AboutUs.module.css';

export default function AboutUs() {
  const { t } = useLanguage();

  return (
    <div className={styles.aboutPage}>
      <div className={styles.container}>
        <div className={styles.header}>
          <h1 className={styles.title}>
            About <span className={styles.brandName}>
              <span className={styles.brandFaze}>Faze</span>
              <span className={styles.brandN}>N</span>
              <span className={styles.brandAuto}>Auto</span>
            </span>
          </h1>
        </div>

        <div className={styles.content}>
          <div className={styles.aboutContent}>
            <div className={styles.aboutText}>
              <p className={styles.description}>
                At FazeNAuto, we're committed to providing quality pre-owned vehicles with complete transparency.
                Our pricing model means no surprises, no hidden fees, and honest deals for every customer.
              </p>
              <p className={styles.description}>
                We understand that buying a vehicle is a significant decision, and we're here to make that process as
                smooth and transparent as possible. Every vehicle in our inventory is carefully selected and priced
                fairly.
              </p>
              
              <div className={styles.features}>
                <div className={styles.feature}>
                  <div className={styles.featureIcon}>✓</div>
                  <span className={styles.featureText}>Fair Market Pricing</span>
                </div>
                <div className={styles.feature}>
                  <div className={styles.featureIcon}>✓</div>
                  <span className={styles.featureText}>Quality Pre-Owned Vehicles</span>
                </div>
                <div className={styles.feature}>
                  <div className={styles.featureIcon}>✓</div>
                  <span className={styles.featureText}>No Hidden Fees</span>
                </div>
                <div className={styles.feature}>
                  <div className={styles.featureIcon}>✓</div>
                  <span className={styles.featureText}>Customer-Focused Service</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
