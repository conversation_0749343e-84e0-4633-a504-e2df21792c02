import { decodeVIN } from '../utils/vinDecoder.js';

/**
 * VIN-to-Ad Generator with GPT-4 Integration
 * Generates market-ready listing copy for multiple platforms
 */
export class AdGenerator {
  constructor() {
    this.openaiApiKey = process.env.OPENAI_API_KEY;
    this.dealerInfo = {
      name: 'FazeNAuto',
      phone: '************',
      address: '1120 Meighen Way',
      website: 'fazenauto.com'
    };
  }

  /**
   * Generate complete ad package from VIN
   * @param {String} vin - Vehicle Identification Number
   * @param {Object} options - Generation options
   * @returns {Promise<Object>} - Complete ad package
   */
  async generateFromVIN(vin, options = {}) {
    const {
      platforms = ['facebook', 'kijiji', 'craigslist', 'autotrader'],
      includeEmojis = true,
      bilingual = false,
      tone = 'professional', // professional, casual, enthusiastic
      priceRange = null,
      additionalFeatures = []
    } = options;

    try {
      // Step 1: Decode VIN to get vehicle specs
      console.log('🔍 Decoding VIN:', vin);
      let vehicleSpecs;

      try {
        const vinResult = await decodeVIN(vin);
        if (vinResult.success) {
          vehicleSpecs = vinResult.data;
          console.log('✅ VIN decoded successfully:', vehicleSpecs);
        } else {
          throw new Error(vinResult.error || 'VIN decoding failed');
        }
      } catch (vinError) {
        console.warn('VIN decoding failed, using mock data:', vinError.message);
        // Use mock vehicle specs if VIN decoding fails
        vehicleSpecs = {
          make: 'Unknown',
          model: 'Vehicle',
          year: new Date().getFullYear(),
          engine: 'Standard Engine',
          transmission: 'Automatic',
          drivetrain: 'FWD',
          fuelType: 'Gasoline'
        };
      }

      // Step 2: Generate base ad content using GPT-4
      const baseContent = await this.generateBaseContent(vehicleSpecs, {
        tone,
        includeEmojis,
        additionalFeatures
      });

      // Step 3: Generate platform-specific versions
      const platformAds = {};
      for (const platform of platforms) {
        platformAds[platform] = await this.generatePlatformSpecificAd(
          baseContent,
          platform,
          vehicleSpecs,
          { includeEmojis, bilingual }
        );
      }

      // Step 4: Generate bilingual versions if requested
      if (bilingual) {
        for (const platform of platforms) {
          platformAds[`${platform}_french`] = await this.translateToFrench(
            platformAds[platform]
          );
        }
      }

      return {
        success: true,
        vin,
        vehicleSpecs,
        baseContent,
        platformAds,
        generatedAt: new Date().toISOString(),
        options
      };

    } catch (error) {
      console.error('❌ Ad generation failed:', error);
      return {
        success: false,
        error: error.message,
        vin
      };
    }
  }

  /**
   * Generate base ad content using GPT-4
   * @param {Object} vehicleSpecs - Decoded vehicle specifications
   * @param {Object} options - Generation options
   * @returns {Promise<Object>} - Base ad content
   */
  async generateBaseContent(vehicleSpecs, options = {}) {
    const { tone, includeEmojis, additionalFeatures } = options;

    const prompt = this.buildGPTPrompt(vehicleSpecs, {
      tone,
      includeEmojis,
      additionalFeatures
    });

    if (!this.openaiApiKey) {
      // Fallback to template-based generation if no GPT-4 API key
      console.warn('⚠️ No OpenAI API key found, using template-based generation');
      return this.generateTemplateBasedContent(vehicleSpecs, options);
    }

    try {
      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.openaiApiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: 'gpt-4',
          messages: [
            {
              role: 'system',
              content: 'You are an expert automotive copywriter specializing in creating compelling vehicle listings for Canadian car dealerships.'
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          max_tokens: 1000,
          temperature: 0.7
        })
      });

      if (!response.ok) {
        throw new Error(`OpenAI API error: ${response.status}`);
      }

      const data = await response.json();
      const generatedContent = data.choices[0].message.content;

      return this.parseGPTResponse(generatedContent, vehicleSpecs);

    } catch (error) {
      console.warn('⚠️ GPT-4 generation failed, falling back to templates:', error.message);
      return this.generateTemplateBasedContent(vehicleSpecs, options);
    }
  }

  /**
   * Build GPT-4 prompt for ad generation
   * @param {Object} vehicleSpecs - Vehicle specifications
   * @param {Object} options - Generation options
   * @returns {String} - GPT prompt
   */
  buildGPTPrompt(vehicleSpecs, options) {
    const { tone, includeEmojis, additionalFeatures } = options;
    
    return `
Create a compelling vehicle listing for a ${vehicleSpecs.year} ${vehicleSpecs.make} ${vehicleSpecs.model}.

Vehicle Details:
- Year: ${vehicleSpecs.year}
- Make: ${vehicleSpecs.make}
- Model: ${vehicleSpecs.model}
- Engine: ${vehicleSpecs.engine || 'Not specified'}
- Transmission: ${vehicleSpecs.transmission || 'Not specified'}
- Fuel Type: ${vehicleSpecs.fuelType || 'Not specified'}
- Drive Type: ${vehicleSpecs.driveType || 'Not specified'}
- Body Style: ${vehicleSpecs.bodyClass || 'Not specified'}
- Doors: ${vehicleSpecs.doors || 'Not specified'}

Additional Features: ${additionalFeatures.join(', ') || 'None specified'}

Dealer Information:
- Name: FazeNAuto
- Phone: ************
- Location: Ontario, Canada
- Selling "as-is" during first year of operations

Requirements:
- Tone: ${tone}
- Include emojis: ${includeEmojis ? 'Yes' : 'No'}
- Target Canadian market
- Emphasize value and reliability
- Include call-to-action
- Mention "as-is" sale clearly
- Keep under 500 words

Please provide the response in this JSON format:
{
  "title": "Catchy title for the listing",
  "description": "Full description with features and benefits",
  "bulletPoints": ["Key feature 1", "Key feature 2", "Key feature 3"],
  "callToAction": "Strong call-to-action text",
  "keywords": ["seo", "keywords", "for", "search"]
}
`;
  }

  /**
   * Parse GPT-4 response
   * @param {String} content - GPT response content
   * @param {Object} vehicleSpecs - Vehicle specifications
   * @returns {Object} - Parsed content
   */
  parseGPTResponse(content, vehicleSpecs) {
    try {
      // Try to parse as JSON first
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return {
          ...parsed,
          vehicleSpecs,
          generatedBy: 'gpt-4'
        };
      }
    } catch (error) {
      console.warn('Failed to parse GPT response as JSON, using text parsing');
    }

    // Fallback to text parsing
    return {
      title: this.extractTitle(content, vehicleSpecs),
      description: content,
      bulletPoints: this.extractBulletPoints(content),
      callToAction: 'Contact FazeNAuto at ************ for more information!',
      keywords: this.generateKeywords(vehicleSpecs),
      vehicleSpecs,
      generatedBy: 'gpt-4-text-parsed'
    };
  }

  /**
   * Generate template-based content (fallback)
   * @param {Object} vehicleSpecs - Vehicle specifications
   * @param {Object} options - Generation options
   * @returns {Object} - Template-based content
   */
  generateTemplateBasedContent(vehicleSpecs, options = {}) {
    const { includeEmojis } = options;
    const emoji = includeEmojis ? '🚗 ' : '';

    const title = `${emoji}${vehicleSpecs.year} ${vehicleSpecs.make} ${vehicleSpecs.model} - Great Value!`;
    
    const description = `
${emoji}Looking for a reliable ${vehicleSpecs.year} ${vehicleSpecs.make} ${vehicleSpecs.model}? Look no further!

This well-maintained vehicle features:
${vehicleSpecs.engine ? `• ${vehicleSpecs.engine} engine` : ''}
${vehicleSpecs.transmission ? `• ${vehicleSpecs.transmission} transmission` : ''}
${vehicleSpecs.fuelType ? `• ${vehicleSpecs.fuelType} fuel system` : ''}
${vehicleSpecs.driveType ? `• ${vehicleSpecs.driveType} drivetrain` : ''}

Perfect for daily commuting or weekend adventures. This vehicle is sold "as-is" and represents excellent value in today's market.

Located in Ontario. Serious inquiries only.
    `.trim();

    return {
      title,
      description,
      bulletPoints: [
        `${vehicleSpecs.year} ${vehicleSpecs.make} ${vehicleSpecs.model}`,
        vehicleSpecs.engine || 'Reliable engine',
        vehicleSpecs.transmission || 'Smooth transmission',
        'Sold as-is',
        'Great value'
      ],
      callToAction: `${includeEmojis ? '📞 ' : ''}Call FazeNAuto at ************ today!`,
      keywords: this.generateKeywords(vehicleSpecs),
      vehicleSpecs,
      generatedBy: 'template'
    };
  }

  /**
   * Generate platform-specific ad versions
   * @param {Object} baseContent - Base ad content
   * @param {String} platform - Target platform
   * @param {Object} vehicleSpecs - Vehicle specifications
   * @param {Object} options - Platform options
   * @returns {Object} - Platform-specific ad
   */
  async generatePlatformSpecificAd(baseContent, platform, vehicleSpecs, options = {}) {
    const platformSpecs = {
      facebook: {
        titleLimit: 100,
        descriptionLimit: 9000,
        emphasize: ['price', 'location', 'condition'],
        format: 'social'
      },
      kijiji: {
        titleLimit: 64,
        descriptionLimit: 4000,
        emphasize: ['features', 'value', 'contact'],
        format: 'classified'
      },
      craigslist: {
        titleLimit: 70,
        descriptionLimit: 8000,
        emphasize: ['specs', 'price', 'serious_buyers'],
        format: 'classified'
      },
      autotrader: {
        titleLimit: 80,
        descriptionLimit: 1000,
        emphasize: ['professional', 'specs', 'dealer_info'],
        format: 'professional'
      }
    };

    const spec = platformSpecs[platform] || platformSpecs.facebook;
    
    return {
      platform,
      title: this.truncateText(baseContent.title, spec.titleLimit),
      description: this.formatForPlatform(baseContent.description, spec),
      bulletPoints: baseContent.bulletPoints,
      callToAction: this.formatCallToAction(baseContent.callToAction, platform),
      keywords: baseContent.keywords,
      platformSpecs: spec,
      copyButtons: this.generateCopyButtons(platform)
    };
  }

  /**
   * Generate copy buttons for easy posting
   * @param {String} platform - Target platform
   * @returns {Object} - Copy button configurations
   */
  generateCopyButtons(platform) {
    return {
      title: {
        label: `Copy ${platform} Title`,
        action: 'copy-title'
      },
      description: {
        label: `Copy ${platform} Description`,
        action: 'copy-description'
      },
      full: {
        label: `Copy Full ${platform} Ad`,
        action: 'copy-full'
      }
    };
  }

  /**
   * Generate SEO keywords
   * @param {Object} vehicleSpecs - Vehicle specifications
   * @returns {Array} - SEO keywords
   */
  generateKeywords(vehicleSpecs) {
    const keywords = [
      vehicleSpecs.year?.toString(),
      vehicleSpecs.make,
      vehicleSpecs.model,
      'used car',
      'Ontario',
      'FazeNAuto',
      'as-is',
      'reliable'
    ];

    if (vehicleSpecs.fuelType) keywords.push(vehicleSpecs.fuelType);
    if (vehicleSpecs.transmission) keywords.push(vehicleSpecs.transmission);
    if (vehicleSpecs.bodyClass) keywords.push(vehicleSpecs.bodyClass);

    return keywords.filter(Boolean);
  }

  /**
   * Utility functions
   */
  truncateText(text, limit) {
    return text.length > limit ? text.substring(0, limit - 3) + '...' : text;
  }

  formatForPlatform(description, spec) {
    // Platform-specific formatting logic
    return this.truncateText(description, spec.descriptionLimit);
  }

  formatCallToAction(cta, platform) {
    const platformCTAs = {
      facebook: `${cta} Message us for quick response!`,
      kijiji: `${cta} Email or call for details.`,
      craigslist: `${cta} Serious inquiries only.`,
      autotrader: `${cta} Professional dealer service.`
    };

    return platformCTAs[platform] || cta;
  }

  extractTitle(content, vehicleSpecs) {
    // Extract title from content or generate default
    const lines = content.split('\n');
    const titleLine = lines.find(line => line.length > 10 && line.length < 100);
    return titleLine || `${vehicleSpecs.year} ${vehicleSpecs.make} ${vehicleSpecs.model}`;
  }

  extractBulletPoints(content) {
    const bullets = content.match(/[•\-\*]\s*(.+)/g);
    return bullets ? bullets.map(b => b.replace(/[•\-\*]\s*/, '')) : [];
  }

  async translateToFrench(adContent) {
    // Placeholder for French translation
    // In production, you'd use Google Translate API or similar
    return {
      ...adContent,
      title: `[FR] ${adContent.title}`,
      description: `[Version française] ${adContent.description}`,
      language: 'fr'
    };
  }
}

export default AdGenerator;
