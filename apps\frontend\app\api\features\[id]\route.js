import { NextResponse } from 'next/server';
import { connectToDatabase } from '../../../../lib/dbConnect';
import Feature from '../../../../models/Feature';

/**
 * PUT /api/features/[id] - Update a feature
 */
export async function PUT(request, { params }) {
  try {
    await connectToDatabase();
    
    const { id } = await params;
    const { category, value } = await request.json();
    
    // Validate required fields
    if (!category || !value) {
      return NextResponse.json({
        success: false,
        error: 'Category and value are required'
      }, { status: 400 });
    }
    
    // Validate category
    const validCategories = ['exterior', 'interior', 'mechanical', 'safety', 'entertainment'];
    if (!validCategories.includes(category)) {
      return NextResponse.json({
        success: false,
        error: 'Invalid category'
      }, { status: 400 });
    }
    
    // Update feature
    const feature = await Feature.findByIdAndUpdate(
      id,
      {
        category: category.toLowerCase(),
        value: value.trim()
      },
      { new: true, runValidators: true }
    );
    
    if (!feature) {
      return NextResponse.json({
        success: false,
        error: 'Feature not found'
      }, { status: 404 });
    }
    
    return NextResponse.json({
      success: true,
      message: 'Feature updated successfully',
      data: feature
    });
    
  } catch (error) {
    console.error('Error updating feature:', error);
    
    // Handle duplicate value error
    if (error.code === 11000) {
      return NextResponse.json({
        success: false,
        error: 'Feature value already exists'
      }, { status: 409 });
    }
    
    return NextResponse.json({
      success: false,
      error: 'Failed to update feature'
    }, { status: 500 });
  }
}

/**
 * DELETE /api/features/[id] - Delete a feature
 */
export async function DELETE(request, { params }) {
  try {
    await connectToDatabase();
    
    const { id } = await params;
    
    const feature = await Feature.findByIdAndDelete(id);
    
    if (!feature) {
      return NextResponse.json({
        success: false,
        error: 'Feature not found'
      }, { status: 404 });
    }
    
    return NextResponse.json({
      success: true,
      message: 'Feature deleted successfully'
    });
    
  } catch (error) {
    console.error('Error deleting feature:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to delete feature'
    }, { status: 500 });
  }
}
