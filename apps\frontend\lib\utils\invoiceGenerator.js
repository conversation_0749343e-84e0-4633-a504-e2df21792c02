import { connectToDatabase } from '../dbConnect.js';
import Counter from '../models/Counter.js';

/**
 * Generate unique invoice number for Bill of Sale
 * Format: BOS-YYYY-##### (e.g., BOS-2025-00001)
 * 
 * @param {string} mode - Either "test" or "prod"
 * @returns {Promise<string>} - Generated invoice number
 */
export async function generateInvoiceNumber(mode = 'test') {
  try {
    // Validate mode parameter
    if (!['test', 'prod'].includes(mode)) {
      throw new Error('Invalid mode. Must be "test" or "prod"');
    }

    // Connect to database
    await connectToDatabase();

    // Generate counter ID based on mode
    const counterId = `billOfSale_${mode}`;
    
    // Get current year
    const currentYear = new Date().getFullYear();
    
    // Get next sequence number
    const sequenceNumber = await Counter.getNextSequence(counterId);
    
    // Format sequence number with leading zeros (5 digits)
    const formattedSequence = sequenceNumber.toString().padStart(5, '0');
    
    // Generate invoice number
    const invoiceNumber = `BOS-${currentYear}-${formattedSequence}`;
    
    console.log(`📄 Generated invoice number: ${invoiceNumber} (mode: ${mode})`);
    
    return invoiceNumber;
    
  } catch (error) {
    console.error('❌ Error generating invoice number:', error);
    
    // Fallback to timestamp-based number if database fails
    const timestamp = Date.now().toString().slice(-5);
    const currentYear = new Date().getFullYear();
    const fallbackNumber = `BOS-${currentYear}-${timestamp}`;
    
    console.warn(`⚠️ Using fallback invoice number: ${fallbackNumber}`);
    return fallbackNumber;
  }
}

/**
 * Get current counter status for a specific mode
 * 
 * @param {string} mode - Either "test" or "prod"
 * @returns {Promise<Object>} - Counter information
 */
export async function getCounterStatus(mode = 'test') {
  try {
    // Validate mode parameter
    if (!['test', 'prod'].includes(mode)) {
      throw new Error('Invalid mode. Must be "test" or "prod"');
    }

    // Connect to database
    await connectToDatabase();

    // Generate counter ID based on mode
    const counterId = `billOfSale_${mode}`;
    const currentYear = new Date().getFullYear();
    
    // Find current counter
    const counter = await Counter.findOne({ 
      _id: counterId, 
      year: currentYear 
    });
    
    if (!counter) {
      return {
        mode,
        year: currentYear,
        currentSequence: 0,
        nextInvoiceNumber: `BOS-${currentYear}-00001`,
        exists: false
      };
    }
    
    const nextSequence = (counter.sequenceValue + 1).toString().padStart(5, '0');
    
    return {
      mode,
      year: counter.year,
      currentSequence: counter.sequenceValue,
      nextInvoiceNumber: `BOS-${counter.year}-${nextSequence}`,
      exists: true,
      lastUpdated: counter.updatedAt
    };
    
  } catch (error) {
    console.error('❌ Error getting counter status:', error);
    throw error;
  }
}

/**
 * Reset counter (admin function)
 *
 * @param {string} mode - Either "test" or "prod"
 * @returns {Promise<boolean>} - Success status
 */
export async function resetCounterForNewYear(mode = 'test') {
  try {
    // Validate mode parameter
    if (!['test', 'prod'].includes(mode)) {
      throw new Error('Invalid mode. Must be "test" or "prod"');
    }

    // Connect to database
    await connectToDatabase();

    // Generate counter ID based on mode
    const counterId = `billOfSale_${mode}`;
    const currentYear = new Date().getFullYear();

    // Delete existing counter and create fresh one
    await Counter.deleteOne({ _id: counterId });

    await Counter.create({
      _id: counterId,
      year: currentYear,
      sequenceValue: 0
    });

    console.log(`🔄 Counter reset: ${counterId} -> 0`);
    return true;

  } catch (error) {
    console.error('❌ Error resetting counter:', error);
    throw error;
  }
}

/**
 * Initialize counters for both test and prod modes
 * Called during application startup
 * 
 * @returns {Promise<void>}
 */
export async function initializeCounters() {
  try {
    await connectToDatabase();
    
    // Initialize both test and prod counters
    await Counter.resetForNewYear('billOfSale_test');
    await Counter.resetForNewYear('billOfSale_prod');
    
    console.log('✅ Invoice counters initialized');
    
  } catch (error) {
    console.error('❌ Error initializing counters:', error);
  }
}
