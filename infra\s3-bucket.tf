# FazeNAuto S3 Infrastructure Configuration
# This file contains Terraform configuration for S3 bucket setup

terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

provider "aws" {
  region = var.aws_region
}

variable "aws_region" {
  description = "AWS region for resources"
  type        = string
  default     = "us-east-1"
}

variable "bucket_name" {
  description = "S3 bucket name for vehicle images"
  type        = string
  default     = "fazenauto-vehicle-images"
}

variable "environment" {
  description = "Environment (dev, staging, prod)"
  type        = string
  default     = "prod"
}

# S3 Bucket for vehicle images
resource "aws_s3_bucket" "vehicle_images" {
  bucket = var.bucket_name

  tags = {
    Name        = "FazeNAuto Vehicle Images"
    Environment = var.environment
    Project     = "FazeNAuto"
  }
}

# S3 Bucket versioning
resource "aws_s3_bucket_versioning" "vehicle_images_versioning" {
  bucket = aws_s3_bucket.vehicle_images.id
  versioning_configuration {
    status = "Enabled"
  }
}

# S3 Bucket public access block
resource "aws_s3_bucket_public_access_block" "vehicle_images_pab" {
  bucket = aws_s3_bucket.vehicle_images.id

  block_public_acls       = false
  block_public_policy     = false
  ignore_public_acls      = false
  restrict_public_buckets = false
}

# S3 Bucket policy for public read access to images
resource "aws_s3_bucket_policy" "vehicle_images_policy" {
  bucket = aws_s3_bucket.vehicle_images.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid       = "PublicReadGetObject"
        Effect    = "Allow"
        Principal = "*"
        Action    = "s3:GetObject"
        Resource  = "${aws_s3_bucket.vehicle_images.arn}/*"
      }
    ]
  })

  depends_on = [aws_s3_bucket_public_access_block.vehicle_images_pab]
}

# S3 Bucket CORS configuration
resource "aws_s3_bucket_cors_configuration" "vehicle_images_cors" {
  bucket = aws_s3_bucket.vehicle_images.id

  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = ["GET", "PUT", "POST", "DELETE", "HEAD"]
    allowed_origins = ["https://fazenauto.com", "https://*.fazenauto.com", "http://localhost:3000"]
    expose_headers  = ["ETag"]
    max_age_seconds = 3000
  }
}

# Outputs
output "bucket_name" {
  description = "Name of the S3 bucket"
  value       = aws_s3_bucket.vehicle_images.bucket
}

output "bucket_arn" {
  description = "ARN of the S3 bucket"
  value       = aws_s3_bucket.vehicle_images.arn
}

output "bucket_domain_name" {
  description = "Domain name of the S3 bucket"
  value       = aws_s3_bucket.vehicle_images.bucket_domain_name
}

output "bucket_regional_domain_name" {
  description = "Regional domain name of the S3 bucket"
  value       = aws_s3_bucket.vehicle_images.bucket_regional_domain_name
}
