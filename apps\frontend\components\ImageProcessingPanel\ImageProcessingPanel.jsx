'use client';

import { useState, useRef } from 'react';
import styles from './ImageProcessingPanel.module.css';

export default function ImageProcessingPanel({ vehicleId, onProcessingComplete }) {
  const [processing, setProcessing] = useState(false);
  const [results, setResults] = useState(null);
  const [selectedPlatforms, setSelectedPlatforms] = useState(['standard']);
  const [options, setOptions] = useState({
    addWatermark: true,
    enhanceLighting: true,
    blurLicensePlates: false // Placeholder for future ML implementation
  });
  const fileInputRef = useRef(null);

  const platforms = [
    { id: 'standard', name: 'Standard (1200x900)', description: 'General purpose high quality' },
    { id: 'facebook', name: 'Facebook Marketplace', description: '1200x900, optimized for social' },
    { id: 'autotrader', name: 'AutoTrader', description: '1024x768, professional quality' },
    { id: 'cargurus', name: '<PERSON><PERSON><PERSON>', description: '1200x800, marketplace optimized' },
    { id: 'kijiji', name: '<PERSON><PERSON><PERSON>', description: '1000x750, classified ads' }
  ];

  const handleFileSelect = (event) => {
    const files = Array.from(event.target.files);
    if (files.length > 50) {
      alert('Maximum 50 images allowed');
      return;
    }
    processImages(files);
  };

  const processImages = async (files) => {
    if (!vehicleId) {
      alert('Vehicle ID is required');
      return;
    }

    setProcessing(true);
    setResults(null);

    try {
      const formData = new FormData();
      formData.append('vehicleId', vehicleId);
      formData.append('platforms', selectedPlatforms.join(','));
      formData.append('addWatermark', options.addWatermark.toString());
      formData.append('enhanceLighting', options.enhanceLighting.toString());
      formData.append('blurLicensePlates', options.blurLicensePlates.toString());

      files.forEach((file, index) => {
        formData.append(`image_${index}`, file);
      });

      const response = await fetch('/api/image-processing', {
        method: 'POST',
        body: formData
      });

      const data = await response.json();
      setResults(data);

      if (data.success && onProcessingComplete) {
        onProcessingComplete(data);
      }

    } catch (error) {
      console.error('Image processing failed:', error);
      setResults({
        success: false,
        error: error.message
      });
    } finally {
      setProcessing(false);
    }
  };

  const handlePlatformToggle = (platformId) => {
    setSelectedPlatforms(prev => 
      prev.includes(platformId)
        ? prev.filter(id => id !== platformId)
        : [...prev, platformId]
    );
  };

  const handleOptionChange = (option) => {
    setOptions(prev => ({
      ...prev,
      [option]: !prev[option]
    }));
  };

  return (
    <div className={styles.imageProcessingPanel}>
      <div className={styles.header}>
        <h3>🖼️ Image Processing Pipeline</h3>
        <p>Batch process vehicle photos for multiple platforms</p>
      </div>

      {/* Platform Selection */}
      <div className={styles.section}>
        <h4>Target Platforms</h4>
        <div className={styles.platformGrid}>
          {platforms.map(platform => (
            <label key={platform.id} className={styles.platformOption}>
              <input
                type="checkbox"
                checked={selectedPlatforms.includes(platform.id)}
                onChange={() => handlePlatformToggle(platform.id)}
                disabled={processing}
              />
              <div className={styles.platformInfo}>
                <span className={styles.platformName}>{platform.name}</span>
                <span className={styles.platformDesc}>{platform.description}</span>
              </div>
            </label>
          ))}
        </div>
      </div>

      {/* Processing Options */}
      <div className={styles.section}>
        <h4>Processing Options</h4>
        <div className={styles.optionsGrid}>
          <label className={styles.option}>
            <input
              type="checkbox"
              checked={options.addWatermark}
              onChange={() => handleOptionChange('addWatermark')}
              disabled={processing}
            />
            <span>Add FazeNAuto Watermark</span>
          </label>
          
          <label className={styles.option}>
            <input
              type="checkbox"
              checked={options.enhanceLighting}
              onChange={() => handleOptionChange('enhanceLighting')}
              disabled={processing}
            />
            <span>Auto-enhance Lighting</span>
          </label>
          
          <label className={styles.option}>
            <input
              type="checkbox"
              checked={options.blurLicensePlates}
              onChange={() => handleOptionChange('blurLicensePlates')}
              disabled={processing}
            />
            <span>Blur License Plates (Coming Soon)</span>
          </label>
        </div>
      </div>

      {/* File Upload */}
      <div className={styles.section}>
        <h4>Upload Images</h4>
        <div className={styles.uploadArea}>
          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept="image/*"
            onChange={handleFileSelect}
            disabled={processing}
            className={styles.fileInput}
          />
          <button
            onClick={() => fileInputRef.current?.click()}
            disabled={processing || selectedPlatforms.length === 0}
            className={styles.uploadButton}
          >
            {processing ? '🔄 Processing...' : '📁 Select Images (Max 50)'}
          </button>
        </div>
        <p className={styles.uploadHint}>
          Supported formats: JPG, PNG, WebP. Max 50 images per batch.
        </p>
      </div>

      {/* Results */}
      {results && (
        <div className={styles.results}>
          <h4>Processing Results</h4>
          {results.success ? (
            <div className={styles.successResults}>
              <div className={styles.summary}>
                <span className={styles.success}>
                  ✅ {results.totalProcessed} images processed successfully
                </span>
                {results.totalErrors > 0 && (
                  <span className={styles.warning}>
                    ⚠️ {results.totalErrors} images had errors
                  </span>
                )}
              </div>
              
              <div className={styles.processedImages}>
                {results.processedImages.map((image, index) => (
                  <div key={index} className={styles.imageResult}>
                    <span className={styles.imageIndex}>Image {image.originalIndex + 1}</span>
                    <div className={styles.platformResults}>
                      {Object.entries(image.platforms).map(([platform, url]) => (
                        <div key={platform} className={styles.platformResult}>
                          <span className={styles.platformName}>{platform}:</span>
                          <a 
                            href={url} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className={styles.imageLink}
                          >
                            View Processed Image
                          </a>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>

              {results.errors.length > 0 && (
                <div className={styles.errors}>
                  <h5>Errors:</h5>
                  {results.errors.map((error, index) => (
                    <div key={index} className={styles.error}>
                      Image {error.imageIndex + 1}: {error.error}
                    </div>
                  ))}
                </div>
              )}
            </div>
          ) : (
            <div className={styles.errorResults}>
              <span className={styles.error}>❌ {results.error}</span>
            </div>
          )}
        </div>
      )}

      {/* Mobile Optimization Note */}
      <div className={styles.mobileNote}>
        <h4>📱 Mobile Tip</h4>
        <p>
          On mobile devices, you can take photos directly or select from your gallery. 
          The processing pipeline will automatically optimize images for all selected platforms.
        </p>
      </div>
    </div>
  );
}
