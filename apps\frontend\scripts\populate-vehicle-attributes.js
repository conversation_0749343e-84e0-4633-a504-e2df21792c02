/**
 * <PERSON><PERSON><PERSON> to populate the vehicle_attributes collection in MongoDB
 * Run this script to seed the database with vehicle attribute options
 */

import mongoose from 'mongoose';
import VehicleAttributes from '../lib/models/VehicleAttributes.js';
import { getFallbackAttributes } from '../lib/getVehicleAttributes.js';

// Load environment variables
import dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });

// MongoDB connection string - update this to match your environment
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/fazenauto';

async function populateVehicleAttributes() {
  try {
    // Connect to MongoDB
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    // Get the fallback attributes data
    const attributesData = getFallbackAttributes();

    // Clear existing data
    await VehicleAttributes.deleteMany({});
    console.log('Cleared existing vehicle attributes');

    // Insert new data
    const documents = [];
    for (const [type, values] of Object.entries(attributesData)) {
      // Handle both string arrays and object arrays
      let processedValues;
      if (Array.isArray(values) && values.length > 0) {
        if (typeof values[0] === 'object' && values[0].value && values[0].display) {
          // For doors and cylinders with value/display objects, use the display text
          processedValues = values.map(item => item.display);
        } else if (typeof values[0] === 'object' && values[0].value && values[0].display) {
          // For drivetrain with value/display objects, use the display text
          processedValues = values.map(item => item.display);
        } else {
          // For simple string arrays
          processedValues = values;
        }
      } else {
        processedValues = values;
      }

      documents.push({
        type: type,
        values: processedValues
      });
    }

    await VehicleAttributes.insertMany(documents);
    console.log('Successfully populated vehicle attributes:');
    
    // Display what was inserted
    for (const doc of documents) {
      console.log(`- ${doc.type}: ${doc.values.length} options`);
    }

  } catch (error) {
    console.error('Error populating vehicle attributes:', error);
  } finally {
    // Close the connection
    await mongoose.connection.close();
    console.log('Database connection closed');
  }
}

// Run the script
populateVehicleAttributes();
