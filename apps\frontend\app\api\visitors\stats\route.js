import { NextResponse } from 'next/server';
import { connectToDatabase } from '../../../../lib/dbConnect';
import mongoose from 'mongoose';

// Get visitor collection
const getVisitorCollection = async () => {
  await connectToDatabase();
  const db = mongoose.connection.db;
  return db.collection('visitors');
};

// GET - Fetch visitor statistics
export async function GET() {
  try {
    const visitors = await getVisitorCollection();
    const now = new Date();
    
    // Calculate date ranges
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const thisWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    // Aggregate statistics
    const stats = await visitors.aggregate([
      {
        $facet: {
          totalVisits: [
            { $group: { _id: null, total: { $sum: '$visitCount' } } }
          ],
          uniqueVisitors: [
            { $group: { _id: null, count: { $sum: 1 } } }
          ],
          todayVisitors: [
            { $match: { lastVisit: { $gte: today } } },
            { $group: { _id: null, count: { $sum: 1 } } }
          ],
          thisWeekVisitors: [
            { $match: { lastVisit: { $gte: thisWeek } } },
            { $group: { _id: null, count: { $sum: 1 } } }
          ]
        }
      }
    ]).toArray();
    
    const result = stats[0];
    
    const statsData = {
      totalVisitors: result.totalVisits[0]?.total || 0,
      uniqueVisitors: result.uniqueVisitors[0]?.count || 0,
      todayVisitors: result.todayVisitors[0]?.count || 0,
      thisWeekVisitors: result.thisWeekVisitors[0]?.count || 0
    };
    
    return NextResponse.json({
      success: true,
      data: statsData
    });
    
  } catch (error) {
    console.error('Error fetching visitor stats:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch visitor statistics' },
      { status: 500 }
    );
  }
}
