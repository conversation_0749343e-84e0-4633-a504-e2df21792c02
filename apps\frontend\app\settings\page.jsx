'use client';

import Settings from '../../components/Settings/Settings';
import { trackPageView } from '../../lib/analytics';
import { useEffect } from 'react';

export default function SettingsPage() {
  useEffect(() => {
    // Track page view for analytics
    trackPageView(window.location.href, 'Settings Page');
  }, []);

  return (
    <div style={{ 
      minHeight: '100vh', 
      background: 'var(--bg-primary)',
      paddingTop: '2rem',
      paddingBottom: '2rem'
    }}>
      <Settings />
    </div>
  );
}
