// Sample script to populate features - run this once to add sample features
// You can run this by creating an API endpoint or running it directly

const sampleFeatures = [
  // Exterior Features
  { category: 'exterior', value: 'Alloy Wheels' },
  { category: 'exterior', value: 'Sunroof' },
  { category: 'exterior', value: 'LED Headlights' },
  { category: 'exterior', value: 'Fog Lights' },
  { category: 'exterior', value: 'Roof Rails' },
  { category: 'exterior', value: 'Tinted Windows' },
  { category: 'exterior', value: 'Chrome Trim' },
  { category: 'exterior', value: 'Running Boards' },
  { category: 'exterior', value: 'Heated Mirrors' },
  { category: 'exterior', value: 'Power Mirrors' },

  // Interior Features
  { category: 'interior', value: 'Leather Seats' },
  { category: 'interior', value: 'Heated Seats' },
  { category: 'interior', value: 'Ventilated Seats' },
  { category: 'interior', value: 'Power Seats' },
  { category: 'interior', value: 'Memory Seats' },
  { category: 'interior', value: 'Dual Zone Climate Control' },
  { category: 'interior', value: 'Rear Air Conditioning' },
  { category: 'interior', value: 'Premium Sound System' },
  { category: 'interior', value: 'Navigation System' },
  { category: 'interior', value: 'Backup Camera' },
  { category: 'interior', value: '360 Degree Camera' },
  { category: 'interior', value: 'Wireless Charging' },
  { category: 'interior', value: 'USB Ports' },
  { category: 'interior', value: 'Cup Holders' },
  { category: 'interior', value: 'Storage Compartments' },

  // Mechanical Features
  { category: 'mechanical', value: 'All-Wheel Drive' },
  { category: 'mechanical', value: 'Four-Wheel Drive' },
  { category: 'mechanical', value: 'Turbo Engine' },
  { category: 'mechanical', value: 'Hybrid System' },
  { category: 'mechanical', value: 'Manual Transmission' },
  { category: 'mechanical', value: 'Automatic Transmission' },
  { category: 'mechanical', value: 'CVT Transmission' },
  { category: 'mechanical', value: 'Sport Mode' },
  { category: 'mechanical', value: 'Eco Mode' },
  { category: 'mechanical', value: 'Tow Package' },
  { category: 'mechanical', value: 'Limited Slip Differential' },
  { category: 'mechanical', value: 'Performance Suspension' },

  // Safety Features
  { category: 'safety', value: 'Anti-lock Brakes (ABS)' },
  { category: 'safety', value: 'Electronic Stability Control' },
  { category: 'safety', value: 'Traction Control' },
  { category: 'safety', value: 'Airbags' },
  { category: 'safety', value: 'Side Airbags' },
  { category: 'safety', value: 'Curtain Airbags' },
  { category: 'safety', value: 'Blind Spot Monitoring' },
  { category: 'safety', value: 'Lane Departure Warning' },
  { category: 'safety', value: 'Forward Collision Warning' },
  { category: 'safety', value: 'Automatic Emergency Braking' },
  { category: 'safety', value: 'Adaptive Cruise Control' },
  { category: 'safety', value: 'Parking Sensors' },
  { category: 'safety', value: 'Tire Pressure Monitoring' },
  { category: 'safety', value: 'Child Safety Locks' },

  // Entertainment Features
  { category: 'entertainment', value: 'Bluetooth Connectivity' },
  { category: 'entertainment', value: 'Apple CarPlay' },
  { category: 'entertainment', value: 'Android Auto' },
  { category: 'entertainment', value: 'Satellite Radio' },
  { category: 'entertainment', value: 'AM/FM Radio' },
  { category: 'entertainment', value: 'CD Player' },
  { category: 'entertainment', value: 'DVD Player' },
  { category: 'entertainment', value: 'Rear Entertainment System' },
  { category: 'entertainment', value: 'WiFi Hotspot' },
  { category: 'entertainment', value: 'Voice Control' },
  { category: 'entertainment', value: 'Steering Wheel Controls' },
  { category: 'entertainment', value: 'Touchscreen Display' },
];

// Function to populate features (you can call this from an API endpoint)
export async function populateFeatures() {
  try {
    const results = [];
    
    for (const feature of sampleFeatures) {
      const response = await fetch('/api/features', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(feature)
      });
      
      const result = await response.json();
      results.push(result);
    }
    
    console.log('Features populated successfully:', results);
    return results;
  } catch (error) {
    console.error('Error populating features:', error);
    throw error;
  }
}

// If running this script directly (not recommended for production)
if (typeof window !== 'undefined') {
  console.log('Sample features data:', sampleFeatures);
  console.log('To populate features, call populateFeatures() function');
}
