'use client';

import { useCallback } from 'react';
import {
  trackVehicleView,
  trackVehicleInterest,
  trackContactForm,
  trackTestDrive,
  trackSearch,
  trackContactClick,
  trackVehicleEngagement,
  trackFinancingInterest,
  trackInventoryBrowsing,
  trackSocialShare,
  trackEvent,
  gtag
} from '../lib/analytics';

/**
 * Custom hook for analytics tracking throughout the application
 * Provides SSR-safe analytics functions with consistent vehicle data handling
 */
export function useAnalytics() {
  // Vehicle-related tracking
  const trackVehiclePageView = useCallback((vehicleData) => {
    if (typeof window === 'undefined') return;
    trackVehicleView(vehicleData);
  }, []);

  const trackVehicleContact = useCallback((formType, vehicleData) => {
    if (typeof window === 'undefined') return;
    trackContactForm(formType, vehicleData.id, vehicleData);
  }, []);

  const trackVehicleTestDrive = useCallback((vehicleData) => {
    if (typeof window === 'undefined') return;
    trackTestDrive(vehicleData.id, vehicleData.make, vehicleData.model, vehicleData);
  }, []);

  const trackVehicleFinancing = useCallback((vehicleData, financingType = 'general') => {
    if (typeof window === 'undefined') return;
    trackFinancingInterest(vehicleData, financingType);
  }, []);

  // Search and browsing tracking
  const trackVehicleSearch = useCallback((searchTerm, filters = {}, resultsCount = null) => {
    if (typeof window === 'undefined') return;
    trackSearch(searchTerm, filters, resultsCount);
  }, []);

  const trackInventoryFilter = useCallback((filters, resultsCount = null) => {
    if (typeof window === 'undefined') return;
    trackInventoryBrowsing('filter_applied', filters, resultsCount);
  }, []);

  // Contact interaction tracking
  const trackPhoneClick = useCallback((location, vehicleData = null) => {
    if (typeof window === 'undefined') return;
    trackContactClick('phone', location, vehicleData?.id, vehicleData);
  }, []);

  const trackWhatsAppClick = useCallback((location, vehicleData = null) => {
    if (typeof window === 'undefined') return;
    trackContactClick('whatsapp', location, vehicleData?.id, vehicleData);
  }, []);

  const trackEmailClick = useCallback((location, vehicleData = null) => {
    if (typeof window === 'undefined') return;
    trackContactClick('email', location, vehicleData?.id, vehicleData);
  }, []);

  const trackMessengerClick = useCallback((location, vehicleData = null) => {
    if (typeof window === 'undefined') return;
    trackContactClick('messenger', location, vehicleData?.id, vehicleData);
  }, []);

  // Vehicle engagement tracking
  const trackImageView = useCallback((vehicleData, imageIndex = null) => {
    if (typeof window === 'undefined') return;
    trackVehicleEngagement('image_view', vehicleData, { image_index: imageIndex });
  }, []);

  const trackVideoPlay = useCallback((vehicleData) => {
    if (typeof window === 'undefined') return;
    trackVehicleEngagement('video_play', vehicleData);
  }, []);

  const trackFeatureExpand = useCallback((vehicleData, featureCategory) => {
    if (typeof window === 'undefined') return;
    trackVehicleEngagement('feature_expand', vehicleData, { feature_category: featureCategory });
  }, []);

  // Social sharing tracking
  const trackVehicleShare = useCallback((platform, vehicleData) => {
    if (typeof window === 'undefined') return;
    trackSocialShare(platform, vehicleData, 'vehicle');
  }, []);

  // Form tracking
  const trackGeneralContactForm = useCallback((formType, success = true) => {
    if (typeof window === 'undefined') return;
    trackContactForm(formType, null, null);
  }, []);

  // Custom event tracking
  const trackCustomEvent = useCallback((eventName, parameters = {}) => {
    if (typeof window === 'undefined') return;
    trackEvent(eventName, parameters);
  }, []);

  // Direct gtag access for advanced use cases
  const trackAdvanced = useCallback((...args) => {
    if (typeof window === 'undefined') return;
    gtag(...args);
  }, []);

  return {
    // Vehicle tracking
    trackVehiclePageView,
    trackVehicleContact,
    trackVehicleTestDrive,
    trackVehicleFinancing,
    
    // Search and browsing
    trackVehicleSearch,
    trackInventoryFilter,
    
    // Contact methods
    trackPhoneClick,
    trackWhatsAppClick,
    trackEmailClick,
    trackMessengerClick,
    
    // Engagement
    trackImageView,
    trackVideoPlay,
    trackFeatureExpand,
    trackVehicleShare,
    
    // Forms
    trackGeneralContactForm,
    
    // Advanced
    trackCustomEvent,
    trackAdvanced
  };
}

export default useAnalytics;
