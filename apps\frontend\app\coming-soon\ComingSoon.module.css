/* Coming Soon Page Styles */

.comingSoonPage {
  min-height: 100vh;
  background: linear-gradient(135deg, #0f1419 0%, #1a202c 50%, #2d3748 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem 1rem;
  position: relative;
  overflow: hidden;
}

.container {
  max-width: 800px;
  width: 100%;
  position: relative;
  z-index: 2;
}

.content {
  background: var(--bg-primary, rgba(255, 255, 255, 0.95));
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 3rem 2rem;
  text-align: center;
  box-shadow: var(--card-shadow-hover, 0 20px 40px rgba(0, 0, 0, 0.1));
  border: 1px solid var(--border-primary, rgba(255, 255, 255, 0.2));
}

/* Main Icon */
.mainIcon {
  font-size: 6rem;
  margin-bottom: 1.5rem;
  animation: bounce 2s ease-in-out infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-20px);
  }
  60% {
    transform: translateY(-10px);
  }
}

/* Typography */
.title {
  font-size: 3rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 0.5rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.subtitle {
  font-size: 2rem;
  font-weight: 600;
  color: #667eea;
  margin-bottom: 1.5rem;
  text-transform: uppercase;
  letter-spacing: 2px;
}

.description {
  font-size: 1.2rem;
  color: #4a5568;
  line-height: 1.6;
  margin-bottom: 2.5rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* Features Section */
.featuresSection {
  margin-bottom: 2.5rem;
}

.featuresTitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 1.5rem;
}

.featuresList {
  list-style: none;
  padding: 0;
  margin: 0;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  max-width: 600px;
  margin: 0 auto;
}

.featureItem {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  background: rgba(15, 20, 25, 0.1);
  border-radius: 8px;
  font-weight: 500;
  color: #2d3748;
  transition: all 0.3s ease;
}

.featureItem:hover {
  background: rgba(15, 20, 25, 0.2);
  transform: translateX(5px);
}

/* CTA Section */
.ctaSection {
  margin-bottom: 2rem;
}

.ctaText {
  font-size: 1.1rem;
  color: #4a5568;
  margin-bottom: 1.5rem;
}

.buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.primaryBtn {
  background: #48bb78;
  color: white;
  padding: 1rem 2rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(72, 187, 120, 0.3);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.primaryBtn:hover {
  background: #38a169;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(72, 187, 120, 0.4);
}

.secondaryBtn {
  background: transparent;
  color: #667eea;
  padding: 1rem 2rem;
  border: 2px solid #667eea;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.secondaryBtn:hover {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
}

/* Contact Section */
.contactSection {
  border-top: 1px solid rgba(102, 126, 234, 0.2);
  padding-top: 1.5rem;
}

.contactText {
  font-size: 1rem;
  color: #4a5568;
  margin-bottom: 1rem;
}

.contactInfo {
  display: flex;
  gap: 2rem;
  justify-content: center;
  flex-wrap: wrap;
}

.contactItem {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #667eea;
  font-weight: 500;
  font-size: 1rem;
}

/* Background Animation */
.backgroundAnimation {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.floatingIcon {
  position: absolute;
  font-size: 2rem;
  opacity: 0.1;
  animation: float 6s ease-in-out infinite;
}

.floatingIcon:nth-child(1) {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.floatingIcon:nth-child(2) {
  top: 60%;
  right: 15%;
  animation-delay: 1.5s;
}

.floatingIcon:nth-child(3) {
  bottom: 30%;
  left: 20%;
  animation-delay: 3s;
}

.floatingIcon:nth-child(4) {
  top: 40%;
  right: 30%;
  animation-delay: 4.5s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-20px) rotate(90deg);
  }
  50% {
    transform: translateY(-40px) rotate(180deg);
  }
  75% {
    transform: translateY(-20px) rotate(270deg);
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .content {
    padding: 2rem 1.5rem;
  }
  
  .title {
    font-size: 2.5rem;
  }
  
  .subtitle {
    font-size: 1.5rem;
  }
  
  .mainIcon {
    font-size: 4rem;
  }
  
  .featuresList {
    grid-template-columns: 1fr;
  }
  
  .buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .primaryBtn,
  .secondaryBtn {
    width: 100%;
    max-width: 300px;
  }
  
  .contactInfo {
    flex-direction: column;
    gap: 1rem;
  }
}
