'use client';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import styles from './transactionDetail.module.css';

export default function TransactionDetailPage({ params }) {
  const router = useRouter();
  const [transaction, setTransaction] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [deleteLoading, setDeleteLoading] = useState(false);

  useEffect(() => {
    fetchTransaction();
  }, [params.id]);

  const fetchTransaction = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/ledger/${params.id}`);
      const data = await response.json();

      if (data.success) {
        setTransaction(data.data);
      } else {
        setError(data.error);
      }
    } catch (error) {
      console.error('Error fetching transaction:', error);
      setError('Failed to fetch transaction details');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this transaction? This action cannot be undone.')) {
      return;
    }

    try {
      setDeleteLoading(true);
      const response = await fetch(`/api/ledger/${params.id}`, {
        method: 'DELETE'
      });

      const data = await response.json();

      if (data.success) {
        router.push('/admin/ledger');
      } else {
        setError(data.error);
      }
    } catch (error) {
      console.error('Error deleting transaction:', error);
      setError('Failed to delete transaction');
    } finally {
      setDeleteLoading(false);
    }
  };

  const formatCurrency = (amount, currency = 'CAD') => {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: currency
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-CA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const downloadReceipt = (receiptData) => {
    if (receiptData.signedUrl) {
      window.open(receiptData.signedUrl, '_blank');
    } else {
      alert(receiptData.error || 'Unable to download receipt');
    }
  };

  if (loading) {
    return (
      <div className={styles.container}>
        <div className={styles.loading}>
          <div className={styles.spinner}></div>
          <p>Loading transaction details...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.container}>
        <div className={styles.error}>
          <h2>❌ Error</h2>
          <p>{error}</p>
          <Link href="/admin/ledger" className={styles.backButton}>
            ← Back to Ledger
          </Link>
        </div>
      </div>
    );
  }

  if (!transaction) {
    return (
      <div className={styles.container}>
        <div className={styles.notFound}>
          <h2>📝 Transaction Not Found</h2>
          <p>The requested transaction could not be found.</p>
          <Link href="/admin/ledger" className={styles.backButton}>
            ← Back to Ledger
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.titleSection}>
          <h1>📋 Transaction Details</h1>
          <p>View and manage transaction information</p>
        </div>
        
        <div className={styles.actions}>
          <Link href="/admin/ledger" className={styles.backButton}>
            ← Back to Ledger
          </Link>
          <button
            onClick={handleDelete}
            disabled={deleteLoading}
            className={styles.deleteButton}
          >
            {deleteLoading ? '🗑️ Deleting...' : '🗑️ Delete'}
          </button>
        </div>
      </div>

      <div className={styles.content}>
        {/* Transaction Info Card */}
        <div className={styles.card}>
          <div className={styles.cardHeader}>
            <h2>💰 Transaction Information</h2>
            <span className={`${styles.typeTag} ${styles[transaction.category]}`}>
              {transaction.type}
            </span>
          </div>
          
          <div className={styles.cardContent}>
            <div className={styles.infoGrid}>
              <div className={styles.infoItem}>
                <label>Amount</label>
                <span className={transaction.isIncome ? styles.incomeAmount : styles.expenseAmount}>
                  {transaction.isIncome ? '+' : '-'}{formatCurrency(transaction.amount, transaction.currency)}
                </span>
              </div>
              
              <div className={styles.infoItem}>
                <label>Date</label>
                <span>{formatDate(transaction.date)}</span>
              </div>
              
              <div className={styles.infoItem}>
                <label>Added By</label>
                <span>{transaction.addedBy}</span>
              </div>
              
              <div className={styles.infoItem}>
                <label>Category</label>
                <span className={styles.category}>{transaction.category.replace('-', ' ')}</span>
              </div>
            </div>
            
            <div className={styles.description}>
              <label>Description</label>
              <p>{transaction.description}</p>
            </div>
            
            {transaction.notes && (
              <div className={styles.notes}>
                <label>Notes</label>
                <p>{transaction.notes}</p>
              </div>
            )}
          </div>
        </div>

        {/* Vehicle Info Card */}
        {transaction.vehicleId && (
          <div className={styles.card}>
            <div className={styles.cardHeader}>
              <h2>🚗 Vehicle Information</h2>
            </div>
            
            <div className={styles.cardContent}>
              <div className={styles.vehicleInfo}>
                <div className={styles.vehicleDetails}>
                  <h3>{transaction.vehicleId.displayName || `${transaction.vehicleId.year} ${transaction.vehicleId.make} ${transaction.vehicleId.model}`}</h3>
                  <p><strong>VIN:</strong> {transaction.vehicleId.vin}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Receipts Card */}
        {transaction.signedReceiptUrls && transaction.signedReceiptUrls.length > 0 && (
          <div className={styles.card}>
            <div className={styles.cardHeader}>
              <h2>📎 Receipt Files ({transaction.signedReceiptUrls.length})</h2>
            </div>
            
            <div className={styles.cardContent}>
              <div className={styles.receiptsList}>
                {transaction.signedReceiptUrls.map((receipt, index) => (
                  <div key={index} className={styles.receiptItem}>
                    <div className={styles.receiptInfo}>
                      <span className={styles.receiptName}>{receipt.fileName}</span>
                      {receipt.error && (
                        <span className={styles.receiptError}>{receipt.error}</span>
                      )}
                    </div>
                    
                    <button
                      onClick={() => downloadReceipt(receipt)}
                      disabled={!receipt.signedUrl}
                      className={styles.downloadButton}
                    >
                      {receipt.signedUrl ? '📥 Download' : '❌ Unavailable'}
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Metadata Card */}
        <div className={styles.card}>
          <div className={styles.cardHeader}>
            <h2>ℹ️ Metadata</h2>
          </div>
          
          <div className={styles.cardContent}>
            <div className={styles.metadataGrid}>
              <div className={styles.metadataItem}>
                <label>Transaction ID</label>
                <span className={styles.transactionId}>{transaction._id}</span>
              </div>
              
              <div className={styles.metadataItem}>
                <label>Fiscal Year</label>
                <span>{transaction.fiscalYear}</span>
              </div>
              
              <div className={styles.metadataItem}>
                <label>Quarter</label>
                <span>Q{transaction.quarter}</span>
              </div>
              
              <div className={styles.metadataItem}>
                <label>Created</label>
                <span>{formatDate(transaction.createdAt)}</span>
              </div>
              
              <div className={styles.metadataItem}>
                <label>Last Updated</label>
                <span>{formatDate(transaction.updatedAt)}</span>
              </div>
              
              {transaction.s3Path && (
                <div className={styles.metadataItem}>
                  <label>S3 Path</label>
                  <span className={styles.s3Path}>{transaction.s3Path}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
