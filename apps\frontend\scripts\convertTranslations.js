#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Import translations from the frontend app
const translationsPath = path.resolve(__dirname, '../lib/translations.js');

async function loadTranslations() {
  try {
    // Convert to file URL for Windows compatibility
    const fileUrl = `file://${translationsPath.replace(/\\/g, '/')}`;
    const module = await import(fileUrl);
    return module.translations;
  } catch (error) {
    console.error('❌ Error loading translations:', error.message);
    process.exit(1);
  }
}

async function convertTranslations() {
  console.log('🔄 Converting translations to individual JSON files...\n');

  const translations = await loadTranslations();
  const baseDir = path.resolve(__dirname, '../locales');

  // Create base locales directory if it doesn't exist
  if (!fs.existsSync(baseDir)) {
    fs.mkdirSync(baseDir, { recursive: true });
    console.log(`📁 Created directory: ${baseDir}`);
  }

  // Convert each language to its own JSON file
  Object.entries(translations).forEach(([lang, content]) => {
    const langDir = path.join(baseDir, lang);
    
    // Create language directory if it doesn't exist
    if (!fs.existsSync(langDir)) {
      fs.mkdirSync(langDir, { recursive: true });
      console.log(`📁 Created directory: ${langDir}`);
    }

    // Write translation.json file
    const filePath = path.join(langDir, 'translation.json');
    const jsonContent = JSON.stringify(content, null, 2);
    
    fs.writeFileSync(filePath, jsonContent, 'utf-8');
    console.log(`✅ Wrote: ${filePath}`);
    
    // Count the number of translation keys
    const keyCount = Object.keys(content).length;
    console.log(`   📊 ${keyCount} translation keys for ${lang.toUpperCase()}`);
  });

  console.log('\n🎉 Translation conversion completed successfully!');
  console.log(`📍 Files created in: ${baseDir}`);
  
  // Show summary
  const languages = Object.keys(translations);
  console.log(`\n📋 Summary:`);
  console.log(`   Languages: ${languages.join(', ').toUpperCase()}`);
  console.log(`   Total files: ${languages.length}`);
  
  // Show next steps
  console.log(`\n🔧 Next steps:`);
  console.log(`   1. Update your language context to load from JSON files`);
  console.log(`   2. Consider using a library like i18next for better i18n support`);
  console.log(`   3. Add new translations by editing the JSON files directly`);
}

// Run the conversion
convertTranslations().catch(error => {
  console.error('❌ Conversion failed:', error);
  process.exit(1);
});
