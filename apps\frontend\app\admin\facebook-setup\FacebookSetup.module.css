.container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 2rem;
  background: #f9f9f9;
  min-height: 100vh;
  transition: background 0.3s ease;
}

/* Dark mode: Apply consistent gradient background */
[data-theme="dark"] .container {
  background: linear-gradient(to bottom right, #0d1117, #161b22);
}

.header {
  text-align: center;
  margin-bottom: 3rem;
}

.header h1 {
  color: #1877f2;
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
}

.header p {
  color: #666;
  font-size: 1.1rem;
}

.steps {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.step {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #1877f2;
}

.step h3 {
  color: #333;
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.step ol {
  margin-left: 1.5rem;
  line-height: 1.6;
}

.step ol li {
  margin-bottom: 0.5rem;
}

.step a {
  color: #1877f2;
  text-decoration: none;
  font-weight: 600;
}

.step a:hover {
  text-decoration: underline;
}

.step code {
  background: #f1f3f4;
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.inputGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.inputGroup label {
  font-weight: 600;
  color: #333;
}

.tokenInput {
  width: 100%;
  padding: 1rem;
  border: 2px solid #ddd;
  border-radius: 8px;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  resize: vertical;
  min-height: 80px;
}

.tokenInput:focus {
  outline: none;
  border-color: #1877f2;
}

.pageIdInput {
  padding: 0.75rem;
  border: 2px solid #ddd;
  border-radius: 8px;
  font-family: 'Courier New', monospace;
}

.pageIdInput:focus {
  outline: none;
  border-color: #1877f2;
}

.primaryBtn {
  background: linear-gradient(135deg, #1877f2 0%, #166fe5 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.primaryBtn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(24, 119, 242, 0.3);
}

.primaryBtn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.secondaryBtn {
  background: #f1f3f4;
  color: #333;
  border: 2px solid #ddd;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.secondaryBtn:hover:not(:disabled) {
  background: #e4e6ea;
  border-color: #1877f2;
}

.selectBtn {
  background: #42b883;
  color: white;
  border: none;
  padding: 0.3rem 0.8rem;
  border-radius: 4px;
  font-size: 0.8rem;
  cursor: pointer;
}

.pagesSection {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.pagesSection h4 {
  margin-bottom: 1rem;
  color: #333;
}

.pageItem {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.5rem;
  background: white;
  border-radius: 6px;
  margin-bottom: 0.5rem;
}

.pageItem span {
  font-weight: 600;
  flex: 1;
}

.pageItem code {
  background: #e9ecef;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
}

.success {
  background: #d4edda;
  border: 1px solid #c3e6cb;
  border-radius: 8px;
  padding: 1.5rem;
  color: #155724;
}

.error {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 8px;
  padding: 1.5rem;
  color: #721c24;
}

.error pre {
  background: #fff;
  padding: 1rem;
  border-radius: 4px;
  overflow-x: auto;
  margin-top: 1rem;
  font-size: 0.8rem;
}

.tokenResult {
  margin-top: 1rem;
}

.tokenResult h4 {
  margin-bottom: 1rem;
  color: #155724;
}

.envVar {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: #fff;
  padding: 1rem;
  border-radius: 6px;
  margin-bottom: 0.5rem;
  border: 1px solid #c3e6cb;
}

.envVar code {
  flex: 1;
  background: transparent;
  word-break: break-all;
  font-size: 0.8rem;
}

.envVar button {
  background: #28a745;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
}

.envVar button:hover {
  background: #218838;
}

.pageInfo {
  background: #fff;
  padding: 1rem;
  border-radius: 6px;
  margin: 1rem 0;
  border: 1px solid #c3e6cb;
}

.tokenInfo {
  background: #fff;
  padding: 1rem;
  border-radius: 6px;
  margin-top: 1rem;
  border: 1px solid #c3e6cb;
}

.tokenInfo p {
  margin: 0.5rem 0;
  font-size: 0.9rem;
}

.envExample {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  margin-top: 1rem;
}

.envExample code {
  background: transparent;
  font-size: 0.9rem;
  line-height: 1.6;
}
