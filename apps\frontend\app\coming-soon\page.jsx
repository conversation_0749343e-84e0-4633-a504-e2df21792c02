'use client';
import { useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { Suspense } from 'react';
import { useLanguage } from '../../contexts/LanguageContext';
import styles from './ComingSoon.module.css';

// Force dynamic rendering to avoid build-time issues
export const dynamic = 'force-dynamic';

function ComingSoonContent() {
  const searchParams = useSearchParams();
  const feature = searchParams.get('feature') || 'This Feature';
  const { t } = useLanguage();

  const getFeatureDetails = (featureName) => {
    switch (featureName.toLowerCase()) {
      case 'special-offer':
        return {
          title: t('special_offers.title'),
          description: t('special_offers.description'),
          icon: '🎯',
          features: [
            t('special_offers.seasonal_discounts'),
            t('special_offers.limited_time_promotions'),
            t('special_offers.exclusive_member_deals'),
            t('special_offers.flash_sales_notifications')
          ]
        };
      case 'sell-or-trade':
        return {
          title: t('sell_trade.title'),
          description: t('sell_trade.description'),
          icon: '🔄',
          features: [
            t('sell_trade.quick_vehicle_appraisals'),
            t('sell_trade.trade_in_evaluations'),
            t('sell_trade.hassle_free_selling_process'),
            t('sell_trade.competitive_market_pricing')
          ]
        };
      default:
        return {
          title: 'New Feature',
          description: 'An exciting new feature is coming to FazeNAuto.',
          icon: '🚀',
          features: [
            'Enhanced user experience',
            'Improved functionality',
            'Better customer service',
            'More convenient features'
          ]
        };
    }
  };

  const featureDetails = getFeatureDetails(feature);

  return (
    <div className={styles.comingSoonPage}>
      <div className={styles.container}>
        <div className={styles.content}>
          {/* Main Icon */}
          <div className={styles.mainIcon}>
            {featureDetails.icon}
          </div>

          {/* Title */}
          <h1 className={styles.title}>
            {featureDetails.title}
          </h1>
          
          <h2 className={styles.subtitle}>
            {feature === 'special-offer' ? t('special_offers.coming_soon') : feature === 'sell-or-trade' ? t('sell_trade.coming_soon') : 'Coming Soon'}
          </h2>

          {/* Description */}
          <p className={styles.description}>
            {featureDetails.description}
          </p>

          {/* Features List */}
          <div className={styles.featuresSection}>
            <h3 className={styles.featuresTitle}>{feature === 'special-offer' ? t('special_offers.what_to_expect') : feature === 'sell-or-trade' ? t('sell_trade.what_to_expect') : 'What to Expect:'}</h3>
            <ul className={styles.featuresList}>
              {featureDetails.features.map((item, index) => (
                <li key={index} className={styles.featureItem}>
                  {item}
                </li>
              ))}
            </ul>
          </div>

          {/* CTA Section */}
          <div className={styles.ctaSection}>
            <p className={styles.ctaText}>
              {feature === 'special-offer' ? t('special_offers.check_inventory') : feature === 'sell-or-trade' ? t('sell_trade.check_inventory') : 'In the meantime, check out our current inventory of quality pre-owned vehicles.'}
            </p>

            <div className={styles.buttons}>
              <Link href="/inventory/used-cars" className={styles.primaryBtn}>
                {feature === 'special-offer' ? t('special_offers.browse_inventory') : feature === 'sell-or-trade' ? t('sell_trade.browse_inventory') : 'Browse Inventory'}
              </Link>
              <Link href="/" className={styles.secondaryBtn}>
                {feature === 'special-offer' ? t('special_offers.back_to_home') : feature === 'sell-or-trade' ? t('sell_trade.back_to_home') : 'Back to Home'}
              </Link>
            </div>
          </div>

          {/* Contact Info */}
          <div className={styles.contactSection}>
            <p className={styles.contactText}>
              {feature === 'special-offer' ? t('special_offers.questions') : feature === 'sell-or-trade' ? t('sell_trade.questions') : 'Have questions? We\'d love to hear from you!'}
            </p>
            <div className={styles.contactInfo}>
              <span className={styles.contactItem}>
                📧 <EMAIL>
              </span>
              <span className={styles.contactItem}>
                📞 647-338-9110
              </span>
            </div>
          </div>
        </div>

        {/* Background Animation */}
        <div className={styles.backgroundAnimation}>
          <div className={styles.floatingIcon}>🚗</div>
          <div className={styles.floatingIcon}>⭐</div>
          <div className={styles.floatingIcon}>🔧</div>
          <div className={styles.floatingIcon}>💎</div>
        </div>
      </div>
    </div>
  );
}

export default function ComingSoon() {
  return (
    <Suspense fallback={
      <div className={styles.comingSoonPage}>
        <div className={styles.container}>
          <div className={styles.content}>
            <div className={styles.mainIcon}>🚀</div>
            <h1 className={styles.title}>Loading...</h1>
          </div>
        </div>
      </div>
    }>
      <ComingSoonContent />
    </Suspense>
  );
}
