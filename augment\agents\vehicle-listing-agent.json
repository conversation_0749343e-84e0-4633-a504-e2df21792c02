{"name": "Vehicle Listing Agent", "description": "AI agent specialized in creating, updating, and managing vehicle listings for the FazeNAuto platform", "version": "1.0.0", "capabilities": ["Create new vehicle listings", "Update existing vehicle information", "Validate vehicle data", "Generate vehicle descriptions", "Manage vehicle images and videos", "Handle vehicle features and specifications", "Process VIN decoding", "Manage vehicle status (active, sold, draft)"], "context": {"domain": "Vehicle Management", "platform": "FazeNAuto", "database": "MongoDB", "storage": "AWS S3", "framework": "Next.js"}, "prompts": {"create_listing": {"description": "Create a new vehicle listing with comprehensive details", "template": "Create a new vehicle listing for a {year} {make} {model} with the following details:\n\n**Basic Information:**\n- VIN: {vin}\n- Color: {color}\n- Mileage: {mileage} km\n- Price: ${price} CAD\n- Engine: {engine}\n- Transmission: {transmission}\n- Drivetrain: {drivetrain}\n\n**Features:**\n{features}\n\n**Images:**\n- Upload {image_count} high-quality images\n- Include exterior, interior, and engine bay shots\n- Ensure proper lighting and angles\n\n**Requirements:**\n1. Validate all required fields\n2. Generate SEO-friendly description\n3. Set appropriate vehicle status\n4. Upload images to S3 with proper naming\n5. Create database record with all relationships\n6. Generate unique listing ID\n\nGenerate the complete vehicle listing implementation.", "variables": ["year", "make", "model", "vin", "color", "mileage", "price", "engine", "transmission", "drivetrain", "features", "image_count"]}, "update_listing": {"description": "Update an existing vehicle listing", "template": "Update vehicle listing {listing_id} with the following changes:\n\n**Updates:**\n{updates}\n\n**Requirements:**\n1. Validate updated data\n2. Preserve existing images unless replaced\n3. Update modification timestamp\n4. Handle status changes appropriately\n5. Maintain data integrity\n6. Log changes for audit trail\n\nGenerate the update implementation with proper error handling.", "variables": ["listing_id", "updates"]}, "generate_description": {"description": "Generate compelling vehicle description", "template": "Generate a compelling, SEO-optimized description for a {year} {make} {model} with these specifications:\n\n**Vehicle Details:**\n- Mileage: {mileage} km\n- Engine: {engine}\n- Transmission: {transmission}\n- Color: {color}\n- Key Features: {key_features}\n\n**Requirements:**\n1. Professional, engaging tone\n2. Highlight key selling points\n3. Include relevant keywords for SEO\n4. Mention FazeNAuto quality standards\n5. Call-to-action for inquiries\n6. 150-300 words optimal length\n\nGenerate the vehicle description.", "variables": ["year", "make", "model", "mileage", "engine", "transmission", "color", "key_features"]}, "validate_vin": {"description": "Validate and decode VIN number", "template": "Validate and decode VIN: {vin}\n\n**Requirements:**\n1. Check VIN format (17 characters)\n2. Validate check digit\n3. Decode vehicle information using NHTSA API\n4. Extract make, model, year, engine details\n5. Flag any inconsistencies with provided data\n6. Return structured validation result\n\nGenerate VIN validation and decoding implementation.", "variables": ["vin"]}}, "workflows": {"complete_listing_creation": {"description": "End-to-end vehicle listing creation workflow", "steps": [{"step": 1, "action": "validate_input_data", "description": "Validate all required vehicle information"}, {"step": 2, "action": "decode_vin", "description": "Validate and decode VIN using NHTSA API"}, {"step": 3, "action": "process_images", "description": "Upload and process vehicle images to S3"}, {"step": 4, "action": "generate_description", "description": "Create compelling vehicle description"}, {"step": 5, "action": "create_database_record", "description": "Save vehicle listing to MongoDB"}, {"step": 6, "action": "generate_listing_id", "description": "Create unique listing identifier"}, {"step": 7, "action": "set_initial_status", "description": "Set vehicle status (draft/active)"}]}, "bulk_import": {"description": "Import multiple vehicles from CSV/Excel", "steps": [{"step": 1, "action": "parse_import_file", "description": "Parse CSV/Excel file and validate format"}, {"step": 2, "action": "validate_batch_data", "description": "Validate all vehicle records in batch"}, {"step": 3, "action": "process_batch_images", "description": "Handle image uploads for multiple vehicles"}, {"step": 4, "action": "create_batch_records", "description": "Create database records in transaction"}, {"step": 5, "action": "generate_import_report", "description": "Create summary report of import results"}]}}, "data_models": {"vehicle": {"required_fields": ["make", "model", "year", "vin", "color", "mileage", "price", "engine", "transmission", "drivetrain"], "optional_fields": ["description", "features", "images", "video", "status", "dealer_notes", "inspection_date", "warranty_info"], "validation_rules": {"year": "Must be between 1900 and current year + 1", "vin": "Must be 17 characters, valid format", "mileage": "Must be positive number", "price": "Must be positive number", "images": "Maximum 50 images per vehicle"}}}, "api_endpoints": {"create": "POST /api/vehicles/upload", "update": "PUT /api/vehicles/[id]", "delete": "DELETE /api/vehicles/[id]", "get": "GET /api/vehicles/[id]", "list": "GET /api/vehicles", "search": "GET /api/vehicles/search", "validate_vin": "POST /api/vehicles/validate-vin"}, "integrations": {"nhtsa_api": {"purpose": "VIN decoding and validation", "endpoint": "https://vpic.nhtsa.dot.gov/api/vehicles/DecodeVin/{vin}?format=json"}, "aws_s3": {"purpose": "Image and video storage", "bucket": "fazenauto-vehicle-images", "structure": "vehicles/{make}/{model}/{year}/"}, "mongodb": {"purpose": "Vehicle data storage", "collection": "vehicles", "indexes": ["make", "model", "year", "status", "createdAt"]}}, "error_handling": {"validation_errors": "Return detailed field-level validation messages", "upload_errors": "Retry failed uploads, log errors for manual review", "database_errors": "Rollback transactions, preserve data integrity", "api_errors": "Graceful degradation, fallback to manual entry"}, "performance_considerations": {"image_processing": "Process images in parallel, use compression", "batch_operations": "Use transactions, process in chunks", "caching": "<PERSON><PERSON> frequently accessed vehicle data", "indexing": "Ensure proper database indexes for search performance"}, "security": {"authentication": "Require dealer/admin authentication for modifications", "authorization": "Role-based access control", "data_validation": "Sanitize all inputs, prevent injection attacks", "file_uploads": "Validate file types, scan for malware"}, "monitoring": {"metrics": ["Listing creation success rate", "Image upload performance", "VIN validation accuracy", "Search response times"], "alerts": ["Failed listing creations", "S3 upload failures", "Database connection issues", "High error rates"]}}