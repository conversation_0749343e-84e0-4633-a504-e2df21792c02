/**
 * Error handling utilities for the ledger system
 */

export class LedgerError extends Error {
  constructor(message, code = 'LEDGER_ERROR', statusCode = 500) {
    super(message);
    this.name = 'LedgerError';
    this.code = code;
    this.statusCode = statusCode;
  }
}

export class ValidationError extends LedgerError {
  constructor(message, field = null) {
    super(message, 'VALIDATION_ERROR', 400);
    this.name = 'ValidationError';
    this.field = field;
  }
}

export class FileUploadError extends LedgerError {
  constructor(message, fileName = null) {
    super(message, 'FILE_UPLOAD_ERROR', 400);
    this.name = 'FileUploadError';
    this.fileName = fileName;
  }
}

export class S3Error extends LedgerError {
  constructor(message, operation = null) {
    super(message, 'S3_ERROR', 500);
    this.name = 'S3Error';
    this.operation = operation;
  }
}

export class DatabaseError extends LedgerError {
  constructor(message, operation = null) {
    super(message, 'DATABASE_ERROR', 500);
    this.name = 'DatabaseError';
    this.operation = operation;
  }
}

/**
 * Validate transaction form data
 * @param {Object} formData - Form data to validate
 * @returns {Object} Validation result
 */
export function validateTransactionData(formData) {
  const errors = [];

  // Required fields
  if (!formData.type || formData.type.trim() === '') {
    errors.push(new ValidationError('Transaction type is required', 'type'));
  }

  if (!formData.description || formData.description.trim() === '') {
    errors.push(new ValidationError('Description is required', 'description'));
  }

  if (!formData.amount || isNaN(parseFloat(formData.amount))) {
    errors.push(new ValidationError('Valid amount is required', 'amount'));
  } else if (parseFloat(formData.amount) <= 0) {
    errors.push(new ValidationError('Amount must be greater than 0', 'amount'));
  }

  if (!formData.addedBy || formData.addedBy.trim() === '') {
    errors.push(new ValidationError('Added by field is required', 'addedBy'));
  }

  // Optional field validations
  if (formData.description && formData.description.length > 500) {
    errors.push(new ValidationError('Description must be 500 characters or less', 'description'));
  }

  if (formData.notes && formData.notes.length > 1000) {
    errors.push(new ValidationError('Notes must be 1000 characters or less', 'notes'));
  }

  if (formData.date) {
    const date = new Date(formData.date);
    if (isNaN(date.getTime())) {
      errors.push(new ValidationError('Invalid date format', 'date'));
    }
  }

  // Currency validation
  const validCurrencies = ['CAD', 'USD', 'EUR'];
  if (formData.currency && !validCurrencies.includes(formData.currency)) {
    errors.push(new ValidationError('Invalid currency', 'currency'));
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate file uploads
 * @param {File[]} files - Files to validate
 * @returns {Object} Validation result
 */
export function validateFileUploads(files) {
  const errors = [];
  const validTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'];
  const maxSize = 10 * 1024 * 1024; // 10MB
  const maxFiles = 20;

  if (files.length > maxFiles) {
    errors.push(new FileUploadError(`Maximum ${maxFiles} files allowed`));
  }

  files.forEach((file, index) => {
    if (!validTypes.includes(file.type)) {
      errors.push(new FileUploadError(
        `File "${file.name}" has unsupported format. Allowed: PDF, JPG, PNG`,
        file.name
      ));
    }

    if (file.size > maxSize) {
      errors.push(new FileUploadError(
        `File "${file.name}" is too large. Maximum size is 10MB`,
        file.name
      ));
    }

    if (file.size === 0) {
      errors.push(new FileUploadError(
        `File "${file.name}" is empty`,
        file.name
      ));
    }
  });

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Handle API errors and return appropriate response
 * @param {Error} error - Error to handle
 * @returns {Object} Error response object
 */
export function handleApiError(error) {
  console.error('API Error:', error);

  if (error instanceof ValidationError) {
    return {
      success: false,
      error: error.message,
      code: error.code,
      field: error.field,
      statusCode: error.statusCode
    };
  }

  if (error instanceof FileUploadError) {
    return {
      success: false,
      error: error.message,
      code: error.code,
      fileName: error.fileName,
      statusCode: error.statusCode
    };
  }

  if (error instanceof S3Error) {
    return {
      success: false,
      error: 'File storage error. Please try again.',
      code: error.code,
      statusCode: error.statusCode
    };
  }

  if (error instanceof DatabaseError) {
    return {
      success: false,
      error: 'Database error. Please try again.',
      code: error.code,
      statusCode: error.statusCode
    };
  }

  if (error instanceof LedgerError) {
    return {
      success: false,
      error: error.message,
      code: error.code,
      statusCode: error.statusCode
    };
  }

  // Generic error
  return {
    success: false,
    error: 'An unexpected error occurred. Please try again.',
    code: 'INTERNAL_ERROR',
    statusCode: 500
  };
}

/**
 * Format error messages for display
 * @param {Error|Object} error - Error to format
 * @returns {string} Formatted error message
 */
export function formatErrorMessage(error) {
  if (typeof error === 'string') {
    return error;
  }

  if (error.message) {
    return error.message;
  }

  if (error.error) {
    return error.error;
  }

  return 'An unexpected error occurred';
}

/**
 * Log error with context
 * @param {Error} error - Error to log
 * @param {Object} context - Additional context
 */
export function logError(error, context = {}) {
  const errorInfo = {
    message: error.message,
    stack: error.stack,
    name: error.name,
    code: error.code,
    timestamp: new Date().toISOString(),
    ...context
  };

  console.error('Ledger System Error:', errorInfo);

  // In production, you might want to send this to a logging service
  // Example: sendToLoggingService(errorInfo);
}

/**
 * Retry function with exponential backoff
 * @param {Function} fn - Function to retry
 * @param {number} maxRetries - Maximum number of retries
 * @param {number} baseDelay - Base delay in milliseconds
 * @returns {Promise} Result of the function
 */
export async function retryWithBackoff(fn, maxRetries = 3, baseDelay = 1000) {
  let lastError;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      if (attempt === maxRetries) {
        throw error;
      }

      const delay = baseDelay * Math.pow(2, attempt);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError;
}
