# AWS S3 Integration Guide

This document explains how the FazeNAuto AWS S3 image upload system works from frontend to backend. 🚀

## 🏗️ System Overview

The S3 integration handles:
- **Vehicle Image Uploads**: Up to 50 images per vehicle
- **Video Uploads**: Vehicle videos with proper organization
- **Image Processing**: Watermarking, resizing, and optimization
- **Structured Storage**: Organized by make/model/year
- **Duplicate Prevention**: Hash-based duplicate detection

## 📊 Data Flow Diagram

```
Frontend Upload Form
        ↓
    FormData with Files
        ↓
    /api/vehicles/upload
        ↓
    File Processing & Validation
        ↓
    S3 Upload (AWS SDK)
        ↓
    MongoDB Storage (URLs)
        ↓
    Response to Frontend
```

## 🔧 AWS S3 Configuration

### Environment Variables
```bash
# AWS S3 Configuration
CUSTOM_AWS_ACCESS_KEY_ID=your-access-key-id
CUSTOM_AWS_SECRET_ACCESS_KEY=your-secret-access-key
CUSTOM_AWS_REGION=us-east-1
CUSTOM_AWS_S3_BUCKET_NAME=fazenauto-vehicle-images
```

### S3 Client Setup
```javascript
// S3 Client Configuration
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';

const s3 = new S3Client({
  region: process.env.CUSTOM_AWS_REGION,
  credentials: {
    accessKeyId: process.env.CUSTOM_AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.CUSTOM_AWS_SECRET_ACCESS_KEY,
  },
});
```

## 📁 S3 Bucket Structure

### File Organization
```
fazenauto-vehicle-images/
├── vehicles/
│   ├── toyota/
│   │   ├── camry/
│   │   │   ├── 2023/
│   │   │   │   ├── uuid1.jpg
│   │   │   │   ├── uuid2.jpg
│   │   │   │   └── videos/
│   │   │   │       └── uuid3.mp4
│   │   │   └── 2024/
│   │   └── corolla/
│   └── honda/
│       └── civic/
└── processed/
    └── vehicles/
        └── {vehicleId}/
            ├── facebook_1_timestamp.jpg
            ├── autotrader_1_timestamp.jpg
            └── kijiji_1_timestamp.jpg
```

### Naming Convention
- **Images**: `vehicles/{make}/{model}/{year}/{uuid}.{ext}`
- **Videos**: `vehicles/{make}/{model}/{year}/videos/{uuid}.{ext}`
- **Processed**: `vehicles/{vehicleId}/processed/{platform}_{index}_{timestamp}.jpg`

## 🚀 Frontend Implementation

### Upload Form Component
```jsx
// Vehicle Upload Form
<form onSubmit={handleSubmit} encType="multipart/form-data">
  <input
    type="file"
    name="images"
    accept="image/*"
    multiple
    required
    className={styles.fileInput}
  />
  <input
    type="file"
    name="video"
    accept="video/*"
    className={styles.fileInput}
  />
</form>
```

### Form Submission
```javascript
const handleSubmit = async (e) => {
  e.preventDefault();
  setIsLoading(true);

  try {
    const submitFormData = new FormData(e.target);
    
    // Add additional data
    submitFormData.append('features', JSON.stringify(selectedFeatures));

    const res = await fetch('/api/vehicles/upload', {
      method: 'POST',
      body: submitFormData,
    });

    const data = await res.json();
    
    if (data.success) {
      setMessage('Vehicle uploaded successfully!');
      // Reset form
    } else {
      setMessage(`Error: ${data.error}`);
    }
  } catch (error) {
    setMessage('Upload failed. Please try again.');
  } finally {
    setIsLoading(false);
  }
};
```

## 🔄 Backend Processing

### API Route: `/api/vehicles/upload`

#### 1. Form Data Processing
```javascript
export async function POST(request) {
  try {
    await connectToDatabase();
    
    const form = await request.formData();
    
    // Extract vehicle data
    const make = form.get('make');
    const model = form.get('model');
    const year = form.get('year');
    
    // Extract files
    const imageFiles = form.getAll('images');
    const videoFile = form.get('video');
```

#### 2. Image Upload Processing
```javascript
const imageUrls = [];
const imageHashes = [];

for (const imageFile of imageFiles) {
  if (imageFile && imageFile.size > 0) {
    // Convert to buffer
    const buffer = Buffer.from(await imageFile.arrayBuffer());
    
    // Generate hash for duplicate detection
    const hash = crypto.createHash('md5').update(buffer).digest('hex');
    
    // Create structured file path
    const uuid = uuidv4();
    const fileExtension = path.extname(imageFile.name);
    const safeMake = make.replace(/\s+/g, '-').toLowerCase();
    const safeModel = model.replace(/\s+/g, '-').toLowerCase();
    const fileKey = `vehicles/${safeMake}/${safeModel}/${year}/${uuid}${fileExtension}`;
    
    // Upload to S3
    const uploadParams = {
      Bucket: process.env.CUSTOM_AWS_S3_BUCKET_NAME,
      Key: fileKey,
      Body: buffer,
      ContentType: imageFile.type,
      CacheControl: 'public, max-age=31536000',
    };
    
    await s3.send(new PutObjectCommand(uploadParams));
    
    // Generate public URL
    const imageUrl = `https://${process.env.CUSTOM_AWS_S3_BUCKET_NAME}.s3.${process.env.CUSTOM_AWS_REGION}.amazonaws.com/${fileKey}`;
    imageUrls.push(imageUrl);
    imageHashes.push(hash);
  }
}
```

#### 3. Video Upload Processing
```javascript
let videoUrl = null;

if (videoFile && videoFile.size > 0) {
  const videoBuffer = Buffer.from(await videoFile.arrayBuffer());
  const videoUuid = uuidv4();
  const videoExtension = path.extname(videoFile.name);
  const videoFileKey = `vehicles/${safeMake}/${safeModel}/${year}/videos/${videoUuid}${videoExtension}`;
  
  const videoUploadParams = {
    Bucket: process.env.CUSTOM_AWS_S3_BUCKET_NAME,
    Key: videoFileKey,
    Body: videoBuffer,
    ContentType: videoFile.type,
    CacheControl: 'public, max-age=31536000',
  };
  
  await s3.send(new PutObjectCommand(videoUploadParams));
  videoUrl = `https://${process.env.CUSTOM_AWS_S3_BUCKET_NAME}.s3.${process.env.CUSTOM_AWS_REGION}.amazonaws.com/${videoFileKey}`;
}
```

#### 4. Database Storage
```javascript
// Create vehicle record
const vehicle = new Vehicle({
  make,
  model,
  year,
  color,
  vin,
  mileage: parseInt(mileage),
  price: parseFloat(price),
  images: imageUrls,
  imageHashes,
  videoUrl,
  features: JSON.parse(features || '{}'),
  status: 'active',
  createdAt: new Date(),
  updatedAt: new Date()
});

await vehicle.save();
```

## 🎨 Image Processing Pipeline

### Advanced Processing Features
```javascript
// Image Processing API: /api/image-processing
const processImage = async (imageBuffer, options) => {
  const {
    platform,        // facebook, autotrader, kijiji
    addWatermark,     // boolean
    enhanceLighting,  // boolean
    blurLicensePlates // boolean (placeholder)
  } = options;
  
  // Platform-specific specifications
  const specs = {
    facebook: { width: 1200, height: 630, quality: 85 },
    autotrader: { width: 800, height: 600, quality: 90 },
    kijiji: { width: 640, height: 480, quality: 80 }
  };
  
  // Process with Sharp
  let sharpImage = sharp(imageBuffer)
    .resize(specs[platform].width, specs[platform].height, {
      fit: 'cover',
      position: 'center'
    });
  
  // Add watermark if requested
  if (addWatermark) {
    sharpImage = await addWatermark(sharpImage, platform);
  }
  
  // Optimize and compress
  const processedBuffer = await sharpImage
    .jpeg({ 
      quality: specs[platform].quality,
      progressive: true,
      mozjpeg: true
    })
    .toBuffer();
  
  // Upload processed image
  const s3Key = `vehicles/${vehicleId}/processed/${platform}_${imageIndex}_${Date.now()}.jpg`;
  const s3Url = await uploadToS3(processedBuffer, s3Key);
  
  return s3Url;
};
```

## 🔒 Security & Permissions

### S3 Bucket Policy
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "PublicReadGetObject",
      "Effect": "Allow",
      "Principal": "*",
      "Action": "s3:GetObject",
      "Resource": "arn:aws:s3:::fazenauto-vehicle-images/*"
    }
  ]
}
```

### IAM User Permissions
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "s3:PutObject",
        "s3:PutObjectAcl",
        "s3:GetObject",
        "s3:DeleteObject"
      ],
      "Resource": "arn:aws:s3:::fazenauto-vehicle-images/*"
    }
  ]
}
```

## 📈 Performance Optimizations

### 1. Caching Strategy
- **Cache-Control**: `public, max-age=31536000` (1 year)
- **CDN**: CloudFront distribution for global delivery
- **Progressive JPEG**: Faster loading for web

### 2. Upload Optimizations
- **Parallel Uploads**: Multiple images uploaded simultaneously
- **Compression**: Client-side compression before upload
- **Duplicate Detection**: MD5 hash comparison

### 3. Error Handling
```javascript
try {
  await s3.send(new PutObjectCommand(uploadParams));
} catch (error) {
  console.error('S3 upload failed:', error);
  
  if (error.name === 'NoSuchBucket') {
    throw new Error('S3 bucket not found');
  } else if (error.name === 'AccessDenied') {
    throw new Error('S3 access denied - check credentials');
  } else {
    throw new Error(`S3 upload failed: ${error.message}`);
  }
}
```

## 🔍 Monitoring & Debugging

### Logging Strategy
```javascript
// Upload logging
console.log('S3 Upload:', {
  bucket: process.env.CUSTOM_AWS_S3_BUCKET_NAME,
  key: fileKey,
  size: buffer.length,
  contentType: imageFile.type
});

// Error logging
console.error('Upload failed:', {
  error: error.message,
  stack: error.stack,
  fileKey,
  vehicleId
});
```

### Health Checks
- **S3 Connectivity**: Regular bucket access tests
- **Upload Success Rate**: Monitor failed uploads
- **Storage Usage**: Track bucket size and costs

## 🚨 Common Issues & Solutions

### Issue 1: Access Denied
**Problem**: S3 returns 403 Forbidden
**Solution**: 
- Check IAM user permissions
- Verify bucket policy
- Ensure correct region configuration

### Issue 2: Large File Uploads
**Problem**: Timeout on large files
**Solution**:
- Implement multipart uploads for files > 5MB
- Add client-side compression
- Use presigned URLs for direct uploads

### Issue 3: Duplicate Images
**Problem**: Same image uploaded multiple times
**Solution**:
- MD5 hash comparison before upload
- Database constraint on image hashes
- User feedback for duplicates

---

*This integration provides a robust, scalable image storage solution for the FazeNAuto platform! 🚀*
