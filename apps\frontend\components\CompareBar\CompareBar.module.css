/* Floating Compare Bar */
.compareBar {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--bg-primary, white);
  border: 1px solid var(--border-primary, #e2e8f0);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  animation: slideUp 0.3s ease-out;
  max-width: 90vw;
  width: auto;
  min-width: 320px;
}

/* Dark mode styling */
[data-theme="dark"] .compareBar {
  background: var(--bg-secondary);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(100%);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

.compareContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  gap: 1rem;
}

.compareInfo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
}

.compareIcon {
  font-size: 1.5rem;
}

.compareText {
  font-weight: 600;
  color: var(--text-primary, #2d3748);
  font-size: 1rem;
}

.helpText {
  font-size: 0.85rem;
  color: var(--text-secondary, #718096);
  font-style: italic;
}

.compareActions {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.clearBtn {
  background: transparent;
  border: 1px solid var(--border-primary, #e2e8f0);
  color: var(--text-secondary, #718096);
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.clearBtn:hover {
  background: var(--bg-hover, #f8f9fa);
  color: var(--text-primary, #2d3748);
  border-color: var(--text-secondary, #718096);
}

.compareBtn {
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  white-space: nowrap;
}

.compareBtnEnabled {
  background: var(--accent-primary, #4299e1);
  color: white;
}

.compareBtnEnabled:hover {
  background: var(--accent-hover, #3182ce);
  transform: translateY(-1px);
}

.compareBtnDisabled {
  background: var(--bg-tertiary, #f1f5f9);
  color: var(--text-tertiary, #94a3b8);
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
  .compareBar {
    bottom: 10px;
    left: 10px;
    right: 10px;
    transform: none;
    max-width: none;
    width: auto;
    min-width: auto;
  }
  
  .compareContent {
    padding: 0.875rem 1rem;
    gap: 0.75rem;
  }
  
  .compareInfo {
    gap: 0.5rem;
  }
  
  .compareText {
    font-size: 0.9rem;
  }
  
  .helpText {
    display: none; /* Hide help text on mobile to save space */
  }
  
  .compareActions {
    gap: 0.5rem;
  }
  
  .clearBtn {
    padding: 0.5rem 0.75rem;
    font-size: 0.85rem;
  }
  
  .compareBtn {
    padding: 0.625rem 1rem;
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .compareContent {
    flex-direction: column;
    gap: 0.75rem;
    padding: 1rem;
  }
  
  .compareInfo {
    justify-content: center;
  }
  
  .compareActions {
    width: 100%;
    justify-content: center;
  }
  
  .clearBtn,
  .compareBtn {
    flex: 1;
    text-align: center;
  }
}
