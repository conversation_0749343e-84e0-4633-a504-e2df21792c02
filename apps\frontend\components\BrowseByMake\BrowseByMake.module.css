/* Browse by Make Section */
.browseSection {
  padding: 3rem 1rem;
  background: white;
  transition: background 0.3s ease;
}

/* Dark mode: Browse section should use transparent background */
[data-theme="dark"] .browseSection {
  background: transparent;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.sectionHeader {
  text-align: center;
  margin-bottom: 3rem;
}

.sectionTitle {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary, #2d3748);
  margin-bottom: 1rem;
}

.makeHighlight {
  color: #e53e3e;
}

/* Brands Grid */
.brandsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 2rem;
  max-width: 1000px;
  margin: 0 auto;
}

.brandCard {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem 0.5rem;
  background: transparent;
  border-radius: 0;
  box-shadow: none;
  transition: all 0.3s ease;
  border: none;
  text-align: center;
}

.brandCard:hover {
  transform: none;
  box-shadow: none;
}

.brandLogo {
  width: 80px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  position: relative;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.brandLogo:hover {
  transform: scale(1.02);
}

.logoImage {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  transition: all 0.3s ease;
  filter: brightness(0.95) contrast(1.05);
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

.brandLogo:hover .logoImage {
  transform: scale(1.08);
  filter: brightness(1) contrast(1.1);
}

.brandEmoji {
  font-size: 3rem;
  transition: transform 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.brandLogo:hover .brandEmoji {
  transform: scale(1.1);
}

.brandName {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary, #2d3748);
  text-align: center;
  margin-bottom: 0.5rem;
}

.vehicleCount {
  font-size: 0.9rem;
  color: var(--text-secondary, #4a5568);
  font-weight: 500;
}

/* Loading States */
.loadingGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 2rem;
  max-width: 1000px;
  margin: 0 auto;
}

.loadingCard {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem 0.5rem;
  background: transparent;
  border-radius: 0;
  box-shadow: none;
  border: none;
}

.loadingSkeleton {
  width: 80px;
  height: 60px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 8px;
}

[data-theme="dark"] .loadingSkeleton {
  background: linear-gradient(90deg, #2d3748 25%, #4a5568 50%, #2d3748 75%);
  background-size: 200% 100%;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .browseSection {
    padding: 2rem 1rem;
  }

  .sectionTitle {
    font-size: 2rem;
  }

  .brandsGrid,
  .loadingGrid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1.5rem;
  }

  .brandCard,
  .loadingCard {
    padding: 1rem 0.5rem;
  }

  .brandLogo {
    width: 60px;
    height: 45px;
  }
}

@media (max-width: 480px) {
  .brandsGrid,
  .loadingGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .brandCard,
  .loadingCard {
    padding: 0.75rem 0.25rem;
  }

  .brandLogo {
    width: 50px;
    height: 40px;
  }

  .vehicleCount {
    font-size: 0.8rem;
  }
}
