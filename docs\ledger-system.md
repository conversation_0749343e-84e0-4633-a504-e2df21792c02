# 💰 FazeNAuto Ledger System Documentation

## Overview

The FazeNAuto Ledger System is a comprehensive accounting and financial tracking solution designed specifically for automotive dealerships. It provides secure transaction recording, receipt management, and financial reporting capabilities.

## 🚀 Features

### Core Functionality
- **Transaction Management**: Record income and expenses with detailed categorization
- **Receipt Storage**: Secure S3-based file storage with automatic metadata generation
- **Vehicle Integration**: Link transactions to specific vehicles in inventory
- **Financial Reporting**: Real-time summaries and export capabilities
- **Search & Filtering**: Advanced filtering by date, type, amount, and more

### Security Features
- **Role-based Access**: Admin and dealer role permissions
- **Secure File Storage**: S3 server-side encryption (SSE-S3)
- **Signed URLs**: Time-limited access to receipt downloads
- **Input Validation**: Comprehensive server and client-side validation

## 📁 System Architecture

### Database Schema (MongoDB)

#### Ledger Collection
```javascript
{
  _id: ObjectId,
  type: String, // "Vehicle Purchase", "Repair Cost", "Sale", etc.
  description: String,
  amount: Number,
  currency: String, // "CAD", "USD", "EUR"
  date: Date,
  vehicleId: ObjectId, // Optional reference to Vehicle
  addedBy: String, // User email
  receiptUrls: [String], // S3 URLs
  s3Path: String, // S3 folder path
  notes: String,
  category: String, // Auto-generated from type
  isIncome: Boolean, // Auto-calculated
  fiscalYear: Number,
  quarter: Number,
  receiptCount: Number,
  createdAt: Date,
  updatedAt: Date
}
```

### S3 Storage Structure
```
fazenauto-ledger-documents/
├── receipts/
│   ├── vehicle-purchase/
│   │   └── 2025-01/
│   │       └── {transactionId}/
│   │           ├── receipt1.pdf
│   │           ├── receipt2.jpg
│   │           └── metadata.json
│   ├── vehicle-repair/
│   ├── sale/
│   └── general-cost/
```

### API Endpoints

#### Main Ledger Operations
- `GET /api/ledger` - List transactions with filtering
- `POST /api/ledger` - Create new transaction with file uploads
- `GET /api/ledger/[id]` - Get single transaction details
- `PUT /api/ledger/[id]` - Update transaction
- `DELETE /api/ledger/[id]` - Delete transaction and files

#### Supporting Endpoints
- `GET /api/ledger/vehicles` - Search vehicles for transaction linking
- `GET /api/ledger/export` - Export data as CSV or PDF
- `GET /api/ledger/receipts/[transactionId]/[fileName]` - Download receipt

## 🎯 User Interface

### Pages Structure
```
/admin/ledger/
├── page.jsx              # Main ledger list with filters
├── new/page.jsx          # Create new transaction form
├── [id]/page.jsx         # Transaction detail view
└── *.module.css          # Component-specific styles
```

### Key Components
1. **Ledger List Page** (`/admin/ledger`)
   - Sortable/filterable transaction table
   - Summary cards (income, expenses, net)
   - Export buttons (CSV, PDF)
   - Pagination support

2. **New Transaction Form** (`/admin/ledger/new`)
   - Transaction type selection
   - Amount and currency input
   - Vehicle search and selection
   - Multiple file upload
   - Form validation

3. **Transaction Detail View** (`/admin/ledger/[id]`)
   - Complete transaction information
   - Vehicle details (if linked)
   - Receipt download links
   - Delete functionality

## 🔧 Installation & Setup

### Prerequisites
- Node.js 18+
- MongoDB database
- AWS S3 bucket: `fazenauto-ledger-documents`
- Required environment variables

### Environment Variables
```bash
# MongoDB
MONGODB_URI=mongodb://...

# AWS S3
CUSTOM_AWS_REGION=us-east-1
CUSTOM_AWS_ACCESS_KEY_ID=your_access_key
CUSTOM_AWS_SECRET_ACCESS_KEY=your_secret_key

# Authentication
ADMIN_SECRET=your_admin_secret
AUTHORIZED_EMAILS=<EMAIL>
```

### Dependencies
```bash
npm install uuid pdfkit @aws-sdk/client-s3 @aws-sdk/s3-request-presigner
```

## 📊 Usage Guide

### Creating Transactions

1. **Navigate to Ledger**
   - Go to `/admin/ledger`
   - Click "➕ Add Transaction"

2. **Fill Transaction Form**
   - Select transaction type
   - Enter description and amount
   - Choose currency (defaults to CAD)
   - Set date (defaults to today)
   - Optionally link to vehicle
   - Upload receipt files
   - Add notes if needed

3. **File Upload Guidelines**
   - Supported formats: PDF, JPG, PNG
   - Maximum file size: 10MB each
   - Maximum files: 20 per transaction
   - Files are automatically encrypted in S3

### Viewing and Managing Transactions

1. **List View Features**
   - Filter by date range, type, vehicle
   - Search in descriptions
   - Sort by any column
   - View summary statistics

2. **Detail View**
   - Complete transaction information
   - Download receipt files
   - View linked vehicle details
   - Delete transaction (with confirmation)

### Exporting Data

1. **CSV Export**
   - Includes all transaction fields
   - Respects current filters
   - Suitable for spreadsheet analysis

2. **PDF Export**
   - Formatted report with summary
   - Professional appearance
   - Includes transaction details

## 🛡️ Security Considerations

### Access Control
- Only authenticated admin/dealer users can access
- User email stored with each transaction
- Role-based permissions enforced

### File Security
- All S3 uploads use server-side encryption
- Receipt downloads use signed URLs (1-hour expiration)
- Files organized in structured folders

### Data Validation
- Server-side validation for all inputs
- File type and size restrictions
- SQL injection prevention
- XSS protection

## 🔍 Troubleshooting

### Common Issues

1. **File Upload Failures**
   - Check file size (max 10MB)
   - Verify file format (PDF, JPG, PNG only)
   - Ensure AWS credentials are correct

2. **Transaction Creation Errors**
   - Validate all required fields
   - Check amount is positive number
   - Verify user authentication

3. **Export Issues**
   - Large datasets may take time to process
   - Check browser download settings
   - Verify PDF generation dependencies

### Error Handling
- Comprehensive error logging
- User-friendly error messages
- Graceful degradation for network issues
- Retry mechanisms for S3 operations

## 📈 Future Enhancements

### Planned Features
- QuickBooks integration
- Automated invoice generation
- Advanced reporting dashboard
- Mobile app support
- Bulk transaction import
- Audit trail logging

### Extensibility
- Modular architecture allows easy feature additions
- API-first design supports integrations
- Configurable transaction types
- Customizable export formats

## 🤝 Support

For technical support or feature requests:
- Check the troubleshooting section
- Review error logs in browser console
- Contact system administrator

## 📝 Changelog

### Version 1.0.0 (Current)
- Initial release
- Core transaction management
- S3 receipt storage
- CSV/PDF export
- Vehicle integration
- Comprehensive error handling

---

**Last Updated**: January 2025  
**System Version**: 1.0.0  
**Documentation Version**: 1.0.0
