/* Legal Pages Styles */

.legalPage {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 2rem 1rem;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.header {
  background: linear-gradient(135deg, #0f1419 0%, #1a202c 50%, #2d3748 100%);
  color: white;
  padding: 3rem 2rem;
  text-align: center;
}

.title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.lastUpdated {
  font-size: 1rem;
  opacity: 0.9;
  margin: 0;
}

.content {
  padding: 2rem;
}

.section {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.section h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e53e3e;
  display: inline-block;
}

.section p {
  color: #4a5568;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.section ul {
  color: #4a5568;
  line-height: 1.6;
  margin-left: 1.5rem;
  margin-bottom: 1rem;
}

.section li {
  margin-bottom: 0.5rem;
}

.contactInfo {
  background: #f7fafc;
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #e53e3e;
}

.contactInfo p {
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.footer {
  padding: 1.5rem 2rem;
  background: #f7fafc;
  border-top: 1px solid #e2e8f0;
}

.backButton {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  padding: 0.5rem 1rem;
  border-radius: 6px;
}

.backButton:hover {
  background: #667eea;
  color: white;
  transform: translateX(-3px);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .legalPage {
    padding: 1rem 0.5rem;
  }
  
  .header {
    padding: 2rem 1rem;
  }
  
  .title {
    font-size: 2rem;
  }
  
  .content {
    padding: 1.5rem;
  }
  
  .footer {
    padding: 1rem 1.5rem;
  }
}
