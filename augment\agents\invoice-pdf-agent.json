{"name": "Invoice PDF Generation Agent", "description": "AI agent specialized in generating professional PDF invoices and compliance documents for vehicle sales in the FazeNAuto platform", "version": "1.0.0", "capabilities": ["Generate professional invoices", "Create bill of sale documents", "Generate OMVIC compliance forms", "Create HST documentation", "Handle warranty disclosures", "Generate inspection certificates", "Manage invoice numbering", "Handle digital signatures"], "context": {"domain": "Document Generation & Compliance", "platform": "FazeNAuto", "regulations": "Ontario tax and motor vehicle regulations", "pdf_library": "PDFKit", "framework": "Next.js"}, "prompts": {"generate_invoice": {"description": "Generate a professional vehicle sale invoice", "template": "Generate a professional invoice for vehicle sale:\n\n**Invoice Details:**\n- Invoice Number: {invoice_number}\n- Date: {invoice_date}\n- Due Date: {due_date}\n\n**Buyer Information:**\n- Name: {buyer_name}\n- Address: {buyer_address}\n- Phone: {buyer_phone}\n- Email: {buyer_email}\n- Driver's License: {buyer_license}\n\n**Vehicle Information:**\n- {year} {make} {model}\n- VIN: {vin}\n- Color: {color}\n- Mileage: {mileage} km\n- Engine: {engine}\n- Transmission: {transmission}\n\n**Pricing:**\n- Vehicle Price: ${vehicle_price}\n- HST (13%): ${hst_amount}\n- Total: ${total_amount}\n- Deposit: ${deposit_amount}\n- Balance Due: ${balance_due}\n\n**Requirements:**\n1. Professional FazeNAuto branding\n2. Clear pricing breakdown\n3. Payment terms and conditions\n4. Signature sections for buyer/seller\n5. Contact information\n6. Compliance with Ontario regulations\n7. Generate unique invoice number\n\nGenerate the complete invoice PDF implementation.", "variables": ["invoice_number", "invoice_date", "due_date", "buyer_name", "buyer_address", "buyer_phone", "buyer_email", "buyer_license", "year", "make", "model", "vin", "color", "mileage", "engine", "transmission", "vehicle_price", "hst_amount", "total_amount", "deposit_amount", "balance_due"]}, "generate_bill_of_sale": {"description": "Generate OMVIC-compliant bill of sale", "template": "Generate OMVIC-compliant bill of sale:\n\n**Transaction Details:**\n- Sale Date: {sale_date}\n- Transaction ID: {transaction_id}\n- RIN Number: {rin_number}\n\n**Dealer Information:**\n- Business Name: {dealer_name}\n- RIN: {dealer_rin}\n- Address: {dealer_address}\n- Phone: {dealer_phone}\n\n**Buyer Information:**\n- Name: {buyer_name}\n- Address: {buyer_address}\n- Driver's License: {buyer_license}\n\n**Vehicle Information:**\n- Year/Make/Model: {year} {make} {model}\n- VIN: {vin}\n- Odometer: {odometer} km\n- Condition: {condition}\n- Certification: {certification_status}\n- Warranty: {warranty_info}\n\n**Financial Details:**\n- Purchase Price: ${purchase_price}\n- Trade-in Value: ${trade_value}\n- HST: ${hst_amount}\n- Total: ${total_amount}\n\n**Requirements:**\n1. OMVIC compliance requirements\n2. All mandatory disclosure fields\n3. Proper RIN display\n4. Signature sections\n5. Certification checkboxes\n6. Warranty disclosure\n7. As-is sale conditions\n\nGenerate OMVIC bill of sale implementation.", "variables": ["sale_date", "transaction_id", "rin_number", "dealer_name", "dealer_rin", "dealer_address", "dealer_phone", "buyer_name", "buyer_address", "buyer_license", "year", "make", "model", "vin", "odometer", "condition", "certification_status", "warranty_info", "purchase_price", "trade_value", "hst_amount", "total_amount"]}, "generate_hst_form": {"description": "Generate HST documentation for vehicle sale", "template": "Generate HST documentation for vehicle sale:\n\n**HST Details:**\n- HST Registration: {hst_registration}\n- Tax Period: {tax_period}\n- Sale Date: {sale_date}\n\n**Vehicle Sale Information:**\n- Vehicle: {year} {make} {model}\n- VIN: {vin}\n- Sale Price: ${sale_price}\n- HST Rate: {hst_rate}%\n- HST Amount: ${hst_amount}\n\n**Buyer Information:**\n- Name: {buyer_name}\n- Address: {buyer_address}\n- HST Number: {buyer_hst} (if applicable)\n\n**Dealer Information:**\n- Business Name: {dealer_name}\n- HST Number: {dealer_hst}\n- Address: {dealer_address}\n\n**Requirements:**\n1. CRA-compliant HST documentation\n2. Proper tax calculations\n3. Clear breakdown of taxable amounts\n4. Business registration details\n5. Quarterly reporting information\n6. Audit trail documentation\n\nGenerate HST form implementation.", "variables": ["hst_registration", "tax_period", "sale_date", "year", "make", "model", "vin", "sale_price", "hst_rate", "hst_amount", "buyer_name", "buyer_address", "buyer_hst", "dealer_name", "dealer_hst", "dealer_address"]}, "generate_inspection_certificate": {"description": "Generate vehicle inspection certificate", "template": "Generate vehicle inspection certificate:\n\n**Inspection Details:**\n- Certificate Number: {certificate_number}\n- Inspection Date: {inspection_date}\n- Inspector: {inspector_name}\n- License Number: {inspector_license}\n\n**Vehicle Information:**\n- {year} {make} {model}\n- VIN: {vin}\n- Odometer: {odometer} km\n- License Plate: {license_plate}\n\n**Inspection Results:**\n- Overall Condition: {overall_condition}\n- Safety Items: {safety_items}\n- Emissions: {emissions_status}\n- Structural: {structural_condition}\n- Recommendations: {recommendations}\n\n**Certification:**\n- Certified: {certification_status}\n- Valid Until: {expiry_date}\n- Notes: {inspection_notes}\n\n**Requirements:**\n1. Official inspection format\n2. Inspector credentials\n3. Detailed inspection checklist\n4. Pass/fail indicators\n5. Expiry date calculation\n6. Official signatures\n7. Compliance with safety standards\n\nGenerate inspection certificate implementation.", "variables": ["certificate_number", "inspection_date", "inspector_name", "inspector_license", "year", "make", "model", "vin", "odometer", "license_plate", "overall_condition", "safety_items", "emissions_status", "structural_condition", "recommendations", "certification_status", "expiry_date", "inspection_notes"]}}, "workflows": {"complete_sale_documentation": {"description": "Generate all required documents for vehicle sale", "steps": [{"step": 1, "action": "generate_invoice", "description": "Create professional sales invoice"}, {"step": 2, "action": "generate_bill_of_sale", "description": "Create OMVIC-compliant bill of sale"}, {"step": 3, "action": "generate_hst_documentation", "description": "Create HST forms and documentation"}, {"step": 4, "action": "generate_warranty_disclosure", "description": "Create warranty disclosure documents"}, {"step": 5, "action": "compile_document_package", "description": "Combine all documents into package"}, {"step": 6, "action": "store_documents", "description": "Store documents in database and S3"}]}, "quarterly_hst_reporting": {"description": "Generate quarterly HST reports", "steps": [{"step": 1, "action": "collect_quarterly_sales", "description": "Gather all sales for the quarter"}, {"step": 2, "action": "calculate_hst_totals", "description": "Calculate total HST collected"}, {"step": 3, "action": "generate_summary_report", "description": "Create quarterly HST summary"}, {"step": 4, "action": "generate_detailed_report", "description": "Create detailed transaction report"}, {"step": 5, "action": "prepare_cra_submission", "description": "Format for CRA submission"}]}}, "document_templates": {"invoice": {"sections": ["header_with_branding", "company_information", "invoice_details", "buyer_information", "vehicle_details", "pricing_table", "payment_terms", "signature_section", "footer"], "required_fields": ["invoice_number", "date", "buyer_info", "vehicle_info", "pricing"]}, "bill_of_sale": {"sections": ["omvic_header", "dealer_information", "buyer_information", "vehicle_details", "condition_disclosure", "warranty_section", "pricing_breakdown", "certification_checkboxes", "signatures"], "compliance_requirements": ["rin_display", "omvic_disclosure", "as_is_conditions", "warranty_terms"]}}, "numbering_systems": {"invoice_numbering": {"format": "INV-{year}-{sequence}", "example": "INV-2024-00001", "sequence_reset": "yearly", "padding": 5}, "bill_of_sale_numbering": {"format": "BOS-{year}-{sequence}", "example": "BOS-2024-00001", "sequence_reset": "yearly", "padding": 5}, "certificate_numbering": {"format": "CERT-{year}-{month}-{sequence}", "example": "CERT-2024-06-001", "sequence_reset": "monthly", "padding": 3}}, "calculations": {"hst_calculation": {"ontario_rate": 0.13, "formula": "vehicle_price * hst_rate", "rounding": "round to nearest cent"}, "total_calculation": {"formula": "vehicle_price + hst_amount + fees - trade_value", "components": ["vehicle_price", "hst", "admin_fees", "trade_allowance"]}, "balance_calculation": {"formula": "total_amount - deposit_paid", "validation": "balance >= 0"}}, "styling": {"branding": {"logo": "FazeNAuto with red 'N'", "colors": {"primary": "#000000", "accent": "#ef4444", "text": "#333333", "light_text": "#666666"}, "fonts": {"primary": "<PERSON><PERSON>", "secondary": "Helvetica", "sizes": {"title": 24, "heading": 16, "body": 10, "small": 8}}}, "layout": {"page_size": "A4", "margins": "1 inch", "line_spacing": 1.2, "table_styling": "bordered with alternating rows"}}, "api_endpoints": {"generate_invoice": "POST /api/documents/invoice", "generate_bill_of_sale": "POST /api/documents/bill-of-sale", "generate_hst_form": "POST /api/documents/hst-form", "get_document": "GET /api/documents/[id]", "list_documents": "GET /api/documents", "download_pdf": "GET /api/documents/[id]/pdf"}, "storage": {"database_storage": {"collection": "documents", "fields": ["document_type", "document_number", "vehicle_id", "buyer_id", "dealer_id", "pdf_url", "created_at"]}, "s3_storage": {"bucket": "fazenauto-documents", "path_structure": "documents/{type}/{year}/{month}/", "naming": "{document_number}_{timestamp}.pdf"}}, "compliance": {"omvic_requirements": ["RIN display on all documents", "Proper disclosure statements", "As-is sale conditions", "Warranty information", "Dealer signature requirements"], "cra_requirements": ["Proper HST calculation", "Business registration display", "Tax period identification", "Audit trail maintenance"], "record_keeping": {"retention_period": "7 years", "backup_requirements": "Multiple copies", "audit_accessibility": "Searchable and retrievable"}}, "error_handling": {"data_validation": "Validate all required fields before generation", "calculation_errors": "Verify all mathematical calculations", "template_errors": "Handle missing template sections gracefully", "pdf_generation_errors": "Provide fallback options and error reporting"}, "security": {"access_control": "Restrict document generation to authorized users", "data_protection": "Encrypt sensitive customer information", "audit_logging": "Log all document generation activities", "digital_signatures": "Support for digital signature integration"}, "performance": {"generation_speed": "Target < 2 seconds per document", "concurrent_generation": "Support multiple simultaneous requests", "caching": "<PERSON><PERSON> frequently used templates and data", "optimization": "Optimize PDF size without quality loss"}}