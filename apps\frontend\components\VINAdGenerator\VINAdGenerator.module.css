.vinAdGenerator {
  background: var(--bg-primary);
  border-radius: 12px;
  padding: 24px;
  margin: 20px 0;
  box-shadow: var(--card-shadow-hover);
  border: 1px solid var(--border-primary);
}

.header {
  margin-bottom: 24px;
  text-align: center;
}

.header h3 {
  color: var(--text-primary);
  margin: 0 0 8px 0;
  font-size: 1.5rem;
}

.header p {
  color: var(--text-secondary);
  margin: 0;
  font-size: 0.95rem;
}

.section {
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--border-primary);
}

.section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.section h4 {
  color: var(--text-primary);
  margin: 0 0 16px 0;
  font-size: 1.1rem;
  font-weight: 600;
}

/* VIN Input */
.vinInput {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.vinField {
  padding: 12px 16px;
  border: 2px solid var(--border-primary, #e0e0e0);
  border-radius: 8px;
  font-size: 1.1rem;
  font-family: 'Courier New', monospace;
  letter-spacing: 1px;
  text-transform: uppercase;
  transition: border-color 0.2s ease;
  background: var(--bg-primary);
  color: var(--text-primary);
}

.vinField:focus {
  outline: none;
  border-color: var(--brand-red, #dc2626);
}

.vinStatus {
  display: flex;
  justify-content: flex-end;
}

.valid {
  color: var(--success, #059669);
  font-weight: 600;
  font-size: 0.9rem;
}

.invalid {
  color: var(--error, #dc2626);
  font-weight: 600;
  font-size: 0.9rem;
}

/* Platform Selection */
.platformGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.platformOption {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.platformOption:hover {
  border-color: #dc2626;
  background: rgba(220, 38, 38, 0.05);
}

.platformOption input[type="checkbox"] {
  transform: scale(1.2);
}

.platformIcon {
  font-size: 1.2rem;
}

.platformName {
  font-weight: 600;
  color: #333;
}

/* Generation Options */
.optionsGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  align-items: start;
}

.optionGroup {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.optionGroup label {
  font-weight: 600;
  color: #333;
  font-size: 0.95rem;
}

.select {
  padding: 10px 12px;
  border: 2px solid #e0e0e0;
  border-radius: 6px;
  font-size: 0.95rem;
  background: white;
  cursor: pointer;
}

.select:focus {
  outline: none;
  border-color: #dc2626;
}

.checkboxGroup {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.checkbox {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.checkbox input[type="checkbox"] {
  transform: scale(1.1);
}

.checkbox span {
  font-size: 0.95rem;
  color: #333;
}

/* Additional Features */
.featuresInput {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.featureInput {
  flex: 1;
  padding: 10px 12px;
  border: 2px solid #e0e0e0;
  border-radius: 6px;
  font-size: 0.95rem;
}

.featureInput:focus {
  outline: none;
  border-color: #dc2626;
}

.addFeatureBtn {
  background: #dc2626;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s ease;
}

.addFeatureBtn:hover:not(:disabled) {
  background: #b91c1c;
}

.addFeatureBtn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.featuresList {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.featureTag {
  background: rgba(220, 38, 38, 0.1);
  color: #dc2626;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 6px;
}

.removeFeature {
  background: none;
  border: none;
  color: #dc2626;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.removeFeature:hover {
  background: rgba(220, 38, 38, 0.2);
}

/* Generate Button */
.generateSection {
  text-align: center;
  margin: 24px 0;
}

.generateButton {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  color: white;
  border: none;
  padding: 16px 32px;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 200px;
}

.generateButton:hover:not(:disabled) {
  background: linear-gradient(135deg, #b91c1c, #991b1b);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
}

.generateButton:disabled {
  background: var(--text-tertiary, #ccc);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Results */
.results {
  background: none;
  border-radius: 0;
  padding: 20px 0;
  border: none;
  border-top: 1px solid #e2e8f0;
}

.results h4 {
  margin: 0 0 20px 0;
  color: #333;
}

.vehicleInfo {
  background: none;
  padding: 16px 0;
  border-radius: 0;
  margin-bottom: 20px;
  border: none;
  border-bottom: 1px solid #e2e8f0;
}

.vehicleInfo h5 {
  margin: 0 0 8px 0;
  color: #333;
}

.vehicleInfo p {
  margin: 0;
  color: #666;
  font-weight: 500;
}

.platformAds {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.platformAd {
  background: none;
  border-radius: 0;
  padding: 20px 0;
  border: none;
  border-bottom: 1px solid #e2e8f0;
}

.platformHeader h5 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 1.1rem;
}

.adContent {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.adSection {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.adSection label {
  font-weight: 600;
  color: #333;
  font-size: 0.95rem;
}

.copyableContent {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.copyableContent p {
  background: rgba(248, 250, 252, 0.8);
  padding: 12px;
  border-radius: 4px;
  margin: 0;
  font-weight: 600;
  color: #333;
}

.description {
  background: rgba(248, 250, 252, 0.8);
  padding: 12px;
  border-radius: 4px;
  margin: 0;
  white-space: pre-wrap;
  font-family: inherit;
  font-size: 0.95rem;
  line-height: 1.5;
  color: #333;
}

.copyBtn {
  background: #059669;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background 0.2s ease;
  align-self: flex-start;
}

.copyBtn:hover {
  background: #047857;
}

.error {
  color: #dc2626;
  font-weight: 600;
}

/* Mobile Tip */
.mobileTip {
  background: rgba(59, 130, 246, 0.1);
  border-radius: 8px;
  padding: 16px;
  border-left: 4px solid #3b82f6;
  margin-top: 20px;
}

.mobileTip h4 {
  color: #1e40af;
  margin: 0 0 8px 0;
  font-size: 1rem;
}

.mobileTip p {
  color: #1e40af;
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.5;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .vinAdGenerator {
    padding: 16px;
    margin: 16px 0;
  }

  .platformGrid {
    grid-template-columns: 1fr;
  }

  .optionsGrid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .featuresInput {
    flex-direction: column;
  }

  .generateButton {
    width: 100%;
    min-width: auto;
  }

  .copyableContent {
    gap: 12px;
  }

  .copyBtn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .header h3 {
    font-size: 1.3rem;
  }

  .section h4 {
    font-size: 1rem;
  }

  .vinField {
    font-size: 1rem;
    padding: 10px 12px;
  }

  .platformOption {
    padding: 10px 12px;
  }

  .generateButton {
    padding: 12px 24px;
    font-size: 1rem;
  }
}
