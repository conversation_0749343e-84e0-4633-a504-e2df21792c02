import { NextResponse } from 'next/server';
import connectToDatabase from '../../../lib/dbConnect';
import VehicleAttributes from '../../../lib/models/VehicleAttributes';
import { getFallbackAttributes } from '../../../lib/getVehicleAttributes';

export async function GET() {
  try {
    await connectToDatabase();

    // Fetch all vehicle attributes from the database
    const attributes = await VehicleAttributes.find({}).select('type values -_id');

    // Transform the data into a more usable format for the frontend
    const attributesMap = {};
    attributes.forEach(attr => {
      attributesMap[attr.type] = attr.values;
    });

    // If no data found in database, return fallback data
    if (Object.keys(attributesMap).length === 0) {
      const fallbackData = getFallbackAttributes();

      // Process fallback data to handle value/display objects
      const processedFallbackData = {};
      for (const [type, values] of Object.entries(fallbackData)) {
        if (Array.isArray(values) && values.length > 0) {
          if (typeof values[0] === 'object' && values[0].value && values[0].display) {
            // For objects with value/display, return the objects as-is for the frontend to handle
            processedFallbackData[type] = values;
          } else {
            // For simple string arrays
            processedFallbackData[type] = values;
          }
        } else {
          processedFallbackData[type] = values;
        }
      }

      return NextResponse.json({
        success: true,
        data: processedFallbackData
      });
    }

    return NextResponse.json({
      success: true,
      data: attributesMap
    });
  } catch (error) {
    console.error('Error fetching vehicle attributes:', error);

    // Return fallback data on error
    const fallbackData = getFallbackAttributes();
    const processedFallbackData = {};
    for (const [type, values] of Object.entries(fallbackData)) {
      if (Array.isArray(values) && values.length > 0) {
        if (typeof values[0] === 'object' && values[0].value && values[0].display) {
          // For objects with value/display, return the objects as-is for the frontend to handle
          processedFallbackData[type] = values;
        } else {
          processedFallbackData[type] = values;
        }
      } else {
        processedFallbackData[type] = values;
      }
    }

    return NextResponse.json({
      success: true,
      data: processedFallbackData
    });
  }
}
