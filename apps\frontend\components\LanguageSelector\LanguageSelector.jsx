'use client';

import { useLanguage } from '../../contexts/LanguageContext';
import styles from './LanguageSelector.module.css';

const languages = [
  { code: 'en', name: 'English', flag: '🇺🇸' },
  { code: 'es', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', flag: '🇪🇸' },
  { code: 'fr', name: 'Français', flag: '🇫🇷' },
  { code: 'ur', name: 'اردو', flag: '🇵🇰' },
  { code: 'ar', name: 'العربية', flag: '🇸🇦' },
];

export default function LanguageSelector({
  showLabel = true,
  showFlags = true,
  variant = 'select', // 'select' or 'dropdown'
  className = '',
  collapsed = false
}) {
  const { currentLanguage, changeLanguage, t } = useLanguage();

  const handleLanguageChange = (e) => {
    changeLanguage(e.target.value);
  };

  const currentLang = languages.find(lang => lang.code === currentLanguage) || languages[0];

  if (variant === 'select') {
    return (
      <div className={`${styles.container} ${className}`}>
        {showLabel && (
          <label className={styles.label}>
            {t('settings.language', 'Language')}
          </label>
        )}
        <select
          value={currentLanguage}
          onChange={handleLanguageChange}
          className={`formInput ${collapsed ? 'collapsed-select' : ''}`}
          aria-label={t('settings.language', 'Select language')}
          style={collapsed ? { width: '50px', textAlign: 'center' } : {}}
        >
          {languages.map((lang) => (
            <option key={lang.code} value={lang.code}>
              {collapsed
                ? lang.code.toUpperCase()
                : showFlags
                  ? `${lang.flag} ${lang.name}`
                  : lang.name
              }
            </option>
          ))}
        </select>
      </div>
    );
  }

  // Dropdown variant (for more advanced styling)
  return (
    <div className={`${styles.container} ${className}`}>
      {showLabel && (
        <label className={styles.label}>
          {t('settings.language', 'Language')}
        </label>
      )}
      <div className={styles.dropdown}>
        <button className={styles.dropdownButton}>
          {showFlags && currentLang.flag} {currentLang.name}
          <svg className={styles.chevron} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </button>
        {/* Add dropdown menu implementation here if needed */}
      </div>
    </div>
  );
}
