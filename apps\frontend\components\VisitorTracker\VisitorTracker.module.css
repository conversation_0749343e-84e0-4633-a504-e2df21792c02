/* Visitor Tracker <PERSON> */

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  backdrop-filter: blur(4px);
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  max-width: 95vw;
  max-height: 90vh;
  width: 1200px;
  display: flex;
  flex-direction: column;
  animation: slideIn 0.3s ease;
  transition: background 0.3s ease, border-color 0.3s ease;
}

/* Dark mode styling for modal */
[data-theme="dark"] .modal {
  background: #161b22;
  border: 1px solid #333;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e2e8f0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px 12px 0 0;
  transition: background 0.3s ease, border-color 0.3s ease;
}

/* Dark mode styling for header */
[data-theme="dark"] .header {
  background: #0d1117;
  border-bottom: 1px solid #333;
}

.header h2 {
  margin: 0;
  color: #2d3748;
  font-size: 1.5rem;
  font-weight: 600;
  transition: color 0.3s ease;
}

/* Dark mode styling for header text */
[data-theme="dark"] .header h2 {
  color: #ffffff;
}

.closeBtn {
  background: none;
  border: none;
  font-size: 2rem;
  color: #718096;
  cursor: pointer;
  padding: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

/* Dark mode styling for close button */
[data-theme="dark"] .closeBtn {
  color: #cbd5e1;
}

[data-theme="dark"] .closeBtn:hover {
  color: #ffffff;
  background: #333;
}

.closeBtn:hover {
  background: rgba(0, 0, 0, 0.1);
  color: #2d3748;
}

/* Stats Section */
.statsSection {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  padding: 1.5rem 2rem;
  background: #f8f9fa;
  border-bottom: 1px solid #e2e8f0;
}

.statCard {
  background: white;
  padding: 1rem;
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.statCard:hover {
  transform: translateY(-2px);
}

.statNumber {
  font-size: 2rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 0.25rem;
}

.statLabel {
  font-size: 0.9rem;
  color: #718096;
  font-weight: 500;
}

/* Filters Section */
.filtersSection {
  display: flex;
  gap: 1rem;
  align-items: center;
  padding: 1rem 2rem;
  background: white;
  border-bottom: 1px solid #e2e8f0;
  flex-wrap: wrap;
}

.filterGroup {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filterGroup label {
  font-size: 0.9rem;
  font-weight: 500;
  color: #4a5568;
  white-space: nowrap;
}

.select {
  padding: 0.5rem 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 0.9rem;
  background: white;
  color: #2d3748;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.select:focus {
  outline: none;
  border-color: #4299e1;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}

.refreshBtn {
  background: #4299e1;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  margin-left: auto;
}

.refreshBtn:hover {
  background: #3182ce;
}

/* Content */
.content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.loading, .error, .noData {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
  flex: 1;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e2e8f0;
  border-top: 4px solid #4299e1;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error {
  color: #e53e3e;
}

.retryBtn {
  background: #e53e3e;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  margin-top: 1rem;
  transition: background-color 0.2s ease;
}

.retryBtn:hover {
  background: #c53030;
}

/* Visitors List */
.visitorsList {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.tableWrapper {
  flex: 1;
  overflow: auto;
  padding: 0 2rem 2rem 2rem;
}

.table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;
}

.table th {
  background: #f8f9fa;
  color: #4a5568;
  font-weight: 600;
  padding: 0.75rem;
  text-align: left;
  border-bottom: 2px solid #e2e8f0;
  position: sticky;
  top: 0;
  z-index: 1;
}

.table td {
  padding: 0.75rem;
  border-bottom: 1px solid #e2e8f0;
  color: #2d3748;
}

.table tr:hover {
  background: #f7fafc;
}

.ipAddress {
  font-family: 'Courier New', monospace;
  font-weight: 600;
  color: #4299e1;
}

.visitCount {
  text-align: center;
  font-weight: 600;
  color: #48bb78;
}

.userAgent {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #718096;
  font-size: 0.8rem;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .modal {
    width: 95vw;
    height: 90vh;
    margin: 5vh auto;
  }

  .header {
    padding: 1rem;
  }

  .header h2 {
    font-size: 1.25rem;
  }

  .statsSection {
    grid-template-columns: repeat(2, 1fr);
    padding: 1rem;
    gap: 0.75rem;
  }

  .statCard {
    padding: 0.75rem;
  }

  .statNumber {
    font-size: 1.5rem;
  }

  .filtersSection {
    padding: 1rem;
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }

  .filterGroup {
    justify-content: space-between;
  }

  .refreshBtn {
    margin-left: 0;
    align-self: center;
  }

  .tableWrapper {
    padding: 0 1rem 1rem 1rem;
  }

  .table {
    font-size: 0.8rem;
  }

  .table th,
  .table td {
    padding: 0.5rem 0.25rem;
  }

  .userAgent {
    max-width: 100px;
  }
}

@media (max-width: 480px) {
  .statsSection {
    grid-template-columns: 1fr;
  }

  .table th,
  .table td {
    padding: 0.4rem 0.2rem;
    font-size: 0.75rem;
  }

  .userAgent {
    display: none;
  }
}
