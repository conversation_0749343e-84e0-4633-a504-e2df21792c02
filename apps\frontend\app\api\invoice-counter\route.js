import { NextResponse } from 'next/server';
import { getCounterStatus, resetCounterForNewYear } from '../../../lib/utils/invoiceGenerator.js';

/**
 * GET /api/invoice-counter - Get current counter status
 * Query params: mode=test|prod
 */
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const mode = searchParams.get('mode') || 'test';

    // Validate mode parameter
    if (!['test', 'prod'].includes(mode)) {
      return NextResponse.json({
        success: false,
        error: 'Invalid mode. Must be "test" or "prod"'
      }, { status: 400 });
    }

    const counterStatus = await getCounterStatus(mode);

    return NextResponse.json({
      success: true,
      data: counterStatus
    });

  } catch (error) {
    console.error('❌ Counter status error:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

/**
 * POST /api/invoice-counter - Reset counter (admin only)
 * Body: { mode: "test"|"prod", action: "reset" }
 */
export async function POST(request) {
  try {
    const body = await request.json();
    const { mode, action } = body;

    // Validate mode parameter
    if (!['test', 'prod'].includes(mode)) {
      return NextResponse.json({
        success: false,
        error: 'Invalid mode. Must be "test" or "prod"'
      }, { status: 400 });
    }

    // Validate action parameter
    if (action !== 'reset') {
      return NextResponse.json({
        success: false,
        error: 'Invalid action. Must be "reset"'
      }, { status: 400 });
    }

    // Reset counter to 0
    await resetCounterForNewYear(mode);

    // Get updated status
    const counterStatus = await getCounterStatus(mode);

    console.log(`🔄 Admin reset counter for ${mode} mode`);

    return NextResponse.json({
      success: true,
      message: `Counter reset successfully for ${mode} mode. Next invoice will be BOS-${new Date().getFullYear()}-00001`,
      data: counterStatus
    });

  } catch (error) {
    console.error('❌ Counter reset error:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}
