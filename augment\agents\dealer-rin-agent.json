{"name": "Dealer RIN Management Agent", "description": "AI agent specialized in managing dealer RIN (Retail Identification Number) information for compliance forms and business operations", "version": "1.0.0", "capabilities": ["Manage dealer RIN information", "Update dealer business details", "Validate RIN format and authenticity", "Generate compliance forms with RIN data", "Handle multi-location dealer setups", "Manage dealer licensing information", "Track RIN expiration dates", "Generate RIN-related reports"], "context": {"domain": "Dealer Management & Compliance", "platform": "FazeNAuto", "regulations": "OMVIC (Ontario Motor Vehicle Industry Council)", "database": "MongoDB", "framework": "Next.js"}, "prompts": {"update_rin_info": {"description": "Update dealer RIN information and business details", "template": "Update dealer RIN information with the following details:\n\n**Dealer Information:**\n- RIN Number: {rin_number}\n- Business Name: {business_name}\n- Operating Name: {operating_name}\n- Business Address: {business_address}\n- Mailing Address: {mailing_address}\n- Phone: {phone}\n- Email: {email}\n- Website: {website}\n\n**Licensing Information:**\n- License Type: {license_type}\n- Issue Date: {issue_date}\n- Expiry Date: {expiry_date}\n- Issuing Authority: {issuing_authority}\n\n**Requirements:**\n1. Validate RIN format (OMVIC standards)\n2. Verify all required business information\n3. Update database records\n4. Generate audit trail\n5. Check for expiration warnings\n6. Update related compliance forms\n\nGenerate the RIN update implementation.", "variables": ["rin_number", "business_name", "operating_name", "business_address", "mailing_address", "phone", "email", "website", "license_type", "issue_date", "expiry_date", "issuing_authority"]}, "validate_rin": {"description": "Validate RIN number format and status", "template": "Validate RIN number: {rin_number}\n\n**Validation Requirements:**\n1. Check RIN format (5-digit number)\n2. Verify with OMVIC database (if API available)\n3. Check expiration status\n4. Validate against business registration\n5. Flag any compliance issues\n6. Return validation status and details\n\n**Output Format:**\n- Valid: true/false\n- Status: active/expired/suspended/invalid\n- Expiry Date: if applicable\n- Warnings: any compliance issues\n- Business Details: if found\n\nGenerate RIN validation implementation.", "variables": ["rin_number"]}, "generate_compliance_form": {"description": "Generate compliance forms with RIN information", "template": "Generate {form_type} compliance form with dealer RIN information:\n\n**Form Details:**\n- Form Type: {form_type}\n- RIN Number: {rin_number}\n- Transaction ID: {transaction_id}\n- Vehicle Information: {vehicle_info}\n- Customer Information: {customer_info}\n\n**Requirements:**\n1. Include all required RIN fields\n2. Auto-populate dealer information\n3. Ensure OMVIC compliance\n4. Generate PDF format\n5. Include digital signatures\n6. Store form record in database\n7. Generate unique form number\n\nGenerate the compliance form implementation.", "variables": ["form_type", "rin_number", "transaction_id", "vehicle_info", "customer_info"]}, "check_rin_expiry": {"description": "Check RIN expiration and send alerts", "template": "Check RIN expiration status for all dealers:\n\n**Check Parameters:**\n- Warning Period: {warning_days} days before expiry\n- Alert Recipients: {alert_recipients}\n- Check Frequency: {check_frequency}\n\n**Requirements:**\n1. Query all dealer RIN records\n2. Calculate days until expiry\n3. Generate expiry warnings\n4. Send email notifications\n5. Update dashboard alerts\n6. Log expiry checks\n7. Generate renewal reminders\n\nGenerate RIN expiry monitoring implementation.", "variables": ["warning_days", "alert_recipients", "check_frequency"]}}, "workflows": {"dealer_onboarding": {"description": "Complete dealer onboarding with RIN setup", "steps": [{"step": 1, "action": "collect_dealer_info", "description": "Gather all required dealer information"}, {"step": 2, "action": "validate_rin", "description": "Validate RIN number with OMVIC"}, {"step": 3, "action": "verify_business_registration", "description": "Verify business registration details"}, {"step": 4, "action": "create_dealer_profile", "description": "Create dealer profile in system"}, {"step": 5, "action": "setup_compliance_templates", "description": "Setup compliance form templates"}, {"step": 6, "action": "configure_notifications", "description": "Setup expiry and compliance notifications"}]}, "rin_renewal_process": {"description": "Handle RIN renewal process", "steps": [{"step": 1, "action": "identify_expiring_rins", "description": "Identify RINs approaching expiry"}, {"step": 2, "action": "send_renewal_notices", "description": "Send renewal notifications to dealers"}, {"step": 3, "action": "collect_renewal_documents", "description": "Collect updated licensing documents"}, {"step": 4, "action": "update_rin_records", "description": "Update RIN information with new dates"}, {"step": 5, "action": "generate_renewal_report", "description": "Generate renewal completion report"}]}}, "data_models": {"dealer_rin": {"required_fields": ["rin_number", "business_name", "business_address", "phone", "email", "license_type", "issue_date", "expiry_date"], "optional_fields": ["operating_name", "mailing_address", "website", "contact_person", "secondary_phone", "fax", "notes"], "validation_rules": {"rin_number": "Must be 5-digit number, unique", "business_name": "Required, max 100 characters", "phone": "Valid Canadian phone number format", "email": "Valid email format", "expiry_date": "Must be future date"}}, "compliance_form": {"required_fields": ["form_type", "form_number", "rin_number", "transaction_date", "vehicle_vin", "customer_name", "dealer_signature"], "form_types": ["bill_of_sale", "omvic_disclosure", "hst_form", "warranty_disclosure", "inspection_certificate"]}}, "api_endpoints": {"get_rin_info": "GET /api/dealers/rin/[rin_number]", "update_rin": "PUT /api/dealers/rin/[rin_number]", "validate_rin": "POST /api/dealers/rin/validate", "list_dealers": "GET /api/dealers", "expiry_check": "GET /api/dealers/rin/expiry-check", "generate_form": "POST /api/compliance/forms/generate"}, "integrations": {"omvic_api": {"purpose": "RIN validation and verification", "endpoint": "https://www.omvic.on.ca/api/dealer-lookup", "note": "API may not be publicly available - manual verification required"}, "email_service": {"purpose": "Send expiry notifications and alerts", "provider": "SMTP or email service provider"}, "pdf_generator": {"purpose": "Generate compliance forms with RIN information", "library": "PDFKit or similar"}}, "compliance_requirements": {"omvic_standards": {"rin_display": "RIN must be displayed on all advertising and documentation", "form_requirements": "All sales forms must include valid RIN", "record_keeping": "Maintain RIN records for audit purposes", "expiry_monitoring": "Monitor and renew RIN before expiry"}, "required_forms": [{"name": "Bill of Sale", "rin_required": true, "fields": ["dealer_name", "rin_number", "business_address"]}, {"name": "OMVIC Disclosure", "rin_required": true, "fields": ["rin_number", "dealer_signature", "disclosure_date"]}]}, "validation_rules": {"rin_format": {"pattern": "^[0-9]{5}$", "description": "5-digit numeric code"}, "business_address": {"required_fields": ["street", "city", "province", "postal_code"], "province_validation": "Must be valid Canadian province"}, "phone_format": {"pattern": "^\\+?1?[2-9][0-9]{2}[2-9][0-9]{2}[0-9]{4}$", "description": "Valid North American phone number"}}, "error_handling": {"invalid_rin": "Return specific validation error messages", "expired_rin": "Flag as expired, require renewal", "missing_info": "Identify missing required fields", "api_failures": "Graceful degradation, manual verification option"}, "notifications": {"expiry_warnings": {"90_days": "Initial renewal reminder", "30_days": "Urgent renewal notice", "7_days": "Final renewal warning", "expired": "RIN expired notification"}, "compliance_alerts": {"missing_forms": "Alert when required forms are missing RIN", "invalid_usage": "Alert when RIN is used incorrectly", "audit_requests": "Notify when audit documentation is requested"}}, "reporting": {"rin_status_report": "Current status of all dealer RINs", "expiry_forecast": "Upcoming RIN expirations", "compliance_audit": "Compliance form usage and RIN inclusion", "renewal_tracking": "RIN renewal completion status"}, "security": {"access_control": "Restrict RIN management to authorized personnel", "audit_logging": "Log all RIN information changes", "data_encryption": "Encrypt sensitive dealer information", "backup_procedures": "Regular backup of RIN data"}}