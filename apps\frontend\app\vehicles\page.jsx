
'use client';

import { useEffect, useState, Suspense } from 'react';
import VehicleCard from '../../components/VehicleCard/VehicleCard';
import styles from './VehiclesPage.module.css';

// Force dynamic rendering to avoid build-time issues
export const dynamic = 'force-dynamic';

export default function VehiclesPage() {
  const [vehicles, setVehicles] = useState([]);

  useEffect(() => {
    const fetchVehicles = async () => {
      try {
        const response = await fetch('/api/vehicles');
        const data = await response.json();
        if (data.success) {
          setVehicles(data.data);
        }
      } catch (error) {
        console.error('Error fetching vehicles:', error);
      }
    };

    fetchVehicles();
  }, []);

  return (
    <Suspense fallback={<div>Loading...</div>}>
      <div className={styles.vehiclesPage}>
        <h1>Our Vehicles</h1>
        <div className={styles.vehicleGrid}>
          {vehicles.map((vehicle) => (
            <VehicleCard key={vehicle.vin} vehicle={vehicle} showCompareButton={true} />
          ))}
        </div>
      </div>
    </Suspense>
  );
}
