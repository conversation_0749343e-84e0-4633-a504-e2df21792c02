# FazeNAuto Frontend

This is the Next.js frontend application for the FazeNAuto dealership platform.

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ 
- npm or yarn

### Installation
```bash
# From the frontend directory
npm install

# Or from the root directory
npm run install-all
```

### Development
```bash
# Start the development server
npm run dev

# Or from root directory
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to view the application.

## 📁 Project Structure

```
apps/frontend/
├── src/
│   ├── app/                 # Next.js App Router pages
│   │   ├── admin/          # Admin dashboard pages
│   │   ├── inventory/      # Vehicle inventory pages
│   │   ├── api/           # API routes (Next.js API)
│   │   └── ...
│   ├── components/         # Reusable React components
│   │   ├── Navbar/
│   │   ├── VehicleCard/
│   │   ├── AdminProtectedLayout/
│   │   └── ...
│   ├── styles/            # CSS stylesheets
│   └── assets/            # Static assets (images, logos)
├── public/                # Public static files
├── .env.local            # Environment variables
├── next.config.mjs       # Next.js configuration
├── jsconfig.json         # JavaScript configuration
└── package.json          # Dependencies and scripts
```

## 🔧 Configuration

### Environment Variables
Create a `.env.local` file with:

```env
# Frontend Environment Variables
NEXT_PUBLIC_BASE_URL=https://fazenauto.com

# AWS Configuration (for image display)
CUSTOM_AWS_REGION=us-east-1
CUSTOM_AWS_S3_BUCKET_NAME=fazenauto-vehicle-images

# Authentication Configuration
ADMIN_SECRET=your_admin_secret
AUTHORIZED_EMAILS=<EMAIL>,<EMAIL>
```

### Import Aliases
The project uses path aliases configured in `jsconfig.json`:

- `@/*` - Points to `./src/*`
- `@/lib/*` - Points to `../api/lib/*` (backend utilities)
- `@/models/*` - Points to `../api/domains/*/models/*` (backend models)
- `@/domains/*` - Points to `../api/domains/*` (backend domains)

## 🎨 Styling

The project uses **CSS-only styling** (no Tailwind or styled-components):
- Global styles in `src/styles/global.css`
- Component-specific CSS modules (e.g., `VehicleCard.module.css`)
- Responsive design with CSS Grid and Flexbox

## 📱 Features

### Public Pages
- **Home** - Landing page with hero section
- **Inventory** - Browse available vehicles
- **Vehicle Details** - Individual vehicle information
- **Contact** - Contact form and information
- **Services** - Dealership services

### Admin Dashboard
- **Vehicle Management** - Add, edit, delete vehicles
- **User Management** - Manage dealers and admins
- **Syndication Panel** - Export to marketplaces
- **Facebook Integration** - Social media posting

### Authentication
- Role-based access (admin/dealer)
- Protected routes for admin functionality
- Email-based authorization

## 🔗 API Integration

The frontend communicates with backend domains through API routes:
- `/api/vehicles` - Vehicle CRUD operations
- `/api/auth` - Authentication endpoints
- `/api/syndication` - Marketplace syndication
- `/api/export` - CSV export functionality

## 🚀 Deployment

### AWS Amplify
The application is configured for AWS Amplify deployment:
1. Build configuration in `infra/amplify.yml`
2. Automatic builds from git commits
3. Environment variables configured in Amplify console

### Build Commands
```bash
# Production build
npm run build

# Start production server
npm run start

# Lint code
npm run lint
```

## 🧪 Development Tips

1. **Hot Reload** - Changes auto-refresh in development
2. **API Routes** - Test API endpoints at `/api/*`
3. **Component Development** - Use React DevTools for debugging
4. **CSS Debugging** - Use browser dev tools for styling

## 📦 Dependencies

### Core
- **Next.js 15** - React framework
- **React 19** - UI library
- **React Icons** - Icon components

### UI Components
- **@tanstack/react-table** - Data tables
- **hamburger-react** - Mobile menu

### Backend Integration
- **Mongoose** - MongoDB ODM (for API routes)
- **@aws-sdk/client-s3** - AWS S3 integration
- **bcryptjs** - Password hashing

## 🔍 Troubleshooting

### Common Issues
1. **Import Errors** - Check path aliases in `jsconfig.json`
2. **API Errors** - Verify backend models are accessible
3. **Build Errors** - Check environment variables
4. **Styling Issues** - Verify CSS module imports

### Debug Mode
```bash
# Enable debug logging
DEBUG=* npm run dev
```

## 🤝 Contributing

1. Follow the existing code structure
2. Use CSS modules for styling
3. Test API integrations thoroughly
4. Update documentation for new features
