/**
 * Client-side helper function to fetch vehicle attributes from the API
 * Returns organized dropdown data for the vehicle upload form
 */
export async function getVehicleAttributes() {
  try {
    const response = await fetch('/api/vehicle-attributes');
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const result = await response.json();
    
    if (!result.success) {
      throw new Error(result.error || 'Failed to fetch vehicle attributes');
    }
    
    return {
      success: true,
      data: result.data
    };
  } catch (error) {
    console.error('Error fetching vehicle attributes:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Get fallback options for when the API call fails
 * These are the original hardcoded values as backup
 */
export function getFallbackAttributes() {
  return {
    engine: [
      '4-Cylinder',
      'V6',
      'V8',
      'V10',
      'V12',
      'Hybrid',
      'Electric',
      'Diesel',
      'Turbo 4-Cylinder',
      'Twin-Turbo V6',
      'Twin-Turbo V8'
    ],
    transmission: [
      'Manual',
      'Automatic',
      'CVT',
      'Semi-Automatic'
    ],
    drivetrain: [
      { value: 'FWD', display: 'Front-Wheel Drive (FWD)' },
      { value: 'RWD', display: 'Rear-Wheel Drive (RWD)' },
      { value: 'AWD', display: 'All-Wheel Drive (AWD)' },
      { value: '4WD', display: 'Four-Wheel Drive (4WD)' }
    ],
    fueltype: [
      'Gasoline',
      'Diesel',
      'Hybrid',
      'Electric',
      'Flex Fuel'
    ],
    bodyclass: [
      'Sedan',
      'SUV',
      'Truck',
      'Coupe',
      'Convertible',
      'Hatchback',
      'Wagon',
      'Van',
      'Minivan',
      'Crossover',
      'Pickup',
      'Roadster'
    ],
    doors: [
      { value: '2', display: '2 Doors' },
      { value: '3', display: '3 Doors' },
      { value: '4', display: '4 Doors' },
      { value: '5', display: '5 Doors' }
    ],
    cylinders: [
      { value: '3', display: '3 Cylinders' },
      { value: '4', display: '4 Cylinders' },
      { value: '6', display: '6 Cylinders' },
      { value: '8', display: '8 Cylinders' },
      { value: '10', display: '10 Cylinders' },
      { value: '12', display: '12 Cylinders' }
    ]
  };
}
