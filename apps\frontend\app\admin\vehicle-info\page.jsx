'use client';

import { useState } from 'react';
import ProtectedRoute from '../../../components/ProtectedRoute/ProtectedRoute';
import VINScanner from '../../../components/VINScanner/VINScanner';
import { decodeVIN, validateVIN } from '../../../utils/vinDecoder';
import { useAuth } from '../../../lib/auth';
import { useLanguage } from '../../../contexts/LanguageContext';
import styles from './VehicleInfo.module.css';
import marketStyles from '../../../styles/shared-market.module.css';

export default function VehicleInfoPage() {
  const { canAccessMarketCheck, isDealer, isAdmin, user } = useAuth();
  const { t } = useLanguage();
  const [showVINScanner, setShowVINScanner] = useState(false);
  const [vinInput, setVinInput] = useState('');
  const [vehicleData, setVehicleData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [vehicleHistory, setVehicleHistory] = useState(null);
  const [recallsData, setRecallsData] = useState(null);
  const [marketData, setMarketData] = useState(null);
  const [marketDataLoading, setMarketDataLoading] = useState(false);

  // Handle VIN input change
  const handleVinInputChange = (e) => {
    setVinInput(e.target.value.toUpperCase());
    setError('');
  };

  // Handle VIN detected from scanner
  const handleVINDetected = async (detectedVIN) => {
    setVinInput(detectedVIN);
    await lookupVehicleInfo(detectedVIN);
  };

  // Lookup vehicle information
  const lookupVehicleInfo = async (vin = vinInput) => {
    if (!vin) {
      setError('Please enter a VIN');
      return;
    }

    if (!validateVIN(vin)) {
      setError('Invalid VIN format. VIN must be 17 characters long and contain only letters and numbers (no I, O, or Q).');
      return;
    }

    setLoading(true);
    setError('');
    setVehicleData(null);
    setVehicleHistory(null);
    setRecallsData(null);
    setMarketData(null);

    try {
      console.log('🔍 Starting VIN lookup for:', vin);

      // Use the new vehicle-info API route that handles both NHTSA and MarketCheck data
      const response = await fetch('/api/vehicle-info', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ vin }),
      });

      if (!response.ok) {
        throw new Error(`API error: ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        console.log('✅ Vehicle info fetched successfully:', result);
        setVehicleData(result.vehicleInfo);
        setRecallsData(result.recalls);

        // Debug auth status
        console.log('🔍 Auth Debug - User:', user);
        console.log('🔍 Auth Debug - canAccessMarketCheck:', canAccessMarketCheck);
        console.log('🔍 Auth Debug - isAdmin:', isAdmin);
        console.log('🔍 Auth Debug - isDealer:', isDealer);

        // Set MarketCheck data if available and user has access
        if (result.marketData && canAccessMarketCheck) {
          console.log('✅ MarketCheck data available for admin/dealer');
          setMarketData(result.marketData);
        } else if (canAccessMarketCheck) {
          console.log('⚠️ MarketCheck data not available:', result.marketData);
        } else {
          console.log('👤 User does not have MarketCheck access');
        }

        // Try to get vehicle history (placeholder for now)
        await getVehicleHistory(vin);
      } else {
        setError(`VIN Decoder Error: ${result.error}`);
      }
    } catch (err) {
      console.error('❌ VIN lookup failed:', err);
      setError(`Failed to lookup vehicle information: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Get vehicle history using our API
  const getVehicleHistory = async (vin) => {
    try {
      const response = await fetch('/api/vehicle-history', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ vin }),
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setVehicleHistory(result.data);
        } else {
          console.error('Vehicle history API error:', result.error);
          setVehicleHistory({
            hasHistory: false,
            error: result.error,
            message: 'Unable to retrieve vehicle history at this time.'
          });
        }
      } else {
        throw new Error('API request failed');
      }
    } catch (error) {
      console.error('Vehicle history lookup error:', error);
      setVehicleHistory({
        hasHistory: false,
        error: 'Network error',
        message: 'Unable to connect to vehicle history service.'
      });
    }
  };

  const handleManualLookup = () => {
    lookupVehicleInfo();
  };

  return (
    <ProtectedRoute>
      <div className={styles.container}>
        <div className={styles.header}>
          <h1>{t('vehicle_lookup.title')}</h1>
          <p>{t('vehicle_lookup.subtitle')}</p>
        </div>

        <div className={styles.inputSection}>
          <div className={styles.vinInputGroup}>
            <input
              type="text"
              value={vinInput}
              onChange={handleVinInputChange}
              placeholder={t('vehicle_lookup.placeholder')}
              maxLength="17"
              className="formInput"
            />
            <button
              onClick={() => setShowVINScanner(true)}
              className={styles.scanButton}
            >
              <span>📷</span>
              <span>{t('vehicle_lookup.scan_vin')}</span>
            </button>
            <button
              onClick={handleManualLookup}
              disabled={loading || !vinInput || vinInput.length !== 17}
              className={styles.lookupButton}
            >
              {loading ? '🔄 ' + t('common.decoding') : '🔍 ' + t('vehicle_lookup.decode')}
            </button>
          </div>
          <div className={styles.characterCount}>
            {t('vehicle_lookup.characters_remaining')} {17 - vinInput.length}
          </div>
        </div>

        {error && (
          <div className={styles.error}>
            {error}
          </div>
        )}

        {vehicleData && (
          <div className={styles.resultsSection}>
            <div className={styles.vehicleInfo}>
              <h2>{t('vehicle_lookup.decoded_vehicle_info')} <span className={styles.vinDisplay}>({vinInput})</span></h2>
              <div className={styles.infoGrid}>
                <div className={styles.infoCard}>
                  <h3>{t('vehicle_lookup.basic_info')}</h3>
                  <div className={styles.infoItem}>
                    <span className={styles.label}>{t('vehicle.make')}</span>
                    <span className={styles.value}>{vehicleData.make || 'N/A'}</span>
                  </div>
                  <div className={styles.infoItem}>
                    <span className={styles.label}>{t('vehicle.model')}</span>
                    <span className={styles.value}>{vehicleData.model || 'N/A'}</span>
                  </div>
                  <div className={styles.infoItem}>
                    <span className={styles.label}>{t('vehicle.year')}</span>
                    <span className={styles.value}>{vehicleData.year || 'N/A'}</span>
                  </div>
                  <div className={styles.infoItem}>
                    <span className={styles.label}>Trim:</span>
                    <span className={styles.value}>{vehicleData.trim || 'N/A'}</span>
                  </div>
                  <div className={styles.infoItem}>
                    <span className={styles.label}>Body Class:</span>
                    <span className={styles.value}>{vehicleData.bodyClass || 'N/A'}</span>
                  </div>
                </div>

                <div className={styles.infoCard}>
                  <h3>Engine & Performance</h3>
                  <div className={styles.infoItem}>
                    <span className={styles.label}>Engine:</span>
                    <span className={styles.value}>{vehicleData.engine || 'N/A'}</span>
                  </div>
                  <div className={styles.infoItem}>
                    <span className={styles.label}>Cylinders:</span>
                    <span className={styles.value}>{vehicleData.engineCylinders || 'N/A'}</span>
                  </div>
                  <div className={styles.infoItem}>
                    <span className={styles.label}>Displacement:</span>
                    <span className={styles.value}>{vehicleData.engineLiters ? `${vehicleData.engineLiters}L` : 'N/A'}</span>
                  </div>
                  <div className={styles.infoItem}>
                    <span className={styles.label}>Displacement (CC):</span>
                    <span className={styles.value}>{vehicleData.displacementCC || 'N/A'}</span>
                  </div>
                  <div className={styles.infoItem}>
                    <span className={styles.label}>Displacement (CI):</span>
                    <span className={styles.value}>{vehicleData.displacementCI || 'N/A'}</span>
                  </div>
                  <div className={styles.infoItem}>
                    <span className={styles.label}>Engine Brake (hp) From:</span>
                    <span className={styles.value}>{vehicleData.horsepowerFrom || 'N/A'}</span>
                  </div>
                  <div className={styles.infoItem}>
                    <span className={styles.label}>Fuel Type:</span>
                    <span className={styles.value}>{vehicleData.fuelType || 'N/A'}</span>
                  </div>
                </div>

                <div className={styles.infoCard}>
                  <h3>Drivetrain & Transmission</h3>
                  <div className={styles.infoItem}>
                    <span className={styles.label}>Transmission:</span>
                    <span className={styles.value}>{vehicleData.transmission || 'N/A'}</span>
                  </div>
                  <div className={styles.infoItem}>
                    <span className={styles.label}>Drive Type:</span>
                    <span className={styles.value}>{vehicleData.driveType || 'N/A'}</span>
                  </div>
                  <div className={styles.infoItem}>
                    <span className={styles.label}>Doors:</span>
                    <span className={styles.value}>{vehicleData.doors || 'N/A'}</span>
                  </div>
                  <div className={styles.infoItem}>
                    <span className={styles.label}>Manufacturer:</span>
                    <span className={styles.value}>{vehicleData.manufacturer || 'N/A'}</span>
                  </div>
                  <div className={styles.infoItem}>
                    <span className={styles.label}>Plant City:</span>
                    <span className={styles.value}>{vehicleData.plantCity || 'N/A'}</span>
                  </div>
                  <div className={styles.infoItem}>
                    <span className={styles.label}>Plant Country:</span>
                    <span className={styles.value}>{vehicleData.plantCountry || 'N/A'}</span>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className={styles.actionButtons}>
                <button
                  className={styles.saveButton}
                  onClick={() => {
                    // TODO: Implement save vehicle functionality
                    alert('Save Vehicle functionality to be implemented');
                  }}
                >
                  Save Vehicle
                </button>
                <button
                  className={styles.clearButton}
                  onClick={() => {
                    setVinInput('');
                    setVehicleData(null);
                    setVehicleHistory(null);
                    setMarketData(null);
                    setError('');
                  }}
                >
                  Clear All
                </button>
              </div>
            </div>

            {vehicleHistory && (
              <div className={styles.historySection}>
                <h2>Vehicle History & Recalls</h2>

                {/* Recalls Section (Free NHTSA Data) */}
                {vehicleHistory.recalls && (
                  <div className={styles.recallsSection}>
                    <h3>Safety Recalls</h3>
                    {vehicleHistory.recalls.hasRecalls ? (
                      <div className={styles.recallsData}>
                        <p className={styles.recallCount}>
                          ⚠️ {vehicleHistory.recalls.count} recall(s) found
                        </p>
                        <div className={styles.recallsList}>
                          {vehicleHistory.recalls.recalls.slice(0, 3).map((recall, index) => (
                            <div key={index} className={styles.recallItem}>
                              <h4>{recall.Component}</h4>
                              <p><strong>Date:</strong> {recall.ReportReceivedDate}</p>
                              <p><strong>Summary:</strong> {recall.Summary}</p>
                            </div>
                          ))}
                        </div>
                        <p className={styles.dataSource}>Source: {vehicleHistory.recalls.source}</p>
                      </div>
                    ) : (
                      <div className={styles.noRecalls}>
                        <p>✅ No recalls found for this vehicle</p>
                        <p className={styles.dataSource}>Source: {vehicleHistory.recalls.source}</p>
                      </div>
                    )}
                  </div>
                )}

                {/* Premium History Services */}
                <div className={styles.premiumSection}>
                  <h3>Complete Vehicle History</h3>
                  <p>For comprehensive vehicle history including accidents, ownership, and title information, consider these services:</p>

                  {vehicleHistory.integrationOptions && (
                    <div className={styles.serviceOptions}>
                      {vehicleHistory.integrationOptions.recommended.map((service, index) => (
                        <div key={index} className={styles.serviceCard}>
                          <h4>{service.service}</h4>
                          <p className={styles.serviceCost}>{service.cost}</p>
                          <ul className={styles.serviceFeatures}>
                            {service.features.map((feature, fIndex) => (
                              <li key={fIndex}>{feature}</li>
                            ))}
                          </ul>
                          {service.note && (
                            <p className={styles.serviceNote}>{service.note}</p>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* MarketCheck Pricing Data - Always available for dealers */}
            {(marketData || marketDataLoading) && (
              <div className={styles.marketDataSection}>
                <h2>Market Pricing Data</h2>
                <p className={styles.dealerInfo}>🏪 Dealer pricing information</p>

                {marketDataLoading ? (
                  <div className={styles.loadingMarket}>
                    <p>Loading market data...</p>
                  </div>
                ) : marketData ? (
                  <div className={styles.marketDataGrid}>
                    {/* Market Value */}
                    {marketData.marketValue?.success && marketData.marketValue.data && (
                      <div className={marketStyles.marketCard}>
                        <h3>Market Value</h3>
                        {marketData.marketValue.data.marketValue ? (
                          <div>
                            <p className={marketStyles.marketPrice}>
                              ${marketData.marketValue.data.marketValue.toLocaleString()}
                            </p>
                            <p className={marketStyles.marketRange}>
                              Range: ${marketData.marketValue.data.priceRange?.min.toLocaleString()} -
                              ${marketData.marketValue.data.priceRange?.max.toLocaleString()}
                            </p>
                            <p className={marketStyles.marketInfo}>
                              {marketData.marketValue.data.message}
                            </p>
                          </div>
                        ) : (
                          <p>No market value data available</p>
                        )}
                      </div>
                    )}

                    {/* Comparable Listings */}
                    {marketData.comparableListings?.success && marketData.comparableListings.data?.listings?.length > 0 && (
                      <div className={marketStyles.marketCard}>
                        <h3>Comparable Listings</h3>
                        <div className={marketStyles.comparableList}>
                          {marketData.comparableListings.data.listings.slice(0, 5).map((listing, index) => (
                            <div key={index} className={marketStyles.comparableItem}>
                              <p><strong>{listing.year} {listing.make} {listing.model}</strong></p>
                              <p>Price: ${listing.price?.toLocaleString() || 'N/A'}</p>
                              <p>Mileage: {listing.mileage?.toLocaleString() || 'N/A'} miles</p>
                              <p>Location: {listing.location || 'N/A'}</p>
                              {listing.distance && <p>Distance: {listing.distance} miles</p>}
                            </div>
                          ))}
                        </div>
                        <p className={marketStyles.marketInfo}>
                          {marketData.comparableListings.data.message}
                        </p>
                      </div>
                    )}

                    {/* Price History */}
                    {marketData.priceHistory?.success && marketData.priceHistory.data?.history?.length > 0 && (
                      <div className={marketStyles.marketCard}>
                        <h3>Price History</h3>
                        <p className={marketStyles.priceTrend}>
                          Trend: <span className={`${marketStyles.trend} ${marketStyles[marketData.priceHistory.data.trend]}`}>
                            {marketData.priceHistory.data.trend}
                          </span>
                        </p>
                        <div className={marketStyles.historyList}>
                          {marketData.priceHistory.data.history.slice(0, 5).map((entry, index) => (
                            <div key={index} className={marketStyles.historyItem}>
                              <p><strong>Date:</strong> {entry.date}</p>
                              <p><strong>Price:</strong> ${entry.price?.toLocaleString() || 'N/A'}</p>
                              <p><strong>Mileage:</strong> {entry.mileage?.toLocaleString() || 'N/A'} miles</p>
                            </div>
                          ))}
                        </div>
                        <p className={marketStyles.marketInfo}>
                          {marketData.priceHistory.data.message}
                        </p>
                      </div>
                    )}
                  </div>
                ) : null}
              </div>
            )}
          </div>
        )}

        {/* VIN Scanner Modal */}
        {showVINScanner && (
          <VINScanner
            onVINDetected={handleVINDetected}
            onClose={() => setShowVINScanner(false)}
          />
        )}
      </div>
    </ProtectedRoute>
  );
}
