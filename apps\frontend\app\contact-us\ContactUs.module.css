/* Contact Us Page Styles */

.contactPage {
  min-height: 100vh;
  background: var(--bg-primary);
  padding: 2rem 1rem;
  transition: background 0.3s ease;
}

/* Dark mode background */
[data-theme="dark"] .contactPage {
  background: linear-gradient(to bottom right, #0d1117, #161b22);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  text-align: center;
  margin-bottom: 3rem;
  padding-top: 2rem;
}

.title {
  font-size: 3rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 1rem;
  transition: color 0.3s ease;
}

.subtitle {
  font-size: 1.2rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
  transition: color 0.3s ease;
}

.content {
  background: var(--bg-secondary);
  border-radius: 12px;
  box-shadow: var(--card-shadow);
  overflow: hidden;
  border: 1px solid var(--border-primary);
  transition: all 0.3s ease;
}

/* Dark mode content styling */
[data-theme="dark"] .content {
  background: rgba(22, 27, 34, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.contactGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  min-height: 600px;
}

/* Contact Information */
.contactInfo {
  background: linear-gradient(135deg, #4a5568 0%, #718096 50%, #a0aec0 100%);
  color: white;
  padding: 3rem 2rem;
  display: flex;
  flex-direction: column;
  gap: 2rem;
  position: relative;
}

/* Dark mode contact info */
[data-theme="dark"] .contactInfo {
  background: linear-gradient(135deg, #1a202c 0%, #2d3748 50%, #4a5568 100%);
}

.contactInfo::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 30px;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.2) 100%);
}

.sectionTitle {
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: white;
}

.contactItem {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.icon {
  font-size: 1.5rem;
  color: #ffd700;
  margin-top: 0.25rem;
  flex-shrink: 0;
}

.contactItem h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: white;
}

.contactItem p {
  margin-bottom: 0.25rem;
  line-height: 1.4;
  color: rgba(255, 255, 255, 0.9);
}

.contactItem span {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  font-style: italic;
}

/* Contact Form */
.contactForm {
  padding: 3rem 2rem;
  background: var(--bg-secondary);
  transition: background 0.3s ease;
}

/* Dark mode form styling */
[data-theme="dark"] .contactForm {
  background: rgba(22, 27, 34, 0.9);
}

.contactForm .sectionTitle {
  color: var(--text-primary);
  margin-bottom: 2rem;
  transition: color 0.3s ease;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
}

.label {
  font-size: 1rem;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  transition: color 0.3s ease;
}

.input,
.select,
.textarea {
  padding: 0.75rem;
  border: 1px solid var(--border-primary);
  border-radius: 6px;
  font-size: 1rem;
  background: var(--bg-primary);
  color: var(--text-primary);
  transition: all 0.3s ease;
}

.input:focus,
.select:focus,
.textarea:focus {
  outline: none;
  border-color: #e53e3e;
  box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
}

.textarea {
  resize: vertical;
  min-height: 120px;
}

.submitButton {
  background: #e53e3e;
  color: white;
  padding: 1rem 2rem;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
}

.submitButton:hover {
  background: #c53030;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(229, 62, 62, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .contactGrid {
    grid-template-columns: 1fr;
  }
  
  .title {
    font-size: 2.5rem;
  }
  
  .contactInfo,
  .contactForm {
    padding: 2rem 1.5rem;
  }
  
  .sectionTitle {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .contactPage {
    padding: 1rem 0.5rem;
  }
  
  .title {
    font-size: 2rem;
  }
  
  .contactInfo,
  .contactForm {
    padding: 1.5rem 1rem;
  }
}

/* RTL Support */
[dir="rtl"] .contactPage {
  direction: rtl;
}

[dir="rtl"] .contactItem {
  flex-direction: row-reverse;
}

[dir="rtl"] .form {
  text-align: right;
}
