'use client'

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import styles from './login.module.css';

const DealerLogin = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const res = await fetch('/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email, password }),
      });

      const data = await res.json();

      if (!data.success) {
        setError(data.error || 'Login failed');
      } else {
        // Store user info in localStorage
        localStorage.setItem('user', JSON.stringify(data.data));

        // Dispatch custom event to notify navbar of login
        window.dispatchEvent(new Event('userLogin'));

        // Redirect to admin dashboard
        router.push('/admin');
      }
    } catch (err) {
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.card}>
        {/* Header */}
        <div className={styles.header}>
          <h1>🔐 Dealer Login</h1>
        </div>

        {/* Error Message */}
        {error && (
          <div className={styles.errorAlert}>
            {error}
          </div>
        )}

        {/* Form */}
        <form onSubmit={handleSubmit} className={styles.form}>
          <div className={styles.formGroup}>
            <label htmlFor="email">Email</label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              placeholder="Enter your authorized email"
              className="formInput"
            />
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="password">Password</label>
            <input
              type="password"
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              placeholder="Enter your password"
              className="formInput"
            />
          </div>

          <button
            type="submit"
            disabled={loading}
            className={`enhanced-button ${styles.submitBtn}`}
          >
            {loading ? 'Signing In...' : 'Sign In'}
          </button>

          <div className={styles.footer}>
            <p>Access restricted to authorized dealers only</p>
          </div>
        </form>
      </div>
    </div>
  );
};

export default DealerLogin;
