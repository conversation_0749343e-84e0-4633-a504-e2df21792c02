/* Vehicle Info Page Styles */

.container {
  min-height: 100vh;
  background: #ffffff;
  padding: 0;
  color: var(--text-primary, #333);
  transition: background 0.3s ease;
}

/* Dark mode: Apply consistent gradient background */
[data-theme="dark"] .container {
  background: linear-gradient(to bottom right, #0d1117, #161b22);
}

.header {
  text-align: center;
  margin-bottom: 2rem;
  padding: 2rem 1rem 0;
  background: transparent;
}

/* Responsive padding adjustments for header */
@media (min-width: 769px) {
  .header {
    padding: 2rem 3rem 0;
  }
}

@media (min-width: 1200px) {
  .header {
    padding: 2rem 4rem 0;
  }
}

.header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary, #2d3748);
  margin-bottom: 0.5rem;
}

.header p {
  font-size: 1.1rem;
  color: var(--text-secondary, #4a5568);
  margin: 0;
}

.inputSection {
  max-width: none;
  margin: 0;
  background: transparent;
  padding: 0 1rem 2rem;
}

/* Responsive padding adjustments for input section */
@media (min-width: 769px) {
  .inputSection {
    padding: 0 3rem 2rem;
  }
}

@media (min-width: 1200px) {
  .inputSection {
    padding: 0 4rem 2rem;
  }
}

.vinInputGroup {
  display: flex;
  gap: 1rem;
  align-items: stretch;
}

.vinInput {
  flex: 1;
  padding: 1rem;
  border: 2px solid var(--border-color, #e2e8f0);
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: border-color 0.2s ease;
  background: var(--bg-secondary, white);
  color: var(--text-primary, #333);
}

.vinInput:focus {
  outline: none;
  border-color: var(--brand-red, #e53e3e);
  box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
}

/* Dark mode border styling - consistent with other inputs */
[data-theme="dark"] .vinInput {
  border: 1.5px solid #4a4a4a !important;
}

.scanButton {
  background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
  color: white;
  border: none;
  padding: 1rem 1.25rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  min-width: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  line-height: 1;
}

.scanButton span {
  display: flex;
  align-items: center;
}

.scanButton:hover {
  background: linear-gradient(135deg, #c53030 0%, #9b2c2c 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(229, 62, 62, 0.3);
}

/* Camera icon flash animation */
.scanButton span:first-child {
  transition: filter 0.2s ease;
  display: flex;
  align-items: center;
  transform: translateY(-1px);
  font-size: 1.1em;
  line-height: 1;
}

.scanButton:hover span:first-child {
  animation: flashIcon 2s ease-in-out;
}

@keyframes flashIcon {
  0%   { filter: brightness(1); }
  50%  { filter: brightness(2); }
  100% { filter: brightness(1); }
}

.lookupButton {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  padding: 1rem 1.5rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  min-width: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.lookupButton:hover:not(:disabled) {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.lookupButton:disabled {
  background: var(--text-tertiary, #9ca3af);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.characterCount {
  margin-top: 0.5rem;
  font-size: 0.9rem;
  color: var(--text-secondary, #4a5568);
  text-align: center;
}

.error {
  max-width: none;
  margin: 0 0 2rem 0;
  background: var(--error-bg, #fed7d7);
  color: var(--error, #c53030);
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid var(--error-border, #feb2b2);
  text-align: center;
  font-weight: 500;
}

.resultsSection {
  max-width: none;
  margin: 0;
}

.vehicleInfo {
  background: transparent;
  padding: 0 1rem 2rem;
  margin-bottom: 2rem;
}

/* Responsive padding adjustments for vehicle info */
@media (min-width: 769px) {
  .vehicleInfo {
    padding: 0 3rem 2rem;
  }
}

@media (min-width: 1200px) {
  .vehicleInfo {
    padding: 0 4rem 2rem;
  }
}

.vehicleInfo h2 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary, #2d3748);
  margin-bottom: 1.5rem;
  text-align: center;
}

.vinDisplay {
  font-size: 1.2rem;
  font-weight: 400;
  color: var(--text-secondary, #4a5568);
}

.infoGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.infoCard {
  background: none;
  border-radius: 0;
  padding: 1.5rem 0;
  border: none;
  border-bottom: 1px solid var(--border-color, #e2e8f0);
}

/* Dark mode border styling for info cards */
[data-theme="dark"] .infoCard {
  border-bottom-color: #333b49;
}

.infoCard h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary, #2d3748);
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--brand-red, #e53e3e);
}

.infoItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--border-primary, #e2e8f0);
}

.infoItem:last-child {
  border-bottom: none;
}

.label {
  font-weight: 600;
  color: var(--text-secondary, #4a5568);
}

.value {
  font-weight: 500;
  color: var(--text-primary, #2d3748);
  text-align: right;
}

.actionButtons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid var(--border-color, #e2e8f0);
}

.saveButton {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 140px;
}

.saveButton:hover {
  background: linear-gradient(135deg, #38a169 0%, #2f855a 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(72, 187, 120, 0.3);
}

.clearButton {
  background: linear-gradient(135deg, #a0aec0 0%, #718096 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 140px;
}

.clearButton:hover {
  background: linear-gradient(135deg, #718096 0%, #4a5568 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(160, 174, 192, 0.3);
}

.historySection {
  background: transparent;
  padding: 0 1rem 2rem;
}

/* Responsive padding adjustments for history section */
@media (min-width: 769px) {
  .historySection {
    padding: 0 3rem 2rem;
  }
}

@media (min-width: 1200px) {
  .historySection {
    padding: 0 4rem 2rem;
  }
}

.historySection h2 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary, #2d3748);
  margin-bottom: 1.5rem;
  text-align: center;
}

.historyPlaceholder {
  text-align: center;
  padding: 2rem;
  background: var(--bg-tertiary, #f8f9fa);
  border-radius: 12px;
  border: 1px solid var(--border-primary, #e2e8f0);
}

.historyPlaceholder p {
  font-size: 1.1rem;
  color: var(--text-secondary, #4a5568);
  margin-bottom: 1.5rem;
}

.suggestedServices {
  max-width: 400px;
  margin: 0 auto;
}

.suggestedServices h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary, #2d3748);
  margin-bottom: 1rem;
}

.suggestedServices ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.suggestedServices li {
  background: var(--bg-primary, white);
  padding: 0.75rem 1rem;
  margin-bottom: 0.5rem;
  border-radius: 8px;
  border: 1px solid var(--border-primary, #e2e8f0);
  color: var(--text-secondary, #4a5568);
  font-weight: 500;
}

/* Recalls Section */
.recallsSection {
  background: var(--bg-tertiary, #f8f9fa);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  border: 1px solid var(--border-primary, #e2e8f0);
}

.recallsSection h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary, #2d3748);
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--brand-red, #e53e3e);
}

.recallCount {
  background: var(--error-bg, #fed7d7);
  color: var(--error, #c53030);
  padding: 0.75rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  font-weight: 600;
  text-align: center;
}

.noRecalls {
  background: rgba(198, 246, 213, 0.8);
  color: var(--success, #22543d);
  padding: 1rem;
  border-radius: 8px;
  text-align: center;
}

.noRecalls p:first-child {
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.recallsData {
  margin-top: 1rem;
}

.recallsList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.moreRecalls {
  color: var(--text-secondary, #718096);
  font-style: italic;
  text-align: center;
  margin-top: 1rem;
}

.recallSource {
  font-size: 0.8rem;
  color: var(--text-secondary, #718096);
  font-style: italic;
  margin-top: 1rem;
  text-align: center;
}

.recallItem {
  background: none;
  padding: 1rem 0;
  border-radius: 0;
  border: none;
  border-left: 4px solid #e53e3e;
  border-bottom: 1px solid #e2e8f0;
}

.recallItem h4 {
  color: #2d3748;
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.recallItem p {
  color: #4a5568;
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
}

.dataSource {
  font-size: 0.8rem;
  color: #718096;
  font-style: italic;
  margin-top: 1rem;
  text-align: center;
}

/* Premium Services Section */
.premiumSection {
  background: none;
  border-radius: 0;
  padding: 1.5rem 0;
  border: none;
  border-top: 1px solid #e2e8f0;
  transition: background 0.3s ease, border-color 0.3s ease;
}

/* Dark mode styling for premium section */
[data-theme="dark"] .premiumSection {
  background: none;
  border: none;
  border-top: 1px solid #333;
}

.premiumSection h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #3b82f6;
  transition: color 0.3s ease;
}

.premiumSection > p {
  color: #4a5568;
  margin-bottom: 1.5rem;
  transition: color 0.3s ease;
}

/* Dark mode styling for premium section text */
[data-theme="dark"] .premiumSection h3 {
  color: #ffffff;
}

[data-theme="dark"] .premiumSection > p {
  color: #cbd5e1;
}

.serviceOptions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.serviceCard {
  background: none;
  padding: 1.5rem 0;
  border-radius: 0;
  border: none;
  border-bottom: 1px solid #e2e8f0;
  transition: transform 0.2s ease, box-shadow 0.2s ease, background 0.3s ease, border-color 0.3s ease;
}

/* Dark mode styling for service cards - match Services page style */
[data-theme="dark"] .serviceCard {
  background: none;
  border: none;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.serviceCard:hover {
  transform: none;
  box-shadow: none;
}

.serviceCard h4 {
  color: #2d3748;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.serviceCost {
  color: #e53e3e;
  font-weight: 600;
  font-size: 1rem;
  margin-bottom: 1rem;
}

.serviceFeatures {
  list-style: none;
  padding: 0;
  margin: 0 0 1rem 0;
}

.serviceFeatures li {
  color: #4a5568;
  font-size: 0.9rem;
  padding: 0.25rem 0;
  position: relative;
  padding-left: 1.2rem;
}

.serviceFeatures li::before {
  content: '✓';
  color: #22543d;
  font-weight: bold;
  position: absolute;
  left: 0;
}

.serviceNote {
  font-size: 0.8rem;
  color: #718096;
  font-style: italic;
  margin: 0;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .container {
    padding: 1rem 0.5rem;
  }
  
  .header {
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }
  
  .header h1 {
    font-size: 2rem;
  }
  
  .inputSection {
    padding: 1.5rem;
  }
  
  .vinInputGroup {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .scanButton,
  .lookupButton {
    width: 100%;
    min-width: auto;
  }
  
  .vehicleInfo,
  .historySection {
    padding: 1.5rem;
  }
  
  .infoGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .infoCard {
    padding: 1rem;
  }
  
  .infoItem {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
  
  .value {
    text-align: left;
    font-size: 0.9rem;
  }
}

/* MarketCheck Pricing Data Styles */
.marketDataSection {
  margin-top: 2rem;
  padding: 1.5rem;
  background: var(--card-background, #f8f9fa);
  border-radius: 12px;
  border: 1px solid var(--border-color, #e2e8f0);
}

[data-theme="dark"] .marketDataSection {
  background: #0d1117;
  border-color: #30363d;
}

.marketDataSection h2 {
  color: var(--text-primary, #2d3748);
  margin-bottom: 0.5rem;
  font-size: 1.5rem;
  font-weight: 600;
}

.dealerOnly {
  color: var(--text-secondary, #4a5568);
  font-size: 0.9rem;
  margin-bottom: 1.5rem;
  font-style: italic;
}

.loadingMarket {
  text-align: center;
  padding: 2rem;
  color: var(--text-secondary, #4a5568);
}

.marketDataGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

/* Market-related styles moved to shared-market.module.css */

/* Message for non-dealers */
.dealerMessage {
  margin-top: 2rem;
  padding: 1.5rem;
  background: var(--background-secondary, #f8f9fa);
  border-radius: 8px;
  border: 1px solid var(--border-color, #e2e8f0);
  text-align: center;
}

[data-theme="dark"] .dealerMessage {
  background: #161b22;
  border-color: #30363d;
}

.dealerMessage h3 {
  color: var(--text-primary, #2d3748);
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.dealerMessage p {
  color: var(--text-secondary, #4a5568);
  margin-bottom: 0.5rem;
}

.loginLink {
  color: var(--primary-color, #007bff);
  text-decoration: none;
  font-weight: 600;
}

.loginLink:hover {
  text-decoration: underline;
}

/* Responsive adjustments for MarketCheck data */
@media (max-width: 768px) {
  .marketDataGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .marketCard {
    padding: 1rem;
  }

  .marketPrice {
    font-size: 1.5rem;
  }
}

/* RTL Support for Vehicle Lookup */
[dir="rtl"] .container {
  direction: rtl;
  text-align: right;
}

[dir="rtl"] .header {
  text-align: center; /* Keep header centered for all languages */
}

[dir="rtl"] .vinInputGroup {
  flex-direction: row-reverse;
}

[dir="rtl"] .infoGrid {
  direction: rtl;
}

[dir="rtl"] .infoCard {
  text-align: right;
}

[dir="rtl"] .infoItem {
  flex-direction: row-reverse;
  text-align: right;
}

[dir="rtl"] .label {
  text-align: right;
}

[dir="rtl"] .value {
  text-align: right;
}

[dir="rtl"] .recallsSection {
  direction: rtl;
  text-align: right;
}

[dir="rtl"] .recallItem {
  text-align: right;
}

[dir="rtl"] .marketDataGrid {
  direction: rtl;
}

[dir="rtl"] .marketCard {
  text-align: right;
}
