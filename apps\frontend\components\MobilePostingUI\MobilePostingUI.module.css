.mobilePostingUI {
  max-width: 480px;
  margin: 0 auto;
  padding: 16px;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  min-height: 100vh;
}

/* Progress Steps */
.progressSteps {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;
  padding: 16px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.progressStep {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  flex: 1;
  position: relative;
}

.progressStep::after {
  content: '';
  position: absolute;
  top: 20px;
  left: 60%;
  width: 80%;
  height: 2px;
  background: #e0e0e0;
  z-index: 1;
}

.progressStep:last-child::after {
  display: none;
}

.progressStep.active::after {
  background: #dc2626;
}

.stepIcon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
}

.progressStep.active .stepIcon {
  background: #dc2626;
  color: white;
}

.progressStep.current .stepIcon {
  background: #dc2626;
  color: white;
  transform: scale(1.1);
  box-shadow: 0 0 0 4px rgba(220, 38, 38, 0.2);
}

.stepName {
  font-size: 0.75rem;
  font-weight: 600;
  color: #666;
  text-align: center;
}

.progressStep.active .stepName {
  color: #dc2626;
}

/* Step Container */
.stepContainer {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  min-height: 400px;
}

.stepContent {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.stepHeader {
  text-align: center;
}

.stepHeader h3 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 1.3rem;
}

.stepHeader p {
  margin: 0;
  color: #666;
  font-size: 0.95rem;
}

/* VIN Input */
.vinInput {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.vinField {
  padding: 16px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 1.1rem;
  font-family: 'Courier New', monospace;
  letter-spacing: 1px;
  text-transform: uppercase;
  text-align: center;
  transition: border-color 0.2s ease;
}

.vinField:focus {
  outline: none;
  border-color: #dc2626;
}

.vinStatus {
  text-align: center;
}

.valid {
  color: #059669;
  font-weight: 600;
}

.invalid {
  color: #dc2626;
  font-weight: 600;
}

/* Photo Actions */
.photoActions {
  display: flex;
  gap: 12px;
}

.hiddenInput {
  display: none;
}

.photoBtn {
  flex: 1;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  border: none;
  padding: 16px 12px;
  border-radius: 8px;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.photoBtn:hover {
  background: linear-gradient(135deg, #2563eb, #1d4ed8);
  transform: translateY(-2px);
}

/* Image Grid */
.imageGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 12px;
  max-height: 300px;
  overflow-y: auto;
  padding: 12px;
  background: rgba(248, 250, 252, 0.5);
  border-radius: 8px;
}

.imageItem {
  position: relative;
  aspect-ratio: 1;
  border-radius: 6px;
  overflow: hidden;
  background: #f5f5f5;
}

.imageItem img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.removeBtn {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: rgba(220, 38, 38, 0.9);
  color: white;
  border: none;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Generate Section */
.generateSection {
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: center;
}

.summary {
  background: rgba(248, 250, 252, 0.8);
  padding: 16px;
  border-radius: 8px;
  width: 100%;
}

.summaryItem {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.summaryItem:last-child {
  margin-bottom: 0;
}

.label {
  font-weight: 600;
  color: #666;
}

.value {
  color: #333;
  font-family: 'Courier New', monospace;
}

.generateBtn {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  color: white;
  border: none;
  padding: 16px 32px;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 200px;
}

.generateBtn:hover:not(:disabled) {
  background: linear-gradient(135deg, #b91c1c, #991b1b);
  transform: translateY(-2px);
}

.generateBtn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

/* Generated Ads */
.generatedAds {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.platformAd {
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  background: rgba(248, 250, 252, 0.5);
}

.platformHeader h4 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 1rem;
}

.adPreview {
  background: white;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 12px;
  border-left: 4px solid #dc2626;
}

.adTitle {
  margin-bottom: 8px;
  color: #333;
  font-size: 0.95rem;
}

.adDescription {
  color: #666;
  font-size: 0.85rem;
  line-height: 1.4;
}

.adActions {
  display: flex;
  gap: 8px;
}

.copyBtn {
  flex: 1;
  background: #059669;
  color: white;
  border: none;
  padding: 12px;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s ease;
}

.copyBtn:hover {
  background: #047857;
}

.postBtn {
  flex: 1;
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  color: white;
  border: none;
  padding: 12px;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.postBtn:hover {
  background: linear-gradient(135deg, #b91c1c, #991b1b);
}

/* Step Actions */
.stepActions {
  display: flex;
  gap: 12px;
  margin-top: auto;
}

.backBtn {
  background: #6b7280;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 6px;
  font-size: 0.95rem;
  cursor: pointer;
  transition: background 0.2s ease;
}

.backBtn:hover {
  background: #4b5563;
}

.nextBtn {
  flex: 1;
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 6px;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.nextBtn:hover:not(:disabled) {
  background: linear-gradient(135deg, #b91c1c, #991b1b);
  transform: translateY(-1px);
}

.nextBtn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

.newPostingBtn {
  width: 100%;
  background: linear-gradient(135deg, #059669, #047857);
  color: white;
  border: none;
  padding: 16px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.newPostingBtn:hover {
  background: linear-gradient(135deg, #047857, #065f46);
  transform: translateY(-2px);
}

/* Mobile Tips */
.mobileTips {
  background: rgba(59, 130, 246, 0.1);
  border-radius: 8px;
  padding: 16px;
  border-left: 4px solid #3b82f6;
}

.mobileTips h4 {
  color: #1e40af;
  margin: 0 0 12px 0;
  font-size: 1rem;
}

.mobileTips ul {
  margin: 0;
  padding-left: 20px;
  color: #1e40af;
}

.mobileTips li {
  margin-bottom: 6px;
  font-size: 0.85rem;
  line-height: 1.4;
}

.mobileTips li:last-child {
  margin-bottom: 0;
}

/* Responsive adjustments for larger screens */
@media (min-width: 481px) {
  .mobilePostingUI {
    max-width: 600px;
    padding: 24px;
  }

  .stepContainer {
    padding: 32px;
  }

  .progressSteps {
    padding: 20px;
  }
}

/* Very small screens */
@media (max-width: 360px) {
  .mobilePostingUI {
    padding: 12px;
  }

  .stepContainer {
    padding: 16px;
  }

  .progressSteps {
    padding: 12px;
  }

  .stepName {
    font-size: 0.7rem;
  }

  .stepIcon {
    width: 36px;
    height: 36px;
    font-size: 1rem;
  }

  .photoActions {
    flex-direction: column;
  }

  .adActions {
    flex-direction: column;
  }
}
