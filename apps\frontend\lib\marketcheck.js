/**
 * MarketCheck API Service
 * Provides vehicle pricing data for dealer-only access
 * API Documentation: https://www.marketcheck.com/automotive/api/docs
 */

const MARKETCHECK_API_BASE = 'https://api.marketcheck.com/v2';

/**
 * Get vehicle market value and pricing data
 * @param {string} vin - Vehicle Identification Number
 * @param {Object} vehicleData - Basic vehicle data from NHTSA (make, model, year, etc.)
 * @returns {Promise<Object>} - Market pricing data
 */
export async function getMarketValue(vin, vehicleData = {}) {
  const apiKey = process.env.MARKETCHECK_API_KEY;
  const apiSecret = process.env.MARKETCHECK_API_SECRET;

  if (!apiKey || !apiSecret) {
    throw new Error('MarketCheck API credentials not configured');
  }

  try {
    console.log('🏷️ Fetching MarketCheck market value for VIN:', vin);

    // Build query parameters
    const params = new URLSearchParams({
      api_key: apiKey,
      vin: vin
    });

    // Add additional filters if vehicle data is available
    if (vehicleData.make) params.append('make', vehicleData.make);
    if (vehicleData.model) params.append('model', vehicleData.model);
    if (vehicleData.year) params.append('year', vehicleData.year);

    // Add timeout to prevent hanging requests
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 20000); // 20 second timeout for MarketCheck

    const response = await fetch(
      `${MARKETCHECK_API_BASE}/search?${params.toString()}`,
      {
        method: 'GET',
        signal: controller.signal,
        headers: {
          'Accept': 'application/json',
          'Authorization': `Bearer ${apiSecret}`,
          'User-Agent': 'FazeNAuto-MarketCheck/1.0'
        }
      }
    );

    clearTimeout(timeoutId);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`MarketCheck API error: ${response.status} - ${errorData.message || response.statusText}`);
    }

    const data = await response.json();
    
    console.log('✅ MarketCheck market value fetched successfully');
    
    return {
      success: true,
      data: parseMarketValueResponse(data),
      source: 'MarketCheck',
      fetchedAt: new Date().toISOString()
    };

  } catch (error) {
    console.error('❌ MarketCheck market value fetch failed:', error);

    // Provide more specific error messages
    let errorMessage = 'Failed to fetch market value';
    if (error.name === 'AbortError') {
      errorMessage = 'MarketCheck API timeout - please try again later';
    } else if (error.message.includes('fetch failed')) {
      errorMessage = 'Network connection issue - MarketCheck API unavailable';
    } else if (error.message) {
      errorMessage = error.message;
    }

    return {
      success: false,
      error: errorMessage,
      source: 'MarketCheck'
    };
  }
}

/**
 * Get comparable vehicle listings
 * @param {string} vin - Vehicle Identification Number
 * @param {Object} vehicleData - Basic vehicle data from NHTSA
 * @param {Object} options - Search options (radius, limit, etc.)
 * @returns {Promise<Object>} - Comparable listings data
 */
export async function getComparableListings(vin, vehicleData = {}, options = {}) {
  const apiKey = process.env.MARKETCHECK_API_KEY;
  const apiSecret = process.env.MARKETCHECK_API_SECRET;

  if (!apiKey || !apiSecret) {
    throw new Error('MarketCheck API credentials not configured');
  }

  try {
    console.log('🔍 Fetching MarketCheck comparable listings for VIN:', vin);

    const params = new URLSearchParams({
      api_key: apiKey,
      make: vehicleData.make || '',
      model: vehicleData.model || '',
      year: vehicleData.year || '',
      radius: options.radius || '50', // 50 mile radius
      rows: options.limit || '10', // Limit to 10 results
      start: '0'
    });

    // Add optional filters
    if (options.minPrice) params.append('price_min', options.minPrice);
    if (options.maxPrice) params.append('price_max', options.maxPrice);
    if (options.mileageMax) params.append('miles_max', options.mileageMax);

    // Add timeout to prevent hanging requests
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 20000); // 20 second timeout

    const response = await fetch(
      `${MARKETCHECK_API_BASE}/search?${params.toString()}`,
      {
        method: 'GET',
        signal: controller.signal,
        headers: {
          'Accept': 'application/json',
          'Authorization': `Bearer ${apiSecret}`,
          'User-Agent': 'FazeNAuto-MarketCheck/1.0'
        }
      }
    );

    clearTimeout(timeoutId);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`MarketCheck API error: ${response.status} - ${errorData.message || response.statusText}`);
    }

    const data = await response.json();
    
    console.log('✅ MarketCheck comparable listings fetched successfully');
    
    return {
      success: true,
      data: parseComparableListingsResponse(data),
      source: 'MarketCheck',
      fetchedAt: new Date().toISOString()
    };

  } catch (error) {
    console.error('❌ MarketCheck comparable listings fetch failed:', error);
    return {
      success: false,
      error: error.message || 'Failed to fetch comparable listings',
      source: 'MarketCheck'
    };
  }
}

/**
 * Get vehicle price history
 * @param {string} vin - Vehicle Identification Number
 * @param {Object} vehicleData - Basic vehicle data from NHTSA
 * @returns {Promise<Object>} - Price history data
 */
export async function getPriceHistory(vin, vehicleData = {}) {
  const apiKey = process.env.MARKETCHECK_API_KEY;
  const apiSecret = process.env.MARKETCHECK_API_SECRET;

  if (!apiKey || !apiSecret) {
    throw new Error('MarketCheck API credentials not configured');
  }

  try {
    console.log('📈 Fetching MarketCheck price history for VIN:', vin);

    const params = new URLSearchParams({
      api_key: apiKey,
      vin: vin
    });

    // Add timeout to prevent hanging requests
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 20000); // 20 second timeout

    const response = await fetch(
      `${MARKETCHECK_API_BASE}/history?${params.toString()}`,
      {
        method: 'GET',
        signal: controller.signal,
        headers: {
          'Accept': 'application/json',
          'Authorization': `Bearer ${apiSecret}`,
          'User-Agent': 'FazeNAuto-MarketCheck/1.0'
        }
      }
    );

    clearTimeout(timeoutId);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`MarketCheck API error: ${response.status} - ${errorData.message || response.statusText}`);
    }

    const data = await response.json();
    
    console.log('✅ MarketCheck price history fetched successfully');
    
    return {
      success: true,
      data: parsePriceHistoryResponse(data),
      source: 'MarketCheck',
      fetchedAt: new Date().toISOString()
    };

  } catch (error) {
    console.error('❌ MarketCheck price history fetch failed:', error);
    return {
      success: false,
      error: error.message || 'Failed to fetch price history',
      source: 'MarketCheck'
    };
  }
}

/**
 * Parse market value response from MarketCheck API
 */
function parseMarketValueResponse(data) {
  if (!data || !data.listings || data.listings.length === 0) {
    return {
      marketValue: null,
      averagePrice: null,
      priceRange: null,
      listingCount: 0,
      message: 'No market data available'
    };
  }

  const listings = data.listings;
  const prices = listings.map(listing => listing.price).filter(price => price && price > 0);
  
  if (prices.length === 0) {
    return {
      marketValue: null,
      averagePrice: null,
      priceRange: null,
      listingCount: listings.length,
      message: 'No pricing data available'
    };
  }

  const averagePrice = Math.round(prices.reduce((sum, price) => sum + price, 0) / prices.length);
  const minPrice = Math.min(...prices);
  const maxPrice = Math.max(...prices);

  return {
    marketValue: averagePrice,
    averagePrice: averagePrice,
    priceRange: {
      min: minPrice,
      max: maxPrice
    },
    listingCount: listings.length,
    dataPoints: prices.length,
    message: `Based on ${prices.length} comparable listings`
  };
}

/**
 * Parse comparable listings response from MarketCheck API
 */
function parseComparableListingsResponse(data) {
  if (!data || !data.listings || data.listings.length === 0) {
    return {
      listings: [],
      totalCount: 0,
      message: 'No comparable listings found'
    };
  }

  const listings = data.listings.map(listing => ({
    id: listing.id,
    vin: listing.vin,
    make: listing.make,
    model: listing.model,
    year: listing.year,
    price: listing.price,
    mileage: listing.miles,
    location: listing.dealer_name || listing.city,
    distance: listing.distance,
    daysOnMarket: listing.dom,
    source: listing.source,
    url: listing.vdp_url,
    imageUrl: listing.photo_links?.[0] || null
  }));

  return {
    listings: listings,
    totalCount: data.num_found || listings.length,
    message: `Found ${listings.length} comparable listings`
  };
}

/**
 * Parse price history response from MarketCheck API
 */
function parsePriceHistoryResponse(data) {
  if (!data || !data.history || data.history.length === 0) {
    return {
      history: [],
      trend: null,
      message: 'No price history available'
    };
  }

  const history = data.history.map(entry => ({
    date: entry.date,
    price: entry.price,
    mileage: entry.miles,
    source: entry.source
  }));

  // Calculate trend
  let trend = 'stable';
  if (history.length >= 2) {
    const firstPrice = history[0].price;
    const lastPrice = history[history.length - 1].price;
    const changePercent = ((lastPrice - firstPrice) / firstPrice) * 100;
    
    if (changePercent > 5) trend = 'increasing';
    else if (changePercent < -5) trend = 'decreasing';
  }

  return {
    history: history,
    trend: trend,
    message: `Price history with ${history.length} data points`
  };
}

/**
 * Check if MarketCheck API is properly configured
 */
export function isMarketCheckConfigured() {
  return !!(process.env.MARKETCHECK_API_KEY && process.env.MARKETCHECK_API_SECRET);
}

/**
 * Get all vehicle market data (combines all MarketCheck endpoints)
 * @param {string} vin - Vehicle Identification Number
 * @param {Object} vehicleData - Basic vehicle data from NHTSA
 * @param {Object} options - Options for comparable listings
 * @returns {Promise<Object>} - Combined market data
 */
export async function getCompleteMarketData(vin, vehicleData = {}, options = {}) {
  if (!isMarketCheckConfigured()) {
    return {
      success: false,
      error: 'MarketCheck API not configured',
      data: null
    };
  }

  try {
    console.log('🏪 Fetching complete MarketCheck data for VIN:', vin);

    // Fetch all data in parallel to optimize API usage
    const [marketValueResult, comparableListingsResult, priceHistoryResult] = await Promise.allSettled([
      getMarketValue(vin, vehicleData),
      getComparableListings(vin, vehicleData, options),
      getPriceHistory(vin, vehicleData)
    ]);

    return {
      success: true,
      data: {
        marketValue: marketValueResult.status === 'fulfilled' ? marketValueResult.value : { success: false, error: 'Failed to fetch market value' },
        comparableListings: comparableListingsResult.status === 'fulfilled' ? comparableListingsResult.value : { success: false, error: 'Failed to fetch comparable listings' },
        priceHistory: priceHistoryResult.status === 'fulfilled' ? priceHistoryResult.value : { success: false, error: 'Failed to fetch price history' }
      },
      source: 'MarketCheck',
      fetchedAt: new Date().toISOString()
    };

  } catch (error) {
    console.error('❌ Complete MarketCheck data fetch failed:', error);
    return {
      success: false,
      error: error.message || 'Failed to fetch complete market data',
      data: null
    };
  }
}

export default {
  getMarketValue,
  getComparableListings,
  getPriceHistory,
  getCompleteMarketData,
  isMarketCheckConfigured
};
