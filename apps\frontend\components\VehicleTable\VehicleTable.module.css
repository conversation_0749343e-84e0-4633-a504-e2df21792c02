.tableContainer {
  background: var(--bg-secondary, white);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border: 1px solid var(--border-color, #e2e8f0);
  transition: border-color 0.3s ease;
}

/* Dark mode styling for table container - consistent border */
[data-theme="dark"] .tableContainer {
  border: 1px solid rgba(255, 255, 255, 0.15);
}

/* Selection checkboxes */
.selectCheckbox {
  width: 18px;
  height: 18px;
  accent-color: #e53e3e;
  cursor: pointer;
}

.filters {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  background: var(--bg-tertiary, #f8f9fa);
  border-bottom: 1px solid var(--border-color, #e9ecef);
  flex-wrap: wrap;
}

.searchInput {
  flex: 1;
  min-width: 200px;
  padding: 0.5rem 1rem;
  border: 1px solid var(--border-color, #ddd);
  border-radius: 4px;
  font-size: 0.875rem;
  background: var(--bg-secondary, white);
  color: var(--text-primary, #333);
}

.filterSelect {
  padding: 0.5rem 1rem;
  border: 1px solid var(--border-color, #ddd);
  border-radius: 4px;
  font-size: 0.875rem;
  background: var(--bg-secondary, white);
  color: var(--text-primary, #333);
  min-width: 120px;
}

.filterSelect option {
  color: var(--text-primary, #333);
  background: var(--bg-secondary, white);
  padding: 0.5rem;
}

.refreshBtn {
  padding: 0.5rem 1rem;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background-color 0.2s;
}

.refreshBtn:hover {
  background: #0056b3;
}

.tableWrapper {
  overflow-x: auto;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

/* Hide mobile cards on desktop */
.mobileCardsContainer {
  display: none;
}

.th {
  background: var(--bg-tertiary, #f8f9fa);
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: var(--text-primary, #495057);
  border-bottom: 2px solid var(--border-color, #dee2e6);
  white-space: nowrap;
}

.sortable {
  cursor: pointer;
  user-select: none;
}

.sortable:hover {
  background: var(--bg-hover, #e9ecef);
  border-radius: 4px;
  padding: 0.25rem;
  margin: -0.25rem;
}

.td {
  padding: 1rem;
  border-bottom: 1px solid var(--border-color, #dee2e6);
  vertical-align: middle;
  color: var(--text-primary, #333);
}

.tr:hover {
  background: var(--bg-hover, #f8f9fa);
}

.imageCell {
  display: flex;
  align-items: center;
  justify-content: center;
}

.vehicleImage {
  border-radius: 4px;
  object-fit: cover;
  object-position: center;
}

.noImage {
  width: 80px;
  height: 60px;
  background: #e9ecef;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  color: #6c757d;
}

.makeCell {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.makeLogo {
  width: 24px;
  height: 24px;
  object-fit: contain;
  border-radius: 2px;
}

.makeText {
  font-weight: 500;
}

.status {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status.active {
  background: #d4edda;
  color: #155724;
}

.status.draft {
  background: #fff3cd;
  color: #856404;
}

.status.sold {
  background: #f8d7da;
  color: #721c24;
}

.status.on_hold {
  background: #fff3cd;
  color: #856404;
}

.status.archived {
  background: #e2e3e5;
  color: #495057;
}

.actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.editBtn {
  padding: 0.25rem 0.75rem;
  background: #28a745;
  color: white;
  text-decoration: none;
  border-radius: 4px;
  font-size: 0.75rem;
  transition: background-color 0.2s;
}

.editBtn:hover {
  background: #218838;
}

.deleteBtn {
  padding: 0.25rem 0.75rem;
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 0.75rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.deleteBtn:hover {
  background: #c82333;
}

.deleteBtn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.archiveBtn {
  padding: 0.25rem 0.75rem;
  background: #6b7280;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 0.75rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.archiveBtn:hover {
  background: #4b5563;
}

.archiveBtn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.soldBtn {
  padding: 0.25rem 0.75rem;
  background: #059669;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 0.75rem;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-right: 0.5rem;
}

.soldBtn:hover {
  background: #047857;
}

.soldBtn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.onHoldBtn {
  padding: 0.25rem 0.75rem;
  background: #f59e0b;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 0.75rem;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-right: 0.5rem;
}

.onHoldBtn:hover {
  background: #d97706;
}

.onHoldBtn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.removeFromHoldBtn {
  padding: 0.25rem 0.75rem;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 0.75rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.removeFromHoldBtn:hover {
  background: #2563eb;
}

.removeFromHoldBtn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  padding: 1.5rem;
  background: var(--bg-tertiary, #f8f9fa);
  border-top: 1px solid var(--border-primary, #e9ecef);
}

.pageBtn {
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--border-primary, #ddd);
  background: var(--bg-primary, white);
  color: var(--text-primary, #333);
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s;
}

.pageBtn:hover:not(:disabled) {
  background: var(--bg-hover, #e9ecef);
}

.pageBtn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pageInfo {
  font-size: 0.875rem;
  color: var(--text-secondary, #495057);
  margin: 0 1rem;
}

.pageSizeSelect {
  padding: 0.5rem;
  border: 1px solid var(--border-primary, #ddd);
  border-radius: 4px;
  font-size: 0.875rem;
  background: var(--bg-primary, white);
  color: var(--text-primary, #333);
  min-width: 100px;
}

.loading {
  padding: 3rem;
  text-align: center;
  color: #6c757d;
  font-size: 1.125rem;
}

.error {
  padding: 3rem;
  text-align: center;
  color: #dc3545;
}

.error p {
  margin-bottom: 1rem;
  font-size: 1.125rem;
}

.retryBtn {
  padding: 0.75rem 1.5rem;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background-color 0.2s;
}

.retryBtn:hover {
  background: #0056b3;
}

/* MOBILE ONLY INVENTORY LAYOUT - Responsive */
@media (max-width: 768px) {
  /* Mobile Filters Layout */
  .filters {
    flex-direction: column;
    gap: 1rem;
    padding: 1.25rem;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    border-radius: 12px 12px 0 0;
  }

  .searchInput {
    width: 100%;
    min-width: 100%;
    padding: 1rem;
    font-size: clamp(1rem, 2.5vw, 1.125rem);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: all 0.2s ease;
    min-height: 48px;
  }

  .searchInput:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    background: var(--bg-primary);
  }

  .filterSelect {
    width: 100%;
    padding: 1rem;
    font-size: clamp(1rem, 2.5vw, 1.125rem);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: all 0.2s ease;
    min-height: 48px;
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 1rem center;
    background-repeat: no-repeat;
    background-size: 1rem;
    padding-right: 3rem;
  }

  .filterSelect:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  .refreshBtn {
    width: 100%;
    padding: 1rem;
    font-size: clamp(1rem, 2.5vw, 1.125rem);
    min-height: 48px;
    border-radius: 12px;
    background: #3b82f6;
    color: white;
    border: none;
    font-weight: 600;
    transition: all 0.2s ease;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
  }

  .refreshBtn:hover {
    background: #2563eb;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  }

  .refreshBtn:active {
    transform: scale(0.97);
  }

  /* Mobile Pagination */
  .pagination {
    flex-direction: column;
    gap: 1rem;
    padding: 1.25rem;
    background: var(--bg-secondary);
    border-radius: 0 0 12px 12px;
    align-items: stretch;
  }

  .pageInfo {
    margin: 0;
    order: -1;
    width: 100%;
    text-align: center;
    font-size: clamp(0.875rem, 2vw, 1rem);
    color: var(--text-secondary);
    padding: 0.75rem;
    background: var(--bg-primary);
    border-radius: 8px;
    border: 1px solid var(--border-color);
  }

  .paginationControls {
    display: flex;
    gap: 0.75rem;
    justify-content: center;
    flex-wrap: wrap;
  }

  .pageButton {
    min-width: 48px;
    min-height: 48px;
    padding: 0.75rem 1rem;
    border-radius: 12px;
    font-size: clamp(0.875rem, 2vw, 1rem);
    font-weight: 600;
    transition: all 0.2s ease;
  }

  .pageButton:not(:disabled):hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  .pageButton:not(:disabled):active {
    transform: scale(0.97);
  }

  /* Mobile Vehicle Cards Layout */
  .tableWrapper {
    overflow: visible;
    background: transparent;
  }

  .table {
    display: none; /* Hide table on mobile */
  }

  /* Mobile Cards Container */
  .mobileCardsContainer {
    display: flex !important; /* Override desktop display: none */
    flex-direction: column;
    gap: 1rem;
    padding: 1rem 0.75rem;
  }

  /* Mobile Vehicle Card */
  .mobileVehicleCard {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
  }

  .mobileVehicleCard:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }

  /* Mobile Card Header */
  .mobileCardHeader {
    padding: 1rem 1.25rem;
    background: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
  }

  .mobileCardTitle {
    font-size: clamp(1rem, 3vw, 1.25rem);
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
    flex: 1;
    line-height: 1.3;
  }

  .mobileCardStatus {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    flex-shrink: 0;
  }

  /* Mobile Card Content */
  .mobileCardContent {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    padding: 1.25rem;
  }

  .mobileCardImageSection {
    display: flex;
    gap: 1rem;
    align-items: flex-start;
  }

  .mobileCardImage {
    width: 120px;
    height: 90px;
    border-radius: 12px;
    object-fit: cover;
    object-position: center;
    flex-shrink: 0;
    border: 1px solid var(--border-color);
  }

  .mobileCardNoImage {
    width: 120px;
    height: 90px;
    background: var(--bg-tertiary);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
    flex-shrink: 0;
  }

  .mobileCardDetails {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .mobileCardDetailRow {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
  }

  .mobileCardDetailRow:last-child {
    border-bottom: none;
  }

  .mobileCardDetailLabel {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
  }

  .mobileCardDetailValue {
    font-size: 0.875rem;
    color: var(--text-primary);
    font-weight: 600;
    text-align: right;
  }

  /* Mobile Card Actions */
  .mobileCardActions {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
  }

  .mobileActionRow {
    display: flex;
    gap: 0.75rem;
  }

  .mobileActionButton {
    flex: 1;
    padding: 1rem;
    border-radius: 12px;
    font-size: clamp(0.875rem, 2.5vw, 1rem);
    font-weight: 600;
    text-align: center;
    text-decoration: none;
    transition: all 0.2s ease;
    min-height: 48px; /* Touch-friendly minimum height */
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    border: none;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    user-select: none;
    -webkit-tap-highlight-color: transparent; /* Remove tap highlight on iOS */
  }

  .mobileActionButton:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    filter: brightness(1.05);
  }

  .mobileActionButton:active {
    transform: scale(0.97);
    filter: brightness(0.95);
  }

  /* Touch feedback for mobile buttons */
  @media (hover: none) and (pointer: coarse) {
    .mobileActionButton:hover {
      transform: none;
      filter: none;
    }

    .mobileActionButton:active {
      transform: scale(0.95);
      filter: brightness(0.9);
    }
  }

  .mobileActionButton.edit {
    background: #10b981;
    color: white;
  }

  .mobileActionButton.edit:hover {
    background: #059669;
  }

  .mobileActionButton.sold {
    background: #3b82f6;
    color: white;
  }

  .mobileActionButton.sold:hover {
    background: #2563eb;
  }

  .mobileActionButton.onHold {
    background: #f59e0b;
    color: white;
  }

  .mobileActionButton.onHold:hover {
    background: #d97706;
  }

  .mobileActionButton.delete {
    background: #ef4444;
    color: white;
  }

  .mobileActionButton.delete:hover {
    background: #dc2626;
  }

  .mobileActionButton:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }

  .mobileActionButton:disabled:hover {
    transform: none;
    box-shadow: none;
  }
}

/* Modal Styles */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.modal {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  max-width: 400px;
  width: 90%;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal h3 {
  margin: 0 0 1rem 0;
  color: #1f2937;
  font-size: 1.25rem;
  font-weight: 600;
}

.modal p {
  margin: 0 0 1.5rem 0;
  color: #6b7280;
  line-height: 1.5;
}

.modal p strong {
  color: #1f2937;
  font-weight: 600;
}

.modalActions {
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
}

.confirmBtn {
  background: #dc2626;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.confirmBtn:hover:not(:disabled) {
  background: #b91c1c;
  transform: translateY(-1px);
}

.confirmBtn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
}

.cancelBtn {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.cancelBtn:hover:not(:disabled) {
  background: #e5e7eb;
  transform: translateY(-1px);
}

.cancelBtn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Sold status styling */
.status.sold {
  background: #059669;
  color: white;
}
