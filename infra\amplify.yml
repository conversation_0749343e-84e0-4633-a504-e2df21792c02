version: 1
frontend:
  phases:
    preBuild:
      commands:
        - nvm use 20
        - node --version
        - npm --version
        - echo "Using Node.js $(node --version) and npm $(npm --version)"
        - cd apps/frontend
        - npm ci
    build:
      commands:
        - cd apps/frontend
        - npm run build
  artifacts:
    baseDirectory: apps/frontend/.next
    files:
      - '**/*'
  cache:
    paths:
      - apps/frontend/node_modules/**/*
      - node_modules/**/*