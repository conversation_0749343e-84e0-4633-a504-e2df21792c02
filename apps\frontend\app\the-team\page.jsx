'use client';
import { useLanguage } from '../../contexts/LanguageContext';
import { FaUser } from 'react-icons/fa';
import styles from './TheTeam.module.css';

export default function TheTeam() {
  const { t } = useLanguage();

  // Team data - Currently single owner operation
  const teamSections = [
    {
      title: "LEADERSHIP",
      members: [
        {
          name: "<PERSON><PERSON>al N Chaudhry",
          position: "Owner & President",
          description: "Sales • Administration • Operations",
          email: "<EMAIL>",
          phone: "************",
          hasPhoto: false
        }
      ]
    }
  ];

  return (
    <div className={styles.teamPage}>
      <div className={styles.container}>
        <div className={styles.header}>
          <h1 className={styles.title}>Meet the Owner</h1>
        </div>

        <div className={styles.content}>
          {teamSections.map((section, sectionIndex) => (
            <div key={sectionIndex} className={styles.section}>
              <h2 className={styles.sectionTitle}>{section.title}</h2>
              <div className={styles.teamGrid}>
                {section.members.map((member, memberIndex) => (
                  <div key={memberIndex} className={styles.memberCard}>
                    <div className={styles.memberPhoto}>
                      {member.hasPhoto ? (
                        <img 
                          src={member.photo} 
                          alt={member.name}
                          className={styles.photoImage}
                        />
                      ) : (
                        <div className={styles.placeholderPhoto}>
                          <FaUser className={styles.userIcon} />
                        </div>
                      )}
                    </div>
                    <div className={styles.memberInfo}>
                      <h3 className={styles.memberName}>{member.name}</h3>
                      <p className={styles.memberPosition}>{member.position}</p>
                      {member.description && (
                        <p className={styles.memberDescription}>{member.description}</p>
                      )}
                      <p className={styles.memberEmail}>{member.email}</p>
                      <p className={styles.memberPhone}>{member.phone}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
