'use client';
import { usePathname } from 'next/navigation';
import { useLanguage } from '../../contexts/LanguageContext';
import Navbar from '../Navbar/Navbar';
import Footer from '../Footer/Footer';
import CompareBar from '../CompareBar/CompareBar';

export default function ClientLayout({ children }) {
  const pathname = usePathname();
  const isAdminPage = pathname?.startsWith('/admin');
  const isComparePage = pathname === '/compare';
  const { isLoading: translationsLoading } = useLanguage();

  return (
    <div className={`app-layout ${translationsLoading ? 'translations-loading' : ''}`}>
      <Navbar />
      <main className="main-content">
        {children}
      </main>
      {!isAdminPage && <Footer />}
      {/* Show CompareBar on all pages except admin and compare page itself */}
      {!isAdminPage && !isComparePage && <CompareBar />}
    </div>
  );
}
