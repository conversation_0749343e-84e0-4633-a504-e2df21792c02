/**
 * Analytics Helper Functions
 * Simplified interface for common analytics tracking scenarios
 */

import {
  trackVehicleView,
  trackContactForm,
  trackTestDrive,
  trackSearch,
  trackContactClick,
  trackVehicleEngagement,
  trackFinancingInterest,
  trackEvent
} from './analytics';

/**
 * Vehicle Analytics Helpers
 */

// Track when a user views a vehicle detail page
export function trackVehiclePageView(vehicle) {
  if (!vehicle) return;
  
  trackVehicleView({
    id: vehicle.id || vehicle._id,
    make: vehicle.make,
    model: vehicle.model,
    year: vehicle.year,
    price: vehicle.price,
    dealerId: vehicle.dealerId || vehicle.dealer_id
  });
}

// Track vehicle-related contact form submissions
export function trackVehicleContactForm(formType, vehicle) {
  if (!vehicle) return;
  
  trackContactForm(formType, vehicle.id || vehicle._id, {
    make: vehicle.make,
    model: vehicle.model,
    year: vehicle.year,
    price: vehicle.price,
    dealerId: vehicle.dealerId || vehicle.dealer_id
  });
}

// Track test drive requests
export function trackVehicleTestDriveRequest(vehicle) {
  if (!vehicle) return;
  
  trackTestDrive(
    vehicle.id || vehicle._id,
    vehicle.make,
    vehicle.model,
    {
      year: vehicle.year,
      price: vehicle.price,
      dealerId: vehicle.dealerId || vehicle.dealer_id
    }
  );
}

// Track financing interest
export function trackVehicleFinancingInterest(vehicle, type = 'general') {
  if (!vehicle) return;
  
  trackFinancingInterest({
    id: vehicle.id || vehicle._id,
    make: vehicle.make,
    model: vehicle.model,
    year: vehicle.year,
    price: vehicle.price,
    dealerId: vehicle.dealerId || vehicle.dealer_id
  }, type);
}

/**
 * Contact Method Helpers
 */

// Track phone clicks with vehicle context
export function trackPhoneClickWithVehicle(location, vehicle = null) {
  trackContactClick('phone', location, vehicle?.id || vehicle?._id, vehicle ? {
    make: vehicle.make,
    model: vehicle.model,
    year: vehicle.year,
    dealerId: vehicle.dealerId || vehicle.dealer_id
  } : null);
}

// Track WhatsApp clicks with vehicle context
export function trackWhatsAppClickWithVehicle(location, vehicle = null) {
  trackContactClick('whatsapp', location, vehicle?.id || vehicle?._id, vehicle ? {
    make: vehicle.make,
    model: vehicle.model,
    year: vehicle.year,
    dealerId: vehicle.dealerId || vehicle.dealer_id
  } : null);
}

// Track email clicks with vehicle context
export function trackEmailClickWithVehicle(location, vehicle = null) {
  trackContactClick('email', location, vehicle?.id || vehicle?._id, vehicle ? {
    make: vehicle.make,
    model: vehicle.model,
    year: vehicle.year,
    dealerId: vehicle.dealerId || vehicle.dealer_id
  } : null);
}

/**
 * Search Helpers
 */

// Track vehicle search with common filter format
export function trackVehicleSearch(searchTerm, filters = {}, results = []) {
  const cleanFilters = {};
  
  // Normalize common filter names
  if (filters.make) cleanFilters.make = filters.make;
  if (filters.model) cleanFilters.model = filters.model;
  if (filters.year) cleanFilters.year = filters.year;
  if (filters.minPrice) cleanFilters.min_price = filters.minPrice;
  if (filters.maxPrice) cleanFilters.max_price = filters.maxPrice;
  if (filters.bodyType) cleanFilters.body_type = filters.bodyType;
  if (filters.fuelType) cleanFilters.fuel_type = filters.fuelType;
  
  trackSearch(searchTerm, cleanFilters, Array.isArray(results) ? results.length : results);
}

/**
 * Engagement Helpers
 */

// Track vehicle image interactions
export function trackVehicleImageView(vehicle, imageIndex = null) {
  if (!vehicle) return;
  
  trackVehicleEngagement('image_view', {
    id: vehicle.id || vehicle._id,
    make: vehicle.make,
    model: vehicle.model,
    year: vehicle.year,
    dealerId: vehicle.dealerId || vehicle.dealer_id
  }, { image_index: imageIndex });
}

// Track vehicle video plays
export function trackVehicleVideoPlay(vehicle) {
  if (!vehicle) return;
  
  trackVehicleEngagement('video_play', {
    id: vehicle.id || vehicle._id,
    make: vehicle.make,
    model: vehicle.model,
    year: vehicle.year,
    dealerId: vehicle.dealerId || vehicle.dealer_id
  });
}

// Track feature section expansions
export function trackVehicleFeatureExpansion(vehicle, featureCategory) {
  if (!vehicle) return;
  
  trackVehicleEngagement('feature_expand', {
    id: vehicle.id || vehicle._id,
    make: vehicle.make,
    model: vehicle.model,
    year: vehicle.year,
    dealerId: vehicle.dealerId || vehicle.dealer_id
  }, { feature_category: featureCategory });
}

/**
 * Quick Setup Functions
 */

// Set up analytics for a vehicle detail page (call this in useEffect)
export function setupVehicleDetailAnalytics(vehicle) {
  if (!vehicle) return;
  
  // Track the page view
  trackVehiclePageView(vehicle);
  
  // Return helper functions bound to this vehicle
  return {
    trackContact: (formType) => trackVehicleContactForm(formType, vehicle),
    trackTestDrive: () => trackVehicleTestDriveRequest(vehicle),
    trackFinancing: (type) => trackVehicleFinancingInterest(vehicle, type),
    trackPhoneClick: (location) => trackPhoneClickWithVehicle(location, vehicle),
    trackWhatsAppClick: (location) => trackWhatsAppClickWithVehicle(location, vehicle),
    trackEmailClick: (location) => trackEmailClickWithVehicle(location, vehicle),
    trackImageView: (index) => trackVehicleImageView(vehicle, index),
    trackVideoPlay: () => trackVehicleVideoPlay(vehicle),
    trackFeatureExpand: (category) => trackVehicleFeatureExpansion(vehicle, category)
  };
}

/**
 * Common Event Patterns
 */

// Track a complete contact flow (form view -> submission -> success)
export function trackContactFlow(step, formType, vehicle = null, success = null) {
  const baseData = {
    form_type: formType,
    flow_step: step, // 'view', 'submit', 'success', 'error'
  };
  
  if (vehicle) {
    baseData.vehicle_id = vehicle.id || vehicle._id;
    baseData.vehicle_make = vehicle.make;
    baseData.vehicle_model = vehicle.model;
  }
  
  if (success !== null) {
    baseData.success = success;
  }
  
  trackEvent('contact_flow', baseData);
}

// Track user journey through vehicle browsing
export function trackVehicleBrowsingJourney(action, data = {}) {
  trackEvent('browsing_journey', {
    journey_action: action, // 'search', 'filter', 'view_list', 'view_detail', 'contact'
    ...data
  });
}
