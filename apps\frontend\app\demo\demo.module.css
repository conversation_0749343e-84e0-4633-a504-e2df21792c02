.container {
  min-height: 100vh;
  background: #ffffff;
  padding: 2rem 1rem;
  transition: background 0.3s ease;
}

/* Dark mode: Apply consistent gradient background */
[data-theme="dark"] .container {
  background: var(--bg-primary);
}

/* Responsive padding adjustments for larger screens */
@media (min-width: 769px) {
  .container {
    padding: 2rem 3rem;
  }
}

@media (min-width: 1200px) {
  .container {
    padding: 2rem 4rem;
  }
}

.header {
  text-align: center;
  margin-bottom: 3rem;
}

.title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.subtitle {
  font-size: 1.125rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

.content {
  max-width: none;
  margin: 0;
}

.showcase {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.featureCard {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: 0.75rem;
  padding: 2rem;
  box-shadow: var(--card-shadow);
  transition: box-shadow 0.2s ease;
}

.featureCard:hover {
  box-shadow: var(--card-shadow-hover);
}

.cardTitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.cardDescription {
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.cardDemo {
  display: flex;
  justify-content: center;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: var(--bg-secondary);
  border-radius: 0.5rem;
  border: 1px solid var(--border-primary);
}

.themeInfo,
.languageInfo,
.analyticsInfo {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: var(--bg-tertiary);
  border-radius: 0.5rem;
  border: 1px solid var(--border-primary);
}

.infoLabel {
  font-weight: 500;
  color: var(--text-secondary);
}

.infoValue {
  font-weight: 600;
  color: var(--text-primary);
}

.testButton {
  background: var(--accent-primary);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.testButton:hover {
  background: var(--accent-hover);
}

.sampleContent {
  margin-bottom: 4rem;
}

.sectionTitle {
  font-size: 2rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 2rem;
  text-align: center;
}

.contentGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.contentCard {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: var(--card-shadow);
}

.contentCard h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.contentCard p {
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.cardActions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.settingsDemo {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: 0.75rem;
  padding: 2rem;
  box-shadow: var(--card-shadow);
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    padding: 1rem 0.5rem;
  }
  
  .title {
    font-size: 2rem;
  }
  
  .showcase {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .featureCard {
    padding: 1.5rem;
  }
  
  .cardActions {
    flex-direction: column;
  }
  
  .themeInfo,
  .languageInfo,
  .analyticsInfo {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }
}
