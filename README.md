dev
HEAD
# FazeNAuto - Car Dealership Management System (Monorepo)

A modern, full-stack car dealership management system built with Next.js, MongoDB, and AWS S3. Organized as a clean monorepo with domain-driven architecture for scalability and maintainability.

## 🚀 Features

### 🏪 **Dealership Management**
- **Vehicle Inventory Management** - Add, edit, delete, and manage vehicle listings
- **Image & Video Upload** - AWS S3 integration for media storage
- **VIN Decoder Integration** - Auto-populate vehicle details
- **Advanced Search & Filtering** - Find vehicles by make, model, year, price, etc.

### 👥 **User Management**
- **Role-Based Access Control** - Admin and Dealer roles
- **Secure Authentication** - Email-based login system
- **User Dashboard** - Personalized admin interface

### 📤 **Marketplace Syndication**
- **Facebook Marketplace** - Auto-post vehicles to Facebook
- **CSV Export** - Export to Craigslist, AutoTrader, Kijiji
- **Custom Templates** - Platform-specific formatting
- **Syndication Tracking** - Monitor posting status and performance

### 📊 **Analytics & Reporting**
- **Performance Metrics** - Track views, inquiries, and sales
- **Database Optimization** - Indexed queries for fast performance
- **Caching System** - Redis/in-memory caching for speed

## 🛠️ Tech Stack

- **Frontend**: Next.js 15, React 19, CSS Modules
- **Backend**: Domain-driven architecture with MongoDB
- **Database**: MongoDB with Mongoose ODM
- **Storage**: AWS S3 for images and videos
- **Authentication**: Custom JWT-based auth
- **Deployment**: AWS Amplify
- **Styling**: Pure CSS (no frameworks)
- **Infrastructure**: Terraform for AWS resources

## 📁 Monorepo Structure

```
fazenauto/
├── apps/
│   ├── frontend/               # Next.js frontend application
│   │   ├── src/
│   │   │   ├── app/           # Next.js App Router pages
│   │   │   ├── components/    # React components
│   │   │   ├── styles/        # CSS stylesheets
│   │   │   └── assets/        # Static assets
│   │   ├── public/            # Public files
│   │   ├── .env.local         # Frontend environment variables
│   │   └── package.json       # Frontend dependencies
│   └── api/                   # Backend business logic
│       ├── domains/
│       │   ├── vehicles/      # Vehicle management domain
│       │   ├── users/         # User management domain
│       │   └── syndication/   # Marketplace syndication domain
│       ├── lib/               # Shared backend utilities
│       ├── .env.local         # Backend environment variables
│       └── package.json       # Backend dependencies
├── packages/
│   └── utils/                 # Shared utilities (S3, helpers)
├── infra/                     # Infrastructure as code
│   ├── amplify.yml           # AWS Amplify configuration
│   └── s3-bucket.tf          # Terraform S3 configuration
├── scripts/                   # Database and utility scripts
├── docs/                      # Documentation
└── package.json              # Monorepo root configuration
```

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- MongoDB (local or Atlas)
- AWS Account (for S3 storage)

### Installation
```bash
# Clone the repository
git clone <repository-url>
cd fazenauto

# Install all dependencies
npm run install-all

# Or install individually
npm install                    # Root dependencies
cd apps/frontend && npm install # Frontend dependencies
cd ../api && npm install       # Backend dependencies
cd ../../packages/utils && npm install # Utils dependencies
```

### Environment Setup
1. **Frontend** (`apps/frontend/.env.local`):
```env
NEXT_PUBLIC_BASE_URL=https://fazenauto.com
CUSTOM_AWS_REGION=us-east-1
CUSTOM_AWS_S3_BUCKET_NAME=fazenauto-vehicle-images
ADMIN_SECRET=your_admin_secret
AUTHORIZED_EMAILS=<EMAIL>,<EMAIL>
```

2. **Backend** (`apps/api/.env`):
```env

### Development
```bash
# Start the development server
npm run dev

# Initialize database indexes
npm run init-indexes

# Validate database performance
npm run validate-indexes
```


## 🏗️ Architecture Overview

### Domain-Driven Design
The backend is organized into three main domains:

1. **Vehicles Domain** (`apps/api/domains/vehicles/`)
   - Vehicle CRUD operations
   - Image/video management
   - VIN decoding
   - Search and filtering

2. **Users Domain** (`apps/api/domains/users/`)
   - Authentication and authorization
   - Role management (admin/dealer)
   - User profile management

3. **Syndication Domain** (`apps/api/domains/syndication/`)
   - Marketplace integrations
   - CSV export functionality
   - Syndication logging and tracking

### Shared Libraries
- **Database Connection** (`apps/api/lib/dbConnect.js`)
- **Caching System** (`apps/api/lib/cache.js`)
- **S3 Utilities** (`packages/utils/s3.js`)
- **Database Indexes** (`apps/api/lib/dbIndexes.js`)

## 🚀 Deployment

### AWS Amplify
The application is configured for AWS Amplify deployment:

```bash
# Build configuration is in infra/amplify.yml
# Amplify will automatically:
# 1. Install dependencies in apps/frontend
# 2. Build the Next.js application
# 3. Deploy to AWS infrastructure
```

### Manual Deployment
```bash
# Build the frontend
cd apps/frontend
npm run build

# The built application will be in apps/frontend/.next
```

## 📚 Documentation

- **Frontend Documentation**: [apps/frontend/README.md](apps/frontend/README.md)
- **Backend Documentation**: [apps/api/README.md](apps/api/README.md)
- **Marketplace Integration Guide**: [docs/MARKETPLACE_INTEGRATION_GUIDE.md](docs/MARKETPLACE_INTEGRATION_GUIDE.md)
- **Admin Setup Guide**: [ADMIN_SETUP.md](ADMIN_SETUP.md)

## 🔧 Development Scripts

```bash
# Monorepo management
npm run dev              # Start frontend development server
npm run build            # Build frontend for production
npm run start            # Start frontend production server
npm run lint             # Lint frontend code
npm run install-all      # Install all dependencies

# Database management
npm run init-indexes     # Initialize database indexes
npm run validate-indexes # Validate database performance
```

## 🤝 Contributing

1. **Follow the domain structure** - Keep related functionality within appropriate domains
2. **Use path aliases** - Leverage the configured import aliases for clean imports
3. **Maintain CSS-only styling** - No Tailwind or styled-components
4. **Test thoroughly** - Ensure all integrations work properly
5. **Update documentation** - Keep README files current with changes

## 📄 License

This project is private and proprietary to FazeNAuto.

## 🆘 Support

For technical support or questions:
- **Email**: <EMAIL>
- **Phone**: ************
- **Address**: 123 Business Ave, Toronto, ON


**FazeNAuto** - Your trusted automotive partner 🚗

FazeNAuto is a modern car sales website built with Next.js and hosted on AWS.
Testing...

