.container {
  padding: 2rem;
  max-width: 800px;
  margin: 0 auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  gap: 2rem;
}

.titleSection h1 {
  margin: 0 0 0.5rem 0;
  color: #1a1a1a;
  font-size: 2rem;
}

.titleSection p {
  margin: 0;
  color: #666;
  font-size: 1rem;
}

.backButton {
  background: #6b7280;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 500;
  transition: background-color 0.2s;
  white-space: nowrap;
}

.backButton:hover {
  background: #4b5563;
}

/* Success Message */
.successMessage {
  text-align: center;
  padding: 4rem 2rem;
  color: #10b981;
}

.successIcon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.successMessage h2 {
  margin: 0 0 1rem 0;
  color: #10b981;
}

.successMessage p {
  color: #6b7280;
}

/* Error Message */
.error {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 2rem;
  white-space: pre-line;
}

/* Form Styles */
.form {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.formGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.formGroup {
  margin-bottom: 1.5rem;
}

.label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
}

.required {
  color: #dc2626;
}

.input,
.select,
.textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s, box-shadow 0.2s;
  box-sizing: border-box;
}

.input:focus,
.select:focus,
.textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.textarea {
  resize: vertical;
  min-height: 80px;
}

/* Amount Group */
.amountGroup {
  display: flex;
  gap: 0.5rem;
}

.amountGroup .input {
  flex: 1;
}

.currencySelect {
  width: 80px;
  padding: 0.75rem 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
  background: white;
}

.currencySelect:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Character Count */
.charCount {
  display: block;
  margin-top: 0.25rem;
  color: #6b7280;
  font-size: 0.75rem;
  text-align: right;
}

/* Vehicle Search */
.vehicleSearch {
  position: relative;
}

.searchLoading {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
  font-size: 0.875rem;
}

.vehicleOptions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #d1d5db;
  border-top: none;
  border-radius: 0 0 8px 8px;
  max-height: 200px;
  overflow-y: auto;
  z-index: 10;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.vehicleOption {
  padding: 0.75rem;
  cursor: pointer;
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.2s;
}

.vehicleOption:hover {
  background: #f9fafb;
}

.vehicleOption:last-child {
  border-bottom: none;
}

.vehicleOption strong {
  display: block;
  color: #374151;
  margin-bottom: 0.25rem;
}

.vehicleOption small {
  color: #6b7280;
  text-transform: capitalize;
}

/* File Upload */
.fileUpload {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.fileInput {
  display: none;
}

.fileButton {
  background: #3b82f6;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
  align-self: flex-start;
}

.fileButton:hover {
  background: #2563eb;
}

.fileHint {
  color: #6b7280;
  font-size: 0.875rem;
}

/* File List */
.fileList {
  margin-top: 1rem;
  padding: 1rem;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.fileList h4 {
  margin: 0 0 1rem 0;
  color: #374151;
  font-size: 0.875rem;
  font-weight: 600;
}

.fileItem {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.5rem 0;
  border-bottom: 1px solid #e5e7eb;
}

.fileItem:last-child {
  border-bottom: none;
}

.fileName {
  flex: 1;
  color: #374151;
  font-size: 0.875rem;
}

.fileSize {
  color: #6b7280;
  font-size: 0.75rem;
}

.removeFile {
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 4px;
  width: 24px;
  height: 24px;
  cursor: pointer;
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.removeFile:hover {
  background: #dc2626;
}

/* Submit Section */
.submitSection {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #e5e7eb;
  text-align: center;
}

.submitButton {
  background: #10b981;
  color: white;
  padding: 1rem 2rem;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  font-weight: 600;
  font-size: 1rem;
  transition: background-color 0.2s;
  min-width: 200px;
}

.submitButton:hover:not(:disabled) {
  background: #059669;
}

.submitButton:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }

  .header {
    flex-direction: column;
    align-items: stretch;
  }

  .form {
    padding: 1.5rem;
  }

  .formGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .amountGroup {
    flex-direction: column;
  }

  .currencySelect {
    width: 100%;
  }

  .vehicleOptions {
    position: fixed;
    left: 1rem;
    right: 1rem;
    top: auto;
    max-height: 300px;
  }
}
