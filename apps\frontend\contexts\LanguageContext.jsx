'use client';

import { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { getTranslation, loadTranslation } from '../lib/translations';

const LanguageContext = createContext();

export function LanguageProvider({ children }) {
  const [currentLanguage, setCurrentLanguage] = useState('en');
  const [isLoading, setIsLoading] = useState(true);
  const [, forceUpdate] = useState({});

  useEffect(() => {
    const initializeLanguage = async () => {
      try {
        // Only access localStorage on client side
        if (typeof window !== 'undefined') {
          const savedSettings = localStorage.getItem('userSettings');
          let language = 'en';

          if (savedSettings) {
            try {
              const parsedSettings = JSON.parse(savedSettings);
              language = parsedSettings.preferences?.language || 'en';
            } catch (error) {
              console.error('Error parsing saved settings:', error);
            }
          }

          setCurrentLanguage(language);

          // Only access document on client side
          if (typeof document !== 'undefined') {
            document.documentElement.lang = language;

            // Set document direction for RTL languages
            const rtlLanguages = ['ar', 'ur'];
            if (rtlLanguages.includes(language)) {
              document.documentElement.dir = 'rtl';
            } else {
              document.documentElement.dir = 'ltr';
            }
          }

          // Initialize legacy translation system
          await loadTranslation(language);
        }
      } catch (error) {
        console.error('Error initializing language:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initializeLanguage();
  }, []);



  const changeLanguage = async (newLanguage) => {
    setCurrentLanguage(newLanguage);

    // Only access document on client side
    if (typeof document !== 'undefined') {
      document.documentElement.lang = newLanguage;

      // Set document direction for RTL languages
      const rtlLanguages = ['ar', 'ur'];
      if (rtlLanguages.includes(newLanguage)) {
        document.documentElement.dir = 'rtl';
      } else {
        document.documentElement.dir = 'ltr';
      }
    }

    try {
      // Load translation for legacy system
      await loadTranslation(newLanguage);

      // Only access localStorage on client side
      if (typeof window !== 'undefined') {
        const savedSettings = localStorage.getItem('userSettings');
        let settings = {};

        if (savedSettings) {
          try {
            settings = JSON.parse(savedSettings);
          } catch (error) {
            console.error('Error parsing saved settings:', error);
          }
        }

        // Ensure preferences object exists
        if (!settings.preferences) {
          settings.preferences = {};
        }

        settings.preferences.language = newLanguage;
        localStorage.setItem('userSettings', JSON.stringify(settings));
      }

      // Force re-render of all components using this context
      forceUpdate({});
    } catch (error) {
      console.error('Error changing language:', error);
    }
  };

  // Create a reactive translation function that updates when language changes
  const t = useCallback((key, fallback = key) => {
    // Use the legacy translation system for now
    const translation = getTranslation(currentLanguage);
    const result = translation(key, fallback);

    // Debug logging for test pages
    if (typeof window !== 'undefined' && window.location.pathname.includes('test-features')) {
      console.log(`🌐 Translation: "${key}" -> "${result}" (lang: ${currentLanguage})`);
    }

    return result;
  }, [currentLanguage]);

  const value = {
    currentLanguage,
    changeLanguage,
    t,
    isLoading
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}
