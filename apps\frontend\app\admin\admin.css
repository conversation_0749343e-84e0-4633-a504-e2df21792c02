.admin-layout {
  display: flex;
  position: relative;
  min-height: 100vh;
  background-color: var(--bg-secondary, #f9fafb);
  transition: background-color 0.3s ease;
}

/* Dark mode: Apply consistent gradient background to admin layout */
[data-theme="dark"] .admin-layout {
  background: var(--bg-primary);
}

.admin-content {
  margin-left: 60px; /* Start with collapsed sidebar width */
  padding: 0;
  width: calc(100% - 60px); /* Account for collapsed sidebar width */
  background-color: transparent;
  color: var(--text-primary);
  min-height: 100vh;
  transition: margin-left 0.3s ease,
              width 0.3s ease,
              background-color 0.3s ease;
  position: relative;
  z-index: 1;
  flex: 1;
}

/* Dark mode: Admin content should be transparent (layout provides gradient) */
[data-theme="dark"] .admin-content {
  background-color: transparent;
}

/* Sidebar Open State - Fixed 220px width for consistency */
.admin-layout:not(.sidebar-collapsed) .admin-content {
  margin-left: 220px;
  width: calc(100% - 220px);
}

/* Collapsed Sidebar State (default) */
.admin-layout.sidebar-collapsed .admin-content {
  margin-left: 0;
  width: 100%;
}

/* Responsive Design Fixes */
/* Large screens (1200px+) - Perfect as is */
@media (min-width: 1200px) {
  .admin-content {
    padding: 0;
  }
}

/* Desktop screens (768px and above) - Consistent 220px sidebar width */
@media (min-width: 768px) {
  .admin-content {
    padding: 0;
  }

  .admin-layout:not(.sidebar-collapsed) .admin-content {
    margin-left: 220px; /* Fixed 220px to match expanded sidebar width */
    width: calc(100% - 220px);
  }

  .admin-layout.sidebar-collapsed .admin-content {
    margin-left: 60px; /* Fixed 60px to match collapsed sidebar width */
    width: calc(100% - 60px);
  }
}

/* Mobile responsive - sidebar overlays content */
@media (max-width: 767px) {
  .admin-content {
    margin-left: 0 !important;
    width: 100% !important;
  }
}

/* Mobile Responsive Design */
@media (max-width: 767px) {
  .admin-layout {
    flex-direction: column;
  }

  .admin-content {
    margin-left: 0;
    padding: 0;
    width: 100%;
    position: relative;
    z-index: 1;
  }

  /* Ensure content is always full width on mobile regardless of sidebar state */
  .admin-layout:not(.sidebar-collapsed) .admin-content,
  .admin-layout.sidebar-collapsed .admin-content {
    margin-left: 0;
    width: 100%;
  }

  /* Prevent content from being clickable when sidebar is open on mobile */
  .admin-layout:not(.sidebar-collapsed) .admin-content {
    pointer-events: none;
  }

  /* Re-enable pointer events for content when sidebar is collapsed */
  .admin-layout.sidebar-collapsed .admin-content {
    pointer-events: auto;
  }
}

@media (max-width: 480px) {
  .admin-content {
    padding: 0.5rem;
  }
}