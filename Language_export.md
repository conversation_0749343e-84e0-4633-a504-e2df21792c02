# 🌐 Complete Multilingual Implementation Guide

## 📋 Overview

This document provides a comprehensive guide to implementing the multilingual system used in the FazeNAuto project. The implementation supports 5 languages with full RTL (Right-to-Left) support, dynamic language switching, and persistent user preferences.

## 🏗️ Architecture Overview

### Supported Languages
- **English (en)** - Default language, LTR
- **Spanish (es)** - LTR
- **French (fr)** - LTR  
- **Urdu (ur)** - RTL
- **Arabic (ar)** - RTL

### Core Components
1. **LanguageContext** - React Context for global language state
2. **Translation System** - Hybrid JS+JSON translation loader
3. **LanguageSelector** - UI component for language switching
4. **RTL Support** - CSS and layout adjustments for RTL languages
5. **Persistent Storage** - localStorage integration for user preferences

## 📦 Required Dependencies

### Package.json Dependencies
```json
{
  "dependencies": {
    "react": "^19.0.0",
    "react-dom": "^19.0.0",
    "next": "^15.3.2"
  }
}
```

**Note:** This implementation uses a custom translation system. For more advanced features, consider adding:
```json
{
  "i18next": "^25.3.1",
  "i18next-browser-languagedetector": "^8.2.0",
  "react-i18next": "^15.6.0"
}
```

## 🗂️ File Structure

```
apps/frontend/
├── contexts/
│   └── LanguageContext.jsx          # Main language context
├── components/
│   └── LanguageSelector/
│       ├── LanguageSelector.jsx     # Language selector component
│       └── LanguageSelector.module.css
├── lib/
│   └── translations.js              # Translation utilities
├── locales/                         # Translation files
│   ├── en/translation.json         # English translations
│   ├── es/translation.json         # Spanish translations
│   ├── fr/translation.json         # French translations
│   ├── ur/translation.json         # Urdu translations
│   └── ar/translation.json         # Arabic translations
├── scripts/
│   └── convertTranslations.js      # Conversion utility
└── app/
    ├── layout.jsx                   # Root layout with providers
    └── globals.css                  # Global RTL/LTR styles
```

## 🔧 Implementation Steps

### Step 1: Create Language Context

Create `contexts/LanguageContext.jsx`:

```jsx
'use client';

import { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { getTranslation, loadTranslation } from '../lib/translations';

const LanguageContext = createContext();

export function LanguageProvider({ children }) {
  const [currentLanguage, setCurrentLanguage] = useState('en');
  const [isLoading, setIsLoading] = useState(true);
  const [, forceUpdate] = useState({});

  useEffect(() => {
    const initializeLanguage = async () => {
      try {
        if (typeof window !== 'undefined') {
          const savedSettings = localStorage.getItem('userSettings');
          let language = 'en';

          if (savedSettings) {
            try {
              const parsedSettings = JSON.parse(savedSettings);
              language = parsedSettings.preferences?.language || 'en';
            } catch (error) {
              console.error('Error parsing saved settings:', error);
            }
          }

          setCurrentLanguage(language);

          if (typeof document !== 'undefined') {
            document.documentElement.lang = language;

            // Set document direction for RTL languages
            const rtlLanguages = ['ar', 'ur'];
            if (rtlLanguages.includes(language)) {
              document.documentElement.dir = 'rtl';
            } else {
              document.documentElement.dir = 'ltr';
            }
          }

          await loadTranslation(language);
        }
      } catch (error) {
        console.error('Error initializing language:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initializeLanguage();
  }, []);

  const changeLanguage = async (newLanguage) => {
    setCurrentLanguage(newLanguage);

    if (typeof document !== 'undefined') {
      document.documentElement.lang = newLanguage;

      const rtlLanguages = ['ar', 'ur'];
      if (rtlLanguages.includes(newLanguage)) {
        document.documentElement.dir = 'rtl';
      } else {
        document.documentElement.dir = 'ltr';
      }
    }

    try {
      await loadTranslation(newLanguage);

      if (typeof window !== 'undefined') {
        const savedSettings = localStorage.getItem('userSettings');
        let settings = {};

        if (savedSettings) {
          try {
            settings = JSON.parse(savedSettings);
          } catch (error) {
            console.error('Error parsing saved settings:', error);
          }
        }

        if (!settings.preferences) {
          settings.preferences = {};
        }

        settings.preferences.language = newLanguage;
        localStorage.setItem('userSettings', JSON.stringify(settings));
      }

      forceUpdate({});
    } catch (error) {
      console.error('Error changing language:', error);
    }
  };

  const t = useCallback((key, fallback = key) => {
    const translation = getTranslation(currentLanguage);
    return translation(key, fallback);
  }, [currentLanguage]);

  const value = {
    currentLanguage,
    changeLanguage,
    t,
    isLoading
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}
```

### Step 2: Create Translation System

Create `lib/translations.js`:

```jsx
// Translation files loader
const translationFiles = {
  en: () => import('../locales/en/translation.json'),
  es: () => import('../locales/es/translation.json'),
  fr: () => import('../locales/fr/translation.json'),
  ur: () => import('../locales/ur/translation.json'),
  ar: () => import('../locales/ar/translation.json'),
};

// Cache for loaded translations
const translationCache = {};

export async function loadTranslation(language) {
  if (translationCache[language]) {
    return translationCache[language];
  }

  try {
    const translationModule = await translationFiles[language]();
    const translation = translationModule.default || translationModule;
    translationCache[language] = translation;
    return translation;
  } catch (error) {
    console.error(`Failed to load translation for ${language}:`, error);
    if (language !== 'en') {
      return loadTranslation('en');
    }
    return {};
  }
}

export function getTranslation(language) {
  return (key, fallback = key) => {
    try {
      const translation = translationCache[language] || {};

      if (translation[key]) {
        return translation[key];
      }

      if (key.includes('.')) {
        const keys = key.split('.');
        let value = translation;

        for (const k of keys) {
          if (value && typeof value === 'object' && k in value) {
            value = value[k];
          } else {
            return fallback;
          }
        }

        return typeof value === 'string' ? value : fallback;
      }

      return fallback;
    } catch (error) {
      console.error('Translation error:', error);
      return fallback;
    }
  };
}
```

### Step 3: Create Language Selector Component

Create `components/LanguageSelector/LanguageSelector.jsx`:

```jsx
'use client';

import { useLanguage } from '../../contexts/LanguageContext';
import styles from './LanguageSelector.module.css';

const languages = [
  { code: 'en', name: 'English', flag: '🇺🇸' },
  { code: 'es', name: 'Español', flag: '🇪🇸' },
  { code: 'fr', name: 'Français', flag: '🇫🇷' },
  { code: 'ur', name: 'اردو', flag: '🇵🇰' },
  { code: 'ar', name: 'العربية', flag: '🇸🇦' },
];

export default function LanguageSelector({
  showLabel = true,
  showFlags = true,
  variant = 'select',
  className = ''
}) {
  const { currentLanguage, changeLanguage, t } = useLanguage();

  const handleLanguageChange = (e) => {
    changeLanguage(e.target.value);
  };

  return (
    <div className={`${styles.container} ${className}`}>
      {showLabel && (
        <label className={styles.label}>
          {t('settings.language', 'Language')}
        </label>
      )}
      <select
        value={currentLanguage}
        onChange={handleLanguageChange}
        className="formInput"
        aria-label={t('settings.language', 'Select language')}
      >
        {languages.map((lang) => (
          <option key={lang.code} value={lang.code}>
            {showFlags ? `${lang.flag} ${lang.name}` : lang.name}
          </option>
        ))}
      </select>
    </div>
  );
}
```

Create `components/LanguageSelector/LanguageSelector.module.css`:

```css
.container {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
}

/* RTL Support for Language Selector */
[dir="rtl"] .container {
  direction: rtl;
}

[dir="rtl"] .dropdownButton {
  flex-direction: row-reverse;
}
```

### Step 4: Setup Root Layout

Update `app/layout.jsx`:

```jsx
import "./globals.css";
import { Suspense } from "react";
import { ThemeProvider } from "../contexts/ThemeContext";
import { LanguageProvider } from "../contexts/LanguageContext";
import ClientLayout from "../components/ClientLayout/ClientLayout";

export const metadata = {
  title: "Your App Name",
  description: "Your App Description",
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body className="root-layout">
        <ThemeProvider>
          <LanguageProvider>
            <ClientLayout>
              {children}
            </ClientLayout>
          </LanguageProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
```

### Step 5: Add RTL/LTR CSS Support

Add to `app/globals.css`:

```css
/* RTL Language Support */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] .navbar {
  direction: rtl;
}

[dir="rtl"] .dropdown-menu {
  right: 0;
  left: auto;
}

[dir="rtl"] .sidebar {
  right: 0;
  left: auto;
}

[dir="rtl"] .formInput {
  text-align: right;
}

[dir="rtl"] .button {
  direction: rtl;
}

[dir="rtl"] .card {
  direction: rtl;
}

[dir="rtl"] .flex {
  direction: rtl;
}

/* Language-specific font families */
[lang="ar"], [lang="ar"] * {
  font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
  direction: rtl;
  text-align: right;
}

[lang="ur"], [lang="ur"] * {
  font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
  direction: rtl;
  text-align: right;
}

/* Ensure proper spacing for RTL languages */
[dir="rtl"] .gap-2 > * + * {
  margin-right: 0.5rem;
  margin-left: 0;
}

[dir="rtl"] .gap-4 > * + * {
  margin-right: 1rem;
  margin-left: 0;
}
```

## 📝 Translation Files Structure

### Sample Translation File (`locales/en/translation.json`):

```json
{
  "nav.home": "Home",
  "nav.inventory": "Inventory",
  "nav.services": "Services",
  "nav.about": "About",
  "nav.contact": "Contact",
  "nav.login": "Login",
  "nav.logout": "Logout",

  "settings.title": "Settings",
  "settings.subtitle": "Manage your account preferences and settings",
  "settings.language": "Language",
  "settings.language.description": "Select your preferred language",
  "settings.theme": "Theme",

  "common.loading": "Loading...",
  "common.save": "Save",
  "common.cancel": "Cancel",
  "common.edit": "Edit",
  "common.delete": "Delete",
  "common.close": "Close",
  "common.search": "Search",

  "home.hero.title": "Welcome to Our Platform",
  "home.hero.subtitle": "Your trusted partner for all your needs",
  "home.about.title": "About Us",
  "home.services.title": "Our Services",

  "footer.brand_description": "Your company description here",
  "footer.contact.title": "Contact Information",
  "footer.copyright": "© 2024 Your Company. All rights reserved.",

  "vehicle.km": "km",
  "vehicle.odometer": "Odometer:",
  "vehicle.engine": "Engine:",
  "vehicle.transmission": "Transmission:",
  "vehicle.fuel_type": "Fuel Type:",
  "vehicle.description": "Description",
  "vehicle.features_options": "Features & Options",
  "vehicle.disclaimer": "All vehicles are sold as-is. Prices do not include taxes, licensing, and other fees."
}
```

### Key Naming Convention

Use dot notation for hierarchical organization:
- `nav.*` - Navigation items
- `settings.*` - Settings page content
- `common.*` - Common UI elements
- `home.*` - Home page content
- `footer.*` - Footer content
- `vehicle.*` - Vehicle-related content

## 🚀 Usage Examples

### Basic Translation Usage

```jsx
'use client';

import { useLanguage } from '../contexts/LanguageContext';

export default function MyComponent() {
  const { t, currentLanguage } = useLanguage();

  return (
    <div>
      <h1>{t('nav.home', 'Home')}</h1>
      <p>{t('settings.title', 'Settings')}</p>
      <button>{t('common.save', 'Save')}</button>
      <span>Current: {currentLanguage}</span>
    </div>
  );
}
```

### Language Selector Usage

```jsx
import LanguageSelector from '../components/LanguageSelector/LanguageSelector';

// Basic usage
<LanguageSelector />

// With custom props
<LanguageSelector
  showLabel={true}
  showFlags={true}
  variant="select"
  className="my-custom-class"
/>
```

### Conditional RTL Styling

```jsx
'use client';

import { useLanguage } from '../contexts/LanguageContext';

export default function MyComponent() {
  const { currentLanguage } = useLanguage();
  const isRTL = ['ar', 'ur'].includes(currentLanguage);

  return (
    <div className={isRTL ? 'rtl-layout' : 'ltr-layout'}>
      {/* Your content */}
    </div>
  );
}
```

## 🔧 Advanced Features

### Translation Conversion Script

Create `scripts/convertTranslations.js` for converting JS translations to JSON:

```javascript
#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const translationsPath = path.resolve(__dirname, '../lib/translations.js');

async function loadTranslations() {
  try {
    const fileUrl = `file://${translationsPath.replace(/\\/g, '/')}`;
    const module = await import(fileUrl);
    return module.translations;
  } catch (error) {
    console.error('❌ Error loading translations:', error.message);
    process.exit(1);
  }
}

async function convertTranslations() {
  console.log('🔄 Converting translations to individual JSON files...\n');

  const translations = await loadTranslations();
  const baseDir = path.resolve(__dirname, '../locales');

  if (!fs.existsSync(baseDir)) {
    fs.mkdirSync(baseDir, { recursive: true });
  }

  Object.entries(translations).forEach(([lang, content]) => {
    const langDir = path.join(baseDir, lang);

    if (!fs.existsSync(langDir)) {
      fs.mkdirSync(langDir, { recursive: true });
    }

    const filePath = path.join(langDir, 'translation.json');
    const jsonContent = JSON.stringify(content, null, 2);

    fs.writeFileSync(filePath, jsonContent, 'utf-8');
    console.log(`✅ Wrote: ${filePath}`);
  });

  console.log('\n🎉 Translation conversion completed successfully!');
}

convertTranslations().catch(error => {
  console.error('❌ Conversion failed:', error);
  process.exit(1);
});
```

### Persistent Storage Integration

The system automatically saves language preferences to localStorage:

```javascript
// Storage format in localStorage
{
  "preferences": {
    "language": "en",
    "theme": "dark"
  }
}
```

### RTL Layout Considerations

For RTL languages, the system automatically:
1. Sets `document.documentElement.dir = 'rtl'`
2. Sets `document.documentElement.lang = 'ar'` (or 'ur')
3. Applies RTL-specific CSS styles
4. Adjusts flex directions and margins

## 🎯 Best Practices

### 1. Translation Key Organization
- Use consistent dot notation: `section.subsection.item`
- Group related translations together
- Keep keys descriptive but concise

### 2. Fallback Strategy
- Always provide fallback text as second parameter
- English should be the fallback language
- Handle missing translations gracefully

### 3. RTL Support
- Test all layouts in both LTR and RTL modes
- Use logical CSS properties when possible
- Consider text expansion/contraction between languages

### 4. Performance
- Translations are cached after first load
- Only load translations for current language
- Use dynamic imports for lazy loading

## 🐛 Troubleshooting

### Common Issues

**1. Translations not loading**
```javascript
// Check if translation files exist
console.log('Translation cache:', translationCache);

// Verify file paths
const translationFiles = {
  en: () => import('../locales/en/translation.json'),
  // ... other languages
};
```

**2. RTL styles not applying**
```css
/* Ensure RTL detection is working */
[dir="rtl"] .debug {
  background: red; /* Should show red background for RTL */
}
```

**3. Language not persisting**
```javascript
// Check localStorage
console.log(localStorage.getItem('userSettings'));

// Verify JSON structure
const settings = JSON.parse(localStorage.getItem('userSettings') || '{}');
console.log(settings.preferences?.language);
```

## 📋 Migration Checklist

When implementing this system in a new project:

- [ ] Install required dependencies
- [ ] Create contexts/LanguageContext.jsx
- [ ] Create lib/translations.js
- [ ] Create components/LanguageSelector/
- [ ] Setup locales/ directory structure
- [ ] Add translation JSON files for each language
- [ ] Update root layout with LanguageProvider
- [ ] Add RTL/LTR CSS styles to globals.css
- [ ] Test all languages and RTL layouts
- [ ] Verify localStorage persistence
- [ ] Add language selector to UI components
- [ ] Update all hardcoded text to use t() function

## 🔗 Integration Notes

### Next.js Specific
- Uses 'use client' directive for client-side functionality
- Compatible with Next.js App Router
- Supports SSR with proper client-side hydration

### State Management
- Uses React Context for global state
- Integrates with localStorage for persistence
- Force re-renders on language change

### Accessibility
- Proper lang attributes on HTML elements
- ARIA labels for language selector
- Screen reader friendly

This implementation provides a robust, scalable multilingual system that can be easily adapted to any React/Next.js project.
