# Development Environment Guide

This guide explains how to set up and work with the FazeNAuto development environment like a pro! 🛠️

## 🏗️ Project Structure Overview

```
app/
├── apps/
│   ├── frontend/          # Next.js React application
│   │   ├── app/           # App Router pages and API routes
│   │   ├── components/    # Reusable React components
│   │   ├── lib/           # Utility functions and configurations
│   │   ├── middleware/    # Custom middleware
│   │   ├── utils/         # Helper functions
│   │   ├── hooks/         # Custom React hooks
│   │   ├── contexts/      # React contexts
│   │   ├── constants/     # App constants
│   │   ├── styles/        # Global styles and CSS modules
│   │   ├── assets/        # Static assets
│   │   ├── public/        # Public static files
│   │   ├── package.json   # Frontend dependencies
│   │   ├── jsconfig.json  # Path mappings
│   │   └── next.config.js # Next.js configuration
│   └── api/               # Backend API (if separate)
├── docs/                  # Documentation (you are here!)
├── augment/              # AI agent configurations and prompts
├── cursor-prompts/       # Historical development prompts
└── package.json          # Root package.json for monorepo
```

## 🚀 Development Workflow

### Starting Development
```bash
# Navigate to frontend
cd apps/frontend

# Start development server
npm run dev

# Server starts at http://localhost:3000
# Hot reload is enabled - changes appear instantly!
```

### Development vs Production

#### Development Mode (`npm run dev`)
- **Hot Reload**: Changes appear instantly
- **Source Maps**: Easy debugging
- **Detailed Errors**: Full error stack traces
- **Development Database**: Use test data
- **Mock Services**: Simulated external APIs

#### Production Mode (`npm run build && npm start`)
- **Optimized Build**: Minified and compressed
- **Production Database**: Real data
- **Error Handling**: User-friendly error pages
- **Performance**: Optimized for speed

## 🔧 Development Tools Setup

### VS Code Configuration

#### Recommended Extensions
```json
{
  "recommendations": [
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-typescript-next",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    "ms-vscode.vscode-json"
  ]
}
```

#### VS Code Settings (`.vscode/settings.json`)
```json
{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "typescript.preferences.importModuleSpecifier": "relative",
  "emmet.includeLanguages": {
    "javascript": "javascriptreact"
  }
}
```

### Cursor IDE Setup
1. **AI-Powered Development**: Cursor provides intelligent code suggestions
2. **Project Context**: Automatically understands your codebase
3. **Code Generation**: Can generate components and functions
4. **Debugging**: Built-in debugging tools

## 📁 Key Directories Explained

### `/apps/frontend/app/`
- **Pages**: Each folder represents a route
- **API Routes**: `api/` folder contains backend endpoints
- **Layout**: `layout.js` defines page structure
- **Loading**: `loading.js` shows while pages load

### `/apps/frontend/components/`
- **Reusable Components**: Shared across multiple pages
- **CSS Modules**: Each component has its own `.css` file
- **Component Structure**:
  ```
  ComponentName/
  ├── ComponentName.jsx    # React component
  ├── ComponentName.css    # Styles
  └── index.js            # Export file
  ```

### `/apps/frontend/lib/`
- **Database**: MongoDB connection and models
- **Utils**: Helper functions
- **Auth**: Authentication logic
- **AWS**: S3 upload configurations

### `/apps/frontend/middleware/`
- **Visitor Tracking**: IP-based analytics middleware
- **Custom Logic**: Reusable middleware functions

### `/apps/frontend/utils/`
- **VIN Decoder**: Vehicle identification utilities
- **Helper Functions**: Common utility functions

### `/apps/frontend/hooks/`
- **Custom Hooks**: Reusable React hooks
- **State Management**: Component state logic

### `/apps/frontend/contexts/`
- **React Contexts**: Global state management
- **Sidebar Context**: Navigation state

## 🗄️ Database Development

### MongoDB Connection
```javascript
// lib/dbConnect.js
import mongoose from 'mongoose';

const connectToDatabase = async () => {
  if (mongoose.connections[0].readyState) {
    return;
  }
  
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');
  } catch (error) {
    console.error('MongoDB connection error:', error);
  }
};
```

### Development Database Tips
1. **Use MongoDB Compass**: Visual interface for your database
2. **Seed Data**: Create test data for development
3. **Database Naming**: Use different databases for dev/prod
4. **Backup**: Regular backups of development data

## 🔐 Environment Variables

### Development Environment (`.env.local`)
```bash
# Database
MONGODB_URI=mongodb://localhost:27017/fazenauto-dev

# AWS S3
AWS_ACCESS_KEY_ID=your-dev-access-key
AWS_SECRET_ACCESS_KEY=your-dev-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=fazenauto-dev-images

# OAuth
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
APPLE_CLIENT_ID=your-apple-client-id
APPLE_CLIENT_SECRET=your-apple-client-secret

# App Settings
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-dev-secret
```

### Environment-Specific Settings
- **Development**: Use test credentials and local services
- **Staging**: Mirror production but with test data
- **Production**: Real credentials and live services

## 🧪 Testing in Development

### Manual Testing Checklist
- [ ] Vehicle upload works
- [ ] Image upload to S3 works
- [ ] User authentication works
- [ ] Admin dashboard accessible
- [ ] Mobile responsive design
- [ ] PDF generation works

### Browser Testing
- **Chrome DevTools**: Primary development browser
- **Mobile View**: Test responsive design
- **Network Tab**: Monitor API calls
- **Console**: Check for JavaScript errors

## 🚨 Common Development Issues

### Port Conflicts
```bash
# If port 3000 is busy
npx kill-port 3000

# Or use different port
npm run dev -- -p 3001
```

### Database Connection Issues
```bash
# Check MongoDB is running
mongosh

# Test connection string
node -e "console.log(process.env.MONGODB_URI)"
```

### AWS S3 Upload Issues
```bash
# Test AWS credentials
aws s3 ls s3://your-bucket-name

# Check bucket permissions in AWS Console
```

## 🔄 Development Best Practices

### Code Organization
1. **Component Structure**: One component per file
2. **CSS Modules**: Scoped styles for each component
3. **API Routes**: Organized by feature
4. **Error Handling**: Consistent error responses

### Git Workflow
```bash
# Create feature branch
git checkout -b feature/new-feature

# Make changes and commit
git add .
git commit -m "Add new feature"

# Push and create PR
git push origin feature/new-feature
```

### Performance Tips
1. **Image Optimization**: Use Next.js Image component
2. **Code Splitting**: Lazy load components
3. **API Optimization**: Efficient database queries
4. **Caching**: Use appropriate caching strategies

## 🎯 Development Goals

### Daily Development Tasks
- [ ] Check for console errors
- [ ] Test new features thoroughly
- [ ] Update documentation
- [ ] Review code for best practices
- [ ] Test mobile responsiveness

### Weekly Development Tasks
- [ ] Update dependencies
- [ ] Review and merge PRs
- [ ] Backup development database
- [ ] Performance testing
- [ ] Security review

## 🆘 Getting Help

### Debugging Steps
1. **Check Console**: Look for JavaScript errors
2. **Network Tab**: Monitor API requests
3. **Database**: Verify data is correct
4. **Environment**: Check all variables are set

### Resources
- **Next.js Docs**: https://nextjs.org/docs
- **React Docs**: https://react.dev/
- **MongoDB Docs**: https://docs.mongodb.com/
- **AWS S3 Docs**: https://docs.aws.amazon.com/s3/

---

*Happy coding! Remember: every expert was once a beginner. 🚀*
