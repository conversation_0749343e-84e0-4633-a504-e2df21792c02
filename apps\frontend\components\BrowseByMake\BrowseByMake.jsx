'use client';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useLanguage } from '../../contexts/LanguageContext';
import styles from './BrowseByMake.module.css';

// Car brand logo configuration - only logos that actually exist in public/car-logos/
const brandConfig = {
  'Toyota': { logo: '/car-logos/toyota-logo-2005.webp', fallback: '🚗' },
  'Ford': { logo: '/car-logos/ford-logo.webp', fallback: '🚐' },
  'Chevrolet': { logo: '/car-logos/chevrolet-logo.webp', fallback: '🚕' },
  'Nissan': { logo: '/car-logos/nissan-logo-2001-2000x1750-show.webp', fallback: '🚗' },
  'Hyundai': { logo: '/car-logos/hyundai-logo.webp', fallback: '🚙' },
  'Kia': { logo: '/car-logos/kia-logo.webp', fallback: '🚐' },
  'Mazda': { logo: '/car-logos/mazda-logo.webp', fallback: '🚕' },
  'Subaru': { logo: '/car-logos/subaru-logo.webp', fallback: '🚗' },
  'Volkswagen': { logo: '/car-logos/Volkswagen-logo-2015-1920x1080.webp', fallback: '🚙' },
  'BMW': { logo: '/car-logos/bmw-logo-1997.webp', fallback: '🚐' },
  'Mercedes-Benz': { logo: '/car-logos/mercedes-benz-logo.webp', fallback: '🚕' },
  'Audi': { logo: '/car-logos/audi-logo.webp', fallback: '🚗' },
  'Lexus': { logo: '/car-logos/lexus-logo.webp', fallback: '🚙' },
  'Acura': { logo: '/car-logos/acura-logo.webp', fallback: '🚐' },
  'Jeep': { logo: '/car-logos/jeep-logo-1993-640.webp', fallback: '🚗' },
  'Dodge': { logo: '/car-logos/dodge-logo.webp', fallback: '🚐' },
  'Mini': { logo: '/car-logos/mini-logo.webp', fallback: '🚗' },
  'Suzuki': { logo: '/car-logos/suzuki-logo.webp', fallback: '🚙' },
  'Volvo': { logo: '/car-logos/volvo-logo.webp', fallback: '🚐' }
};

// Fallback for brands without logos
const getDefaultFallback = (make) => {
  const fallbacks = ['🚗', '🚙', '🚐', '🚕', '🚖'];
  const index = make.length % fallbacks.length;
  return fallbacks[index];
};

export default function BrowseByMake() {
  const [makes, setMakes] = useState([]);
  const [loading, setLoading] = useState(true);
  const router = useRouter();
  const { t } = useLanguage();

  useEffect(() => {
    const fetchMakes = async () => {
      try {
        const response = await fetch('/api/vehicles?makes=true');
        const data = await response.json();
        if (data.success) {
          // Show all makes, prioritizing those with logos, limit to 7 for display
          const makesWithLogos = data.data.filter(item => brandConfig[item.make]);
          const makesWithoutLogos = data.data.filter(item => !brandConfig[item.make]);
          const availableMakes = [...makesWithLogos, ...makesWithoutLogos].slice(0, 7);
          setMakes(availableMakes);
        }
      } catch (error) {
        console.error('Error fetching makes:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchMakes();
  }, []);

  const handleMakeClick = (make) => {
    router.push(`/inventory/used-cars?make=${encodeURIComponent(make)}`);
  };

  if (loading) {
    return (
      <section className={styles.browseSection}>
        <div className={styles.container}>
          <div className={styles.sectionHeader}>
            <h2 className={styles.sectionTitle}>
              {t('home.browse_by_make.title')} <span className={styles.makeHighlight}>{t('home.browse_by_make.make')}</span>
            </h2>
          </div>
          <div className={styles.loadingGrid}>
            {[...Array(7)].map((_, index) => (
              <div key={index} className={styles.loadingCard}>
                <div className={styles.loadingSkeleton}></div>
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className={styles.browseSection}>
      <div className={styles.container}>
        <div className={styles.sectionHeader}>
          <h2 className={styles.sectionTitle}>
            {t('home.browse_by_make.title')} <span className={styles.makeHighlight}>{t('home.browse_by_make.make')}</span>
          </h2>
        </div>
        <div className={styles.brandsGrid}>
          {makes.map((item) => {
            const config = brandConfig[item.make];
            const hasLogo = config && config.logo;
            const fallbackEmoji = config?.fallback || getDefaultFallback(item.make);

            return (
              <div
                key={item.make}
                className={styles.brandCard}
              >
                <div
                  className={styles.brandLogo}
                  onClick={() => handleMakeClick(item.make)}
                  title={t('home.browse_by_make.view_all', { make: item.make })}
                >
                  {hasLogo ? (
                    <>
                      <img
                        src={config.logo}
                        alt={`${item.make} logo`}
                        width={60}
                        height={60}
                        className={styles.logoImage}
                        onLoad={(e) => {
                          // Ensure image is visible when loaded successfully
                          e.target.style.display = 'block';
                          if (e.target.nextSibling) {
                            e.target.nextSibling.style.display = 'none';
                          }
                        }}
                        onError={(e) => {
                          // Fallback to emoji if image fails to load
                          console.warn(`Failed to load logo for ${item.make}:`, config.logo);
                          console.warn('Full URL attempted:', e.target.src);
                          e.target.style.display = 'none';
                          if (e.target.nextSibling) {
                            e.target.nextSibling.style.display = 'flex';
                          }
                        }}
                      />
                      <div
                        className={styles.brandEmoji}
                        style={{ display: 'none' }}
                      >
                        {fallbackEmoji}
                      </div>
                    </>
                  ) : (
                    <div className={styles.brandEmoji}>
                      {fallbackEmoji}
                    </div>
                  )}
                </div>
                <div className={styles.brandName}>
                  {item.make}
                </div>
                <div className={styles.vehicleCount}>
                  {item.count} {item.count === 1 ? 'vehicle' : 'vehicles'}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
}
