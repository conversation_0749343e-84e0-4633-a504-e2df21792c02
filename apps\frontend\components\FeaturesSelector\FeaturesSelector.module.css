.container {
  background: transparent;
  border-radius: 12px;
  padding: 1.5rem;
  margin: 1.5rem 0;
  border: none;
}

.title {
  color: #333;
  margin: 0 0 0.5rem 0;
  font-size: 1.3rem;
  font-weight: 600;
  transition: color 0.3s ease;
}

[data-theme="dark"] .title {
  color: white;
}

.subtitle {
  color: rgba(51, 51, 51, 0.8);
  margin: 0 0 1.5rem 0;
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

[data-theme="dark"] .subtitle {
  color: rgba(255, 255, 255, 0.8);
}

.loading,
.error {
  text-align: center;
  padding: 2rem;
  color: #333;
  font-size: 1rem;
  transition: color 0.3s ease;
}

[data-theme="dark"] .loading,
[data-theme="dark"] .error {
  color: white;
}

.error {
  color: #ff6b6b;
  background: rgba(255, 107, 107, 0.1);
  border-radius: 8px;
}

.categoriesContainer {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
}

.categoryCard {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

[data-theme="dark"] .categoryCard {
  background: linear-gradient(135deg, #2d2d2d 0%, #1a1a1a 100%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.categoryCard:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

[data-theme="dark"] .categoryCard:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
}

.categoryHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .categoryHeader {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.categoryHeader:hover {
  background: rgba(0, 0, 0, 0.05);
}

[data-theme="dark"] .categoryHeader:hover {
  background: rgba(255, 255, 255, 0.05);
}

.categoryInfo {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.categoryIcon {
  font-size: 2rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.categoryText {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.categoryTitle {
  color: #333;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  transition: color 0.3s ease;
}

[data-theme="dark"] .categoryTitle {
  color: white;
}

.categoryDescription {
  color: #666;
  transition: color 0.3s ease;
}

[data-theme="dark"] .categoryDescription {
  color: #cccccc;
  font-size: 0.9rem;
  margin: 0;
  opacity: 0.8;
}

.categoryToggle {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
}

.toggleIcon {
  color: #4CAF50;
  font-size: 1rem;
  transition: transform 0.3s ease;
}

.toggleIcon.expanded {
  transform: rotate(180deg);
}

.featureCount {
  color: #888;
  font-size: 0.8rem;
  transition: color 0.3s ease;
}

[data-theme="dark"] .featureCount {
  color: #888;
}

.categoryContent {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.categoryContent.expanded {
  max-height: 2000px;
  padding: 1.5rem;
  padding-top: 0;
}

.featuresGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1rem;
}

.featureItem {
  background: rgba(0, 0, 0, 0.02);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  padding: 1.25rem;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

[data-theme="dark"] .featureItem {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.featureItem:hover {
  background: rgba(0, 0, 0, 0.05);
  transform: translateY(-2px);
  border-color: rgba(59, 130, 246, 0.3);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .featureItem:hover {
  background: rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.featureHeader {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.25rem;
}

.featureIcon {
  width: 1.25rem;
  height: 1.25rem;
  color: #6b7280;
  transition: color 0.3s ease;
}

[data-theme="dark"] .featureIcon {
  color: #9ca3af;
}

.featureText {
  color: #333;
  font-size: 0.95rem;
  font-weight: 600;
  margin: 0;
  flex: 1;
  transition: color 0.3s ease;
  line-height: 1.4;
}

[data-theme="dark"] .featureText {
  color: white;
}

.chipGroup {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  flex-wrap: wrap;
}

.chip {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.5rem 0.75rem;
  border-radius: 9999px;
  border: 1px solid transparent;
  background: rgba(0, 0, 0, 0.05);
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  outline: none;
  position: relative;
}

[data-theme="dark"] .chip {
  background: rgba(255, 255, 255, 0.05);
  color: #9ca3af;
}

.chip:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .chip:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.chipIcon {
  width: 1rem;
  height: 1rem;
  transition: all 0.2s ease;
}

.chipText {
  font-size: 0.8rem;
  white-space: nowrap;
}

/* Chip variants */
.chipIncluded {
  border-color: rgba(34, 197, 94, 0.2);
}

.chipIncluded:hover {
  background: rgba(34, 197, 94, 0.1);
  border-color: rgba(34, 197, 94, 0.3);
}

.chipIncluded.chipSelected {
  background: #22c55e;
  color: white;
  border-color: #22c55e;
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.2);
}

.chipNotAvailable {
  border-color: rgba(239, 68, 68, 0.2);
}

.chipNotAvailable:hover {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
}

.chipNotAvailable.chipSelected {
  background: #ef4444;
  color: white;
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2);
}

.chipUnknown {
  border-color: rgba(107, 114, 128, 0.2);
}

.chipUnknown:hover {
  background: rgba(107, 114, 128, 0.1);
  border-color: rgba(107, 114, 128, 0.3);
}

.chipUnknown.chipSelected {
  background: #6b7280;
  color: white;
  border-color: #6b7280;
  box-shadow: 0 0 0 3px rgba(107, 114, 128, 0.2);
}

.noFeatures {
  color: rgba(51, 51, 51, 0.6);
  text-align: center;
  padding: 1rem;
  font-style: italic;
  grid-column: 1 / -1;
  transition: color 0.3s ease;
}

[data-theme="dark"] .noFeatures {
  color: rgba(255, 255, 255, 0.6);
}

.summary {
  background: rgba(0, 0, 0, 0.02);
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

[data-theme="dark"] .summary {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.summary h4 {
  color: #333;
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  transition: color 0.3s ease;
}

[data-theme="dark"] .summary h4 {
  color: white;
}

.selectedFeatures {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.summaryCategory {
  color: rgba(51, 51, 51, 0.8);
  font-size: 0.9rem;
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

[data-theme="dark"] .summaryCategory {
  color: rgba(255, 255, 255, 0.8);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.summaryCategory:last-child {
  border-bottom: none;
}

.summaryCategory strong {
  color: #333;
  margin-right: 0.5rem;
  display: block;
  margin-bottom: 0.25rem;
  transition: color 0.3s ease;
}

[data-theme="dark"] .summaryCategory strong {
  color: white;
}

.summaryGroup {
  margin: 0.25rem 0;
  padding-left: 1rem;
}

.summaryLabel {
  font-weight: 600;
  margin-right: 0.5rem;
}

.noSelection {
  color: rgba(255, 255, 255, 0.6);
  font-style: italic;
  text-align: center;
  padding: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
    margin: 1rem 0;
  }

  .categoryCard {
    margin: 0 -0.5rem;
  }

  .categoryHeader {
    padding: 1rem;
  }

  .categoryIcon {
    font-size: 1.5rem;
  }

  .categoryTitle {
    font-size: 1.1rem;
  }

  .categoryDescription {
    font-size: 0.8rem;
  }

  .categoryContent.expanded {
    padding: 1rem;
  }

  .featuresGrid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .featureItem {
    padding: 0.75rem;
  }

  .featureIcon {
    font-size: 1.25rem;
  }

  .featureText {
    font-size: 0.9rem;
  }

  .radioLabel {
    width: 35px;
    height: 35px;
  }

  .radioText {
    font-size: 1rem;
  }

  .title {
    font-size: 1.2rem;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0.75rem;
  }

  .categoryHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .categoryToggle {
    align-self: flex-end;
    flex-direction: row;
    align-items: center;
    gap: 0.5rem;
  }

  .summary {
    padding: 0.75rem;
  }

  .selectedFeatures {
    gap: 0.25rem;
  }

  .summaryCategory {
    font-size: 0.85rem;
    line-height: 1.4;
  }
}
