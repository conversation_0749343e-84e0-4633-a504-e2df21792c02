'use client';

import { useState, useEffect } from 'react';
import styles from './VisitorTracker.module.css';

export default function VisitorTracker({ isOpen, onClose }) {
  const [visitors, setVisitors] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [stats, setStats] = useState({
    totalVisitors: 0,
    uniqueVisitors: 0,
    todayVisitors: 0,
    thisWeekVisitors: 0
  });
  const [filters, setFilters] = useState({
    timeRange: '7d', // 1d, 7d, 30d, all
    sortBy: 'lastVisit', // lastVisit, visitCount, firstVisit
    sortOrder: 'desc'
  });

  useEffect(() => {
    if (isOpen) {
      fetchVisitors();
      fetchStats();
    }
  }, [isOpen, filters]);

  const fetchVisitors = async () => {
    setLoading(true);
    setError('');
    
    try {
      const queryParams = new URLSearchParams({
        timeRange: filters.timeRange,
        sortBy: filters.sortBy,
        sortOrder: filters.sortOrder
      });

      const response = await fetch(`/api/visitors?${queryParams}`);
      const data = await response.json();

      if (data.success) {
        // The API now returns summary data, not individual visitors
        // For now, we'll show an empty array and display stats instead
        setVisitors([]);
        if (data.data.summary) {
          setStats({
            totalVisitors: data.data.summary.totalVisitors || 0,
            uniqueVisitors: data.data.summary.recentVisitors || 0,
            todayVisitors: data.data.summary.newVisitors || 0,
            thisWeekVisitors: data.data.summary.totalVisits || 0
          });
        }
      } else {
        setError(data.error || 'Failed to fetch visitors');
      }
    } catch (err) {
      setError('Failed to fetch visitors');
      console.error('Error fetching visitors:', err);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await fetch('/api/visitors?statsOnly=true');
      const data = await response.json();

      if (data.success) {
        setStats({
          totalVisitors: data.data.totalVisitors || 0,
          uniqueVisitors: data.data.recentVisitors || 0,
          todayVisitors: data.data.newVisitors || 0,
          thisWeekVisitors: data.data.totalVisits || 0
        });
      }
    } catch (err) {
      console.error('Error fetching visitor stats:', err);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString();
  };

  const getLocationString = (visitor) => {
    const parts = [];
    if (visitor.city) parts.push(visitor.city);
    if (visitor.region) parts.push(visitor.region);
    if (visitor.country) parts.push(visitor.country);
    return parts.join(', ') || 'Unknown';
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  if (!isOpen) return null;

  return (
    <div className={styles.overlay}>
      <div className={styles.modal}>
        <div className={styles.header}>
          <h2>Site Visitors</h2>
          <button onClick={onClose} className={styles.closeBtn}>
            ×
          </button>
        </div>

        {/* Stats Section */}
        <div className={styles.statsSection}>
          <div className={styles.statCard}>
            <div className={styles.statNumber}>{stats.totalVisitors}</div>
            <div className={styles.statLabel}>Total Visits</div>
          </div>
          <div className={styles.statCard}>
            <div className={styles.statNumber}>{stats.uniqueVisitors}</div>
            <div className={styles.statLabel}>Unique Visitors</div>
          </div>
          <div className={styles.statCard}>
            <div className={styles.statNumber}>{stats.todayVisitors}</div>
            <div className={styles.statLabel}>Today</div>
          </div>
          <div className={styles.statCard}>
            <div className={styles.statNumber}>{stats.thisWeekVisitors}</div>
            <div className={styles.statLabel}>This Week</div>
          </div>
        </div>

        {/* Filters */}
        <div className={styles.filtersSection}>
          <div className={styles.filterGroup}>
            <label>Time Range:</label>
            <select
              value={filters.timeRange}
              onChange={(e) => handleFilterChange('timeRange', e.target.value)}
              className="formInput"
            >
              <option value="1d">Last 24 Hours</option>
              <option value="7d">Last 7 Days</option>
              <option value="30d">Last 30 Days</option>
              <option value="all">All Time</option>
            </select>
          </div>
          
          <div className={styles.filterGroup}>
            <label>Sort By:</label>
            <select
              value={filters.sortBy}
              onChange={(e) => handleFilterChange('sortBy', e.target.value)}
              className="formInput"
            >
              <option value="lastVisit">Last Visit</option>
              <option value="visitCount">Visit Count</option>
              <option value="firstVisit">First Visit</option>
            </select>
          </div>

          <button onClick={fetchVisitors} className={styles.refreshBtn}>
            🔄 Refresh
          </button>
        </div>

        {/* Visitors List */}
        <div className={styles.content}>
          {loading ? (
            <div className={styles.loading}>
              <div className={styles.spinner}></div>
              <p>Loading visitors...</p>
            </div>
          ) : error ? (
            <div className={styles.error}>
              <p>Error: {error}</p>
              <button onClick={fetchVisitors} className={styles.retryBtn}>
                Retry
              </button>
            </div>
          ) : (
            <div className={styles.visitorsList}>
              {visitors.length === 0 ? (
                <div className={styles.noData}>
                  <p>No visitors found for the selected time range.</p>
                </div>
              ) : (
                <div className={styles.tableWrapper}>
                  <table className={styles.table}>
                    <thead>
                      <tr>
                        <th>IP Address</th>
                        <th>Location</th>
                        <th>First Visit</th>
                        <th>Last Visit</th>
                        <th>Visit Count</th>
                        <th>User Agent</th>
                      </tr>
                    </thead>
                    <tbody>
                      {visitors && visitors.length > 0 ? (
                        visitors.map((visitor, index) => (
                          <tr key={visitor.ip || index}>
                            <td className={styles.ipAddress}>{visitor.ip}</td>
                            <td>{getLocationString(visitor)}</td>
                            <td>{formatDate(visitor.firstVisit)}</td>
                            <td>{formatDate(visitor.lastVisit)}</td>
                            <td className={styles.visitCount}>{visitor.visitCount}</td>
                            <td className={styles.userAgent} title={visitor.userAgent}>
                              {visitor.userAgent ? visitor.userAgent.substring(0, 50) + '...' : 'Unknown'}
                            </td>
                          </tr>
                        ))
                      ) : (
                        <tr>
                          <td colSpan="6" className={styles.noData}>
                            Individual visitor data not available. Showing summary statistics above.
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
