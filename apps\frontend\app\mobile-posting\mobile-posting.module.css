.mobilePostingPage {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  display: flex;
  flex-direction: column;
}

/* Loading */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #dc2626;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Header */
.header {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.headerContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  max-width: 480px;
  margin: 0 auto;
}

.backBtn {
  background: none;
  border: none;
  color: #dc2626;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background 0.2s ease;
}

.backBtn:hover {
  background: rgba(220, 38, 38, 0.1);
}

.header h1 {
  margin: 0;
  color: #333;
  font-size: 1.3rem;
  font-weight: 700;
}

.headerActions {
  display: flex;
  gap: 8px;
}

.installBtn {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s ease;
}

.installBtn:hover {
  background: #2563eb;
}

/* Install Banner */
.installBanner {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  padding: 16px 20px;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.bannerContent {
  display: flex;
  align-items: center;
  gap: 16px;
  max-width: 480px;
  margin: 0 auto;
}

.bannerIcon {
  font-size: 2rem;
  flex-shrink: 0;
}

.bannerText {
  flex: 1;
}

.bannerText h4 {
  margin: 0 0 4px 0;
  font-size: 1rem;
  font-weight: 600;
}

.bannerText p {
  margin: 0;
  font-size: 0.85rem;
  opacity: 0.9;
}

.bannerInstallBtn {
  background: white;
  color: #3b82f6;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.bannerInstallBtn:hover {
  background: #f8fafc;
  transform: translateY(-1px);
}

/* Content */
.content {
  flex: 1;
  padding: 0;
}

/* Footer */
.footer {
  background: white;
  border-top: 1px solid #e2e8f0;
  margin-top: auto;
}

.footerContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  max-width: 480px;
  margin: 0 auto;
}

.footerContent p {
  margin: 0;
  color: #666;
  font-size: 0.85rem;
  text-align: center;
}

.footerLinks {
  display: flex;
  gap: 16px;
}

.footerLinks button {
  background: none;
  border: 1px solid #e2e8f0;
  color: #666;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.footerLinks button:hover {
  border-color: #dc2626;
  color: #dc2626;
  background: rgba(220, 38, 38, 0.05);
}

/* PWA Specific Styles */
@media (display-mode: standalone) {
  .header {
    padding-top: env(safe-area-inset-top);
  }
  
  .footer {
    padding-bottom: env(safe-area-inset-bottom);
  }
  
  .installBanner {
    display: none;
  }
}

/* Responsive Design */
@media (max-width: 480px) {
  .headerContent {
    padding: 12px 16px;
  }
  
  .header h1 {
    font-size: 1.2rem;
  }
  
  .bannerContent {
    padding: 0 16px;
  }
  
  .footerContent {
    padding: 12px 16px;
  }
}

@media (max-width: 360px) {
  .headerContent {
    padding: 10px 12px;
  }
  
  .header h1 {
    font-size: 1.1rem;
  }
  
  .bannerContent {
    gap: 12px;
    padding: 0 12px;
  }
  
  .bannerIcon {
    font-size: 1.5rem;
  }
  
  .bannerText h4 {
    font-size: 0.95rem;
  }
  
  .bannerText p {
    font-size: 0.8rem;
  }
  
  .footerLinks {
    flex-direction: column;
    width: 100%;
  }
  
  .footerLinks button {
    width: 100%;
    text-align: center;
  }
}

/* Landscape orientation adjustments */
@media (orientation: landscape) and (max-height: 500px) {
  .installBanner {
    padding: 8px 20px;
  }
  
  .bannerText h4 {
    font-size: 0.9rem;
  }
  
  .bannerText p {
    font-size: 0.8rem;
  }
  
  .footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
  }
  
  .content {
    padding-bottom: 80px;
  }
}

/* Dark mode support */
[data-theme="dark"] .mobilePostingPage {
  background: linear-gradient(135deg, var(--bg-secondary), var(--bg-primary));
}

[data-theme="dark"] .header {
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-primary);
}

[data-theme="dark"] .header h1 {
  color: var(--text-primary);
}

[data-theme="dark"] .backBtn {
  color: var(--brand-red);
}

[data-theme="dark"] .backBtn:hover {
  background: rgba(239, 68, 68, 0.1);
}

[data-theme="dark"] .footer {
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-primary);
}

[data-theme="dark"] .footerContent p {
  color: var(--text-secondary);
}

[data-theme="dark"] .footerLinks button {
  border-color: var(--border-primary);
  color: var(--text-secondary);
  background: transparent;
}

[data-theme="dark"] .footerLinks button:hover {
  border-color: var(--brand-red);
  color: var(--brand-red);
  background: rgba(239, 68, 68, 0.1);
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .header {
    border-bottom: 2px solid #000;
  }
  
  .footer {
    border-top: 2px solid #000;
  }
  
  .installBanner {
    background: #000;
    color: #fff;
  }
  
  .bannerInstallBtn {
    background: #fff;
    color: #000;
    border: 2px solid #000;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .spinner {
    animation: none;
  }
  
  .installBanner {
    animation: none;
  }
}
