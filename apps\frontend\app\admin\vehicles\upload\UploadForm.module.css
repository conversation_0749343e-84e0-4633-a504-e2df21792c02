/* Vehicle Upload Form Styling */

.container {
  min-height: 100vh;
  background: #ffffff;
  padding: 0;
  color: var(--text-primary, #333);
  transition: background 0.3s ease;
}

/* Dark mode: Apply consistent gradient background */
[data-theme="dark"] .container {
  background: var(--bg-primary);
}

.formWrapper {
  background: transparent;
  padding: 2rem 1rem;
  width: 100%;
  max-width: none;
  margin: 0;
}

/* Responsive padding adjustments for larger screens */
@media (min-width: 769px) {
  .formWrapper {
    padding: 2rem 3rem;
  }
}

@media (min-width: 1200px) {
  .formWrapper {
    padding: 2rem 4rem;
  }
}

.title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary, #2d3748);
  text-align: center;
  margin-bottom: 2rem;
  border-bottom: 2px solid var(--border-color, #e2e8f0);
  padding-bottom: 1rem;
  transition: color 0.3s ease, border-color 0.3s ease;
}

[data-theme="dark"] .title {
  color: #ffffff;
  border-bottom-color: rgba(255, 255, 255, 0.2);
}

.form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.inputGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.label {
  font-weight: 600;
  color: var(--text-primary, #4a5568);
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
}

/* Input styling removed - using global .formInput class */



/* Select styling removed - using global .formInput class */

.fileInput {
  padding: 0.875rem 1rem;
  border: 2px dashed var(--border-color, #cbd5e0);
  border-radius: 8px;
  background: var(--bg-tertiary, #f7fafc);
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  color: var(--text-primary, #2d3748);
}

.fileInput:hover {
  border-color: #4299e1;
  background: var(--bg-hover, #ebf8ff);
}

.fileInput:focus {
  outline: none;
  border-color: #4299e1;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}

.helpText {
  color: var(--text-secondary, #718096);
  font-size: 0.875rem;
  margin-top: 0.25rem;
  font-style: italic;
}

.submitButton {
  background: #4299e1;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 1rem;
}

.submitButton:hover {
  background: #3182ce;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(66, 153, 225, 0.3);
}

.submitButton:active {
  transform: translateY(0);
}

.message {
  padding: 1rem;
  border-radius: 8px;
  margin-top: 1rem;
  font-weight: 500;
  text-align: center;
}

.messageSuccess {
  background: #c6f6d5;
  color: #22543d;
  border: 1px solid #9ae6b4;
}

.messageError {
  background: #fed7d7;
  color: #742a2a;
  border: 1px solid #fc8181;
}

.twoColumnGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.fullWidth {
  grid-column: 1 / -1;
}

/* Loading state for submit button */
.submitButton:disabled {
  background: #a0aec0;
  cursor: not-allowed;
  transform: none;
}

.submitButton:disabled:hover {
  background: #a0aec0;
  transform: none;
  box-shadow: none;
}

/* VIN Decoder Styles */
.vinInputGroup {
  display: flex;
  gap: 0.75rem;
  align-items: flex-end;
}

.vinInputGroup .input {
  flex: 1;
}

.scanButton {
  background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
  color: white;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  min-width: 150px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  line-height: 1;
}

.scanButton span {
  display: flex;
  align-items: center;
}

.scanButton:hover {
  background: linear-gradient(135deg, #c53030 0%, #9b2c2c 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(229, 62, 62, 0.3);
}

/* Camera icon flash animation */
.scanButton span:first-child {
  transition: filter 0.2s ease;
  display: flex;
  align-items: center;
  transform: translateY(-1px);
  font-size: 1.1em;
  line-height: 1;
}

.scanButton:hover span:first-child {
  animation: flashIcon 2s ease-in-out;
}

@keyframes flashIcon {
  0%   { filter: brightness(1); }
  50%  { filter: brightness(2); }
  100% { filter: brightness(1); }
}

.decodeButton {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.25rem;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  min-width: 130px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.decodeButton:hover:not(:disabled) {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.decodeButton:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.vinHelper {
  font-size: 0.85rem;
  color: #6b7280;
  font-weight: normal;
}

.vinDataPreview {
  margin-top: 1rem;
  padding: 1rem;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
}

.vinDataPreview h4 {
  margin: 0 0 0.75rem 0;
  color: #0c4a6e;
  font-size: 1rem;
}

.vinDataGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  font-size: 0.875rem;
}

.vinDataSection {
  background: white;
  padding: 1rem;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.vinDataSection h5 {
  margin: 0 0 0.75rem 0;
  color: #1f2937;
  font-size: 0.9rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 0.5rem;
}

.vinDataSection span {
  display: block;
  color: #374151;
  margin-bottom: 0.5rem;
  padding: 0.25rem 0;
}

.vinDataSection span:last-child {
  margin-bottom: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }
  
  .formWrapper {
    padding: 2rem 1.5rem;
  }
  
  .title {
    font-size: 1.75rem;
  }
  
  .twoColumnGrid {
    grid-template-columns: 1fr;
  }

  .vinInputGroup {
    flex-direction: column;
    gap: 0.5rem;
  }

  .vinInputGroup .input {
    width: 100%;
    margin: 0;
  }

  .scanButton,
  .decodeButton {
    width: 100%;
    min-width: auto;
    height: 48px;
  }

  .vinDataGrid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .formWrapper {
    padding: 1.5rem 1rem;
  }

  .title {
    font-size: 1.5rem;
  }

  .fileInput {
    padding: 0.75rem;
  }

  .submitButton {
    padding: 0.875rem 1.5rem;
    font-size: 1rem;
  }
}
