'use client';

import { useState, useEffect } from 'react';
import VINScanner from '../../components/VINScanner/VINScanner';
import { decodeVIN, validateVIN } from '../../utils/vinDecoder';
import { useAuth } from '../../lib/auth';
import styles from './VehicleLookup.module.css';
import marketStyles from '../../styles/shared-market.module.css';
import { useLanguage } from '../../contexts/LanguageContext';

export default function PublicVehicleLookupPage() {
  const { t } = useLanguage();
  const { canAccessMarketCheck, isDealer, user } = useAuth();
  const [showVINScanner, setShowVINScanner] = useState(false);
  const [vinInput, setVinInput] = useState('');
  const [vehicleData, setVehicleData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [vehicleHistory, setVehicleHistory] = useState(null);
  const [recallsData, setRecallsData] = useState(null);
  const [marketData, setMarketData] = useState(null);
  const [marketDataLoading, setMarketDataLoading] = useState(false);

  // Handle VIN input change
  const handleVinInputChange = (e) => {
    setVinInput(e.target.value.toUpperCase());
    setError('');
  };

  // Handle VIN detected from scanner
  const handleVINDetected = async (detectedVIN) => {
    setVinInput(detectedVIN);
    await lookupVehicleInfo(detectedVIN);
  };

  // Lookup vehicle information
  const lookupVehicleInfo = async (vin = vinInput) => {
    if (!vin) {
      setError('Please enter a VIN');
      return;
    }

    if (!validateVIN(vin)) {
      setError('Invalid VIN format. VIN must be 17 characters long and contain only letters and numbers (no I, O, or Q).');
      return;
    }

    setLoading(true);
    setError('');
    setVehicleData(null);
    setVehicleHistory(null);
    setRecallsData(null);
    setMarketData(null);

    try {
      console.log('🔍 Starting VIN lookup for:', vin);

      // Use the new vehicle-info API route that handles both NHTSA and MarketCheck data
      const response = await fetch('/api/vehicle-info', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ vin }),
      });

      if (!response.ok) {
        throw new Error(`API error: ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        console.log('✅ Vehicle info fetched successfully:', result);
        setVehicleData(result.vehicleInfo);
        setRecallsData(result.recalls);

        // Check for service warnings
        if (result.warning) {
          console.log('⚠️ Service warning:', result.warning);
          setError(`⚠️ ${result.warning}`);
        }

        // Debug auth status
        console.log('🔍 Auth Debug - User:', user);
        console.log('🔍 Auth Debug - canAccessMarketCheck:', canAccessMarketCheck);
        console.log('🔍 Auth Debug - isDealer:', isDealer);

        // Set MarketCheck data if available and user has access
        if (result.marketData && canAccessMarketCheck) {
          console.log('✅ MarketCheck data available for dealer');
          setMarketData(result.marketData);
        } else if (canAccessMarketCheck) {
          console.log('⚠️ MarketCheck data not available:', result.marketData);
        } else {
          console.log('👤 User is not dealer, MarketCheck data not shown');
        }

        // Try to get vehicle history (placeholder for now)
        await getVehicleHistory(vin);
      } else {
        // Handle specific error types
        if (result.serviceStatus === 'unavailable') {
          setError(`🚨 ${result.error}\n\n💡 The NHTSA vehicle database is temporarily down. This is a government service that experiences occasional outages. Please try again in a few minutes.`);

          // Show retry suggestion for service unavailable
          if (result.retryAfter) {
            setTimeout(() => {
              setError(prev => prev + '\n\n🔄 You can try again now.');
            }, result.retryAfter * 1000);
          }
        } else {
          setError(`${t('vehicle_lookup.vin_decoder_error')} ${result.error}`);
        }
      }
    } catch (err) {
      console.error('❌ VIN lookup failed:', err);

      // Handle network errors and 503 responses
      if (err.message.includes('503') || err.message.includes('Service Unavailable')) {
        setError('🚨 NHTSA vehicle database is temporarily unavailable\n\n💡 This is a government service that experiences occasional outages. Please try again in a few minutes.');
      } else {
        setError(`${t('vehicle_lookup.lookup_failed')} ${err.message}`);
      }
    } finally {
      setLoading(false);
    }
  };

  // Get vehicle history using our API
  const getVehicleHistory = async (vin) => {
    try {
      const response = await fetch('/api/vehicle-history', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ vin }),
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setVehicleHistory(result.data);
        } else {
          console.error('Vehicle history API error:', result.error);
          setVehicleHistory({
            hasHistory: false,
            error: result.error,
            message: 'Unable to retrieve vehicle history at this time.'
          });
        }
      } else {
        throw new Error('API request failed');
      }
    } catch (error) {
      console.error('Vehicle history lookup error:', error);
      setVehicleHistory({
        hasHistory: false,
        error: 'Network error',
        message: 'Unable to connect to vehicle history service.'
      });
    }
  };

  const handleManualLookup = () => {
    lookupVehicleInfo();
  };

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h1>{t('vehicle_lookup.title')}</h1>
        <p>{t('vehicle_lookup.subtitle')}</p>
      </div>

      <div className={styles.inputSection}>
        <div className={styles.vinInputGroup}>
          <input
            type="text"
            value={vinInput}
            onChange={handleVinInputChange}
            placeholder={t('vehicle_lookup.placeholder')}
            maxLength="17"
            className="formInput"
          />
          <button
            onClick={() => setShowVINScanner(true)}
            className={styles.scanButton}
          >
            <span>📷</span>
            <span>{t('vehicle_lookup.scan_vin')}</span>
          </button>
          <button
            onClick={handleManualLookup}
            disabled={loading || !vinInput || vinInput.length !== 17}
            className={styles.lookupButton}
          >
            {loading ? '🔄 ' + t('common.decoding') : '🔍 ' + t('vehicle_lookup.decode')}
          </button>
        </div>
        <div className={styles.characterCount}>
          {t('vehicle_lookup.characters_remaining')} {17 - vinInput.length}
        </div>
      </div>

      {error && (
        <div className={styles.error}>
          {error}
        </div>
      )}

      {vehicleData && (
        <div className={styles.resultsSection}>
          <div className={styles.vehicleInfo}>
            <h2>{t('vehicle_lookup.decoded_vehicle_info')} <span className={styles.vinDisplay}>({vinInput})</span></h2>
            <div className={styles.infoGrid}>
              <div className={styles.infoCard}>
                <h3>{t('vehicle_lookup.basic_info')}</h3>
                <div className={styles.infoItem}>
                  <span className={styles.label}>{t('vehicle.make')}</span>
                  <span className={styles.value}>{vehicleData.make || 'N/A'}</span>
                </div>
                <div className={styles.infoItem}>
                  <span className={styles.label}>{t('vehicle.model')}</span>
                  <span className={styles.value}>{vehicleData.model || 'N/A'}</span>
                </div>
                <div className={styles.infoItem}>
                  <span className={styles.label}>{t('vehicle.year')}</span>
                  <span className={styles.value}>{vehicleData.year || 'N/A'}</span>
                </div>
                <div className={styles.infoItem}>
                  <span className={styles.label}>{t('vehicle_lookup.trim')}</span>
                  <span className={styles.value}>{vehicleData.trim || 'N/A'}</span>
                </div>
              </div>

              <div className={styles.infoCard}>
                <h3>{t('vehicle_lookup.engine_performance')}</h3>
                <div className={styles.infoItem}>
                  <span className={styles.label}>{t('vehicle.engine')}</span>
                  <span className={styles.value}>{vehicleData.engine || 'N/A'}</span>
                </div>
                <div className={styles.infoItem}>
                  <span className={styles.label}>{t('vehicle.cylinders')}</span>
                  <span className={styles.value}>{vehicleData.cylinders || 'N/A'}</span>
                </div>
                <div className={styles.infoItem}>
                  <span className={styles.label}>{t('vehicle.displacement_cc')}</span>
                  <span className={styles.value}>{vehicleData.displacementCC || 'N/A'}</span>
                </div>
                <div className={styles.infoItem}>
                  <span className={styles.label}>{t('vehicle.displacement_ci')}</span>
                  <span className={styles.value}>{vehicleData.displacementCI || 'N/A'}</span>
                </div>
                <div className={styles.infoItem}>
                  <span className={styles.label}>{t('vehicle.horsepower_from')}</span>
                  <span className={styles.value}>{vehicleData.horsepowerFrom || 'N/A'}</span>
                </div>
                <div className={styles.infoItem}>
                  <span className={styles.label}>{t('vehicle.transmission')}</span>
                  <span className={styles.value}>{vehicleData.transmission || 'N/A'}</span>
                </div>
                <div className={styles.infoItem}>
                  <span className={styles.label}>{t('vehicle.drivetrain')}</span>
                  <span className={styles.value}>{vehicleData.drivetrain || 'N/A'}</span>
                </div>
                <div className={styles.infoItem}>
                  <span className={styles.label}>{t('vehicle.drive_type')}</span>
                  <span className={styles.value}>{vehicleData.driveType || 'N/A'}</span>
                </div>
              </div>

              <div className={styles.infoCard}>
                <h3>{t('vehicle_lookup.vehicle_details')}</h3>
                <div className={styles.infoItem}>
                  <span className={styles.label}>{t('vehicle.body_class')}</span>
                  <span className={styles.value}>{vehicleData.bodyClass || 'N/A'}</span>
                </div>
                <div className={styles.infoItem}>
                  <span className={styles.label}>{t('vehicle.fuel_type')}</span>
                  <span className={styles.value}>{vehicleData.fuelType || 'N/A'}</span>
                </div>
                <div className={styles.infoItem}>
                  <span className={styles.label}>{t('vehicle.doors')}</span>
                  <span className={styles.value}>{vehicleData.doors || 'N/A'}</span>
                </div>
                <div className={styles.infoItem}>
                  <span className={styles.label}>{t('vehicle_lookup.manufacturer')}</span>
                  <span className={styles.value}>{vehicleData.manufacturer || 'N/A'}</span>
                </div>
                <div className={styles.infoItem}>
                  <span className={styles.label}>{t('vehicle.plant_city')}</span>
                  <span className={styles.value}>{vehicleData.plantCity || 'N/A'}</span>
                </div>
                <div className={styles.infoItem}>
                  <span className={styles.label}>{t('vehicle.plant_country')}</span>
                  <span className={styles.value}>{vehicleData.plantCountry || 'N/A'}</span>
                </div>
              </div>
            </div>

            {/* Public users get basic action buttons */}
            <div className={styles.actionButtons}>
              <button
                className={styles.clearButton}
                onClick={() => {
                  setVinInput('');
                  setVehicleData(null);
                  setVehicleHistory(null);
                  setMarketData(null);
                  setError('');
                }}
              >
                {t('vehicle_lookup.clear_search')}
              </button>
            </div>
          </div>

          {vehicleHistory && (
            <div className={styles.historySection}>
              <h2>{t('vehicle_lookup.vehicle_history')}</h2>

              {/* Recalls Section (Free NHTSA Data) */}
              {vehicleHistory.recalls && (
                <div className={styles.recallsSection}>
                  <h3>{t('vehicle_lookup.safety_recalls')}</h3>
                  {vehicleHistory.recalls.hasRecalls ? (
                    <div className={styles.recallsData}>
                      <p className={styles.recallCount}>
                        ⚠️ {vehicleHistory.recalls.count} {t('vehicle_lookup.recall_count')}
                      </p>
                      <div className={styles.recallsList}>
                        {vehicleHistory.recalls.recalls.slice(0, 3).map((recall, index) => (
                          <div key={index} className={styles.recallItem}>
                            <h4>{recall.Component}</h4>
                            <p><strong>{t('vehicle_lookup.recall_date')}</strong> {recall.ReportReceivedDate}</p>
                            <p><strong>{t('vehicle_lookup.recall_summary')}</strong> {recall.Summary}</p>
                          </div>
                        ))}
                        {vehicleHistory.recalls.count > 3 && (
                          <p className={styles.moreRecalls}>
                            ... and {vehicleHistory.recalls.count - 3} {t('vehicle_lookup.more_recalls')}
                          </p>
                        )}
                      </div>
                    </div>
                  ) : (
                    <div className={styles.noRecalls}>
                      <p>✅ {t('vehicle_lookup.no_recalls')}</p>
                    </div>
                  )}
                </div>
              )}

              {/* Other History Information */}
              {vehicleHistory.hasHistory && (
                <div className={styles.historyContent}>
                  <p>{t('vehicle_lookup.additional_history')}</p>
                </div>
              )}
            </div>
          )}

          {/* MarketCheck Pricing Data - Dealer Only */}
          {canAccessMarketCheck && (marketData || marketDataLoading) && (
            <div className={styles.marketDataSection}>
              <h2>Market Pricing Data</h2>
              <p className={styles.dealerOnly}>🔒 Dealer-only information</p>

              {marketDataLoading ? (
                <div className={styles.loadingMarket}>
                  <p>Loading market data...</p>
                </div>
              ) : marketData ? (
                <div className={styles.marketDataGrid}>
                  {/* Market Value */}
                  {marketData.marketValue?.success && marketData.marketValue.data && (
                    <div className={marketStyles.marketCard}>
                      <h3>Market Value</h3>
                      {marketData.marketValue.data.marketValue ? (
                        <div>
                          <p className={marketStyles.marketPrice}>
                            ${marketData.marketValue.data.marketValue.toLocaleString()}
                          </p>
                          <p className={marketStyles.marketRange}>
                            Range: ${marketData.marketValue.data.priceRange?.min.toLocaleString()} -
                            ${marketData.marketValue.data.priceRange?.max.toLocaleString()}
                          </p>
                          <p className={marketStyles.marketInfo}>
                            {marketData.marketValue.data.message}
                          </p>
                        </div>
                      ) : (
                        <p>No market value data available</p>
                      )}
                    </div>
                  )}

                  {/* Comparable Listings */}
                  {marketData.comparableListings?.success && marketData.comparableListings.data?.listings?.length > 0 && (
                    <div className={marketStyles.marketCard}>
                      <h3>Comparable Listings</h3>
                      <div className={marketStyles.comparableList}>
                        {marketData.comparableListings.data.listings.slice(0, 3).map((listing, index) => (
                          <div key={index} className={marketStyles.comparableItem}>
                            <p><strong>{listing.year} {listing.make} {listing.model}</strong></p>
                            <p>Price: ${listing.price?.toLocaleString() || 'N/A'}</p>
                            <p>Mileage: {listing.mileage?.toLocaleString() || 'N/A'} miles</p>
                            <p>Location: {listing.location || 'N/A'}</p>
                          </div>
                        ))}
                      </div>
                      <p className={marketStyles.marketInfo}>
                        {marketData.comparableListings.data.message}
                      </p>
                    </div>
                  )}

                  {/* Price History */}
                  {marketData.priceHistory?.success && marketData.priceHistory.data?.history?.length > 0 && (
                    <div className={marketStyles.marketCard}>
                      <h3>Price History</h3>
                      <p className={marketStyles.priceTrend}>
                        Trend: <span className={`${marketStyles.trend} ${marketStyles[marketData.priceHistory.data.trend]}`}>
                          {marketData.priceHistory.data.trend}
                        </span>
                      </p>
                      <p className={marketStyles.marketInfo}>
                        {marketData.priceHistory.data.message}
                      </p>
                    </div>
                  )}
                </div>
              ) : null}
            </div>
          )}

          {/* Message for non-dealers */}
          {!canAccessMarketCheck && vehicleData && (
            <div className={styles.dealerMessage}>
              <h3>Market Pricing Information</h3>
              <p>🔐 Market pricing data is available to registered dealers.</p>
              <p>
                <a href="/login" className={styles.loginLink}>
                  Login as dealer
                </a> to access market value, comparable listings, and price history.
              </p>
            </div>
          )}
        </div>
      )}

      {/* VIN Scanner Modal */}
      {showVINScanner && (
        <VINScanner
          onVINDetected={handleVINDetected}
          onClose={() => setShowVINScanner(false)}
        />
      )}
    </div>
  );
}
