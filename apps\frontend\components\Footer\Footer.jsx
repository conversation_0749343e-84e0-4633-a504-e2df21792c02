"use client";
import React from 'react';
import Link from 'next/link';
import styles from './Footer.module.css';
import { FaFacebook, FaTwitter, FaInstagram, FaTiktok, FaCar } from 'react-icons/fa';
import { MdPhone, MdEmail, MdLocationOn, MdAccessTime } from 'react-icons/md';
import ThemeToggle from '../ThemeToggle/ThemeToggle';
import LanguageSelector from '../LanguageSelector/LanguageSelector';
import { useLanguage } from '../../contexts/LanguageContext';

const Footer = () => {
  const currentYear = new Date().getFullYear();
  const { t } = useLanguage();

  return (
    <footer className={styles.footer}>
      <div className={styles.container}>

        {/* Main Footer Content */}
        <div className={styles.footerContent}>

          {/* Brand Section */}
          <div className={styles.brandSection}>
            <div className={styles.brandHeader}>
              <FaCar className={styles.brandIcon} />
              <h3 className={styles.brandName}>
                <span className={styles.brandFaze}>Faze</span>
                <span className={styles.brandN}>N</span>
                <span className={styles.brandAuto}>Auto</span>
              </h3>
            </div>

            <div className={styles.socialLinks}>
              <a href="https://www.facebook.com/profile.php?id=61577486330108" target="_blank" rel="noopener noreferrer" className={styles.socialLink}>
                <FaFacebook />
              </a>
              <a href="https://x.com/FazeNAuto" target="_blank" rel="noopener noreferrer" className={styles.socialLink}>
                <FaTwitter />
              </a>
              <a href="https://www.instagram.com/fazenauto/" target="_blank" rel="noopener noreferrer" className={styles.socialLink}>
                <FaInstagram />
              </a>
              <a href="https://www.tiktok.com/@fazenauto?lang=en" target="_blank" rel="noopener noreferrer" className={styles.socialLink}>
                <FaTiktok />
              </a>
            </div>
          </div>





          {/* Contact Info and Business Hours */}
          <div className={styles.contactSection}>
            <div className={styles.contactAndHours}>
              <div className={styles.contactInfo}>
                <h4 className={styles.sectionTitle}>{t('footer.contact.title')}</h4>
                <div className={styles.contactItem}>
                  <MdPhone className={styles.contactIcon} />
                  <span>************</span>
                </div>
                <div className={styles.contactItem}>
                  <MdEmail className={styles.contactIcon} />
                  <span><EMAIL></span>
                </div>
                <div className={styles.contactItem}>
                  <MdLocationOn className={styles.contactIcon} />
                  <span>123 Business Ave, Toronto, ON</span>
                </div>
              </div>

              <div className={styles.businessHours}>
                <h5 className={styles.hoursTitle}>
                  <MdAccessTime className={styles.contactIcon} />
                  {t('footer.contact.business_hours')}
                </h5>
                <p className={styles.hoursText}>
                  {t('footer.contact.hours_text').split('\n').map((line, index) => (
                    <React.Fragment key={index}>
                      {line}
                      {index < t('footer.contact.hours_text').split('\n').length - 1 && <br />}
                    </React.Fragment>
                  ))}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Footer Bottom */}
        <div className={styles.footerBottom}>
          <div className={styles.bottomContent}>
            <p className={styles.copyright}>
              &copy; {currentYear} FazeNAuto. {t('footer.copyright')}
            </p>

            {/* Theme and Language Controls */}
            <div className={styles.footerControls}>
              <ThemeToggle showLabel={false} size="small" />
              <LanguageSelector showLabel={false} showFlags={true} variant="select" />
            </div>

            <div className={styles.legalLinks}>
              <Link href="/privacy" className={styles.legalLink}>{t('footer.legal.privacy')}</Link>
              <Link href="/terms" className={styles.legalLink}>{t('footer.legal.terms')}</Link>
              <Link href="/contact" className={styles.legalLink}>{t('footer.legal.contact')}</Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
