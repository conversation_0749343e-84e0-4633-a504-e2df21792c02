/* Inventory Container */
.inventoryContainer {
  padding: 0;
  background-color: #ffffff;
  min-height: 100vh;
  color: var(--text-primary, #333);
  transition: background-color 0.3s ease;
}

[data-theme="dark"] .inventoryContainer {
  background: var(--bg-primary);
}

/* Loading State */
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: var(--text-secondary);
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Desktop Content - Default Layout */
.desktopContent {
  display: block;
}

.header {
  margin-bottom: 2rem;
  padding: 2rem 1rem 0;
}

.header h1 {
  font-size: 2rem;
  margin: 0 0 0.5rem 0;
  color: var(--text-primary);
}

.header p {
  color: var(--text-secondary);
  margin: 0;
}

/* Desktop Search Section */
.desktopSearchSection {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 1.5rem;
  margin: 0 1rem 2rem;
}

.desktopSearchSection .searchContainer {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.desktopSearchSection .searchInput {
  flex: 1;
  padding: 1rem;
  font-size: 1rem;
  border-radius: 12px;
  border: 1px solid var(--border-color);
  background: var(--bg-primary);
  color: var(--text-primary);
  transition: all 0.2s ease;
  min-height: 48px;
}

.desktopSearchSection .searchInput:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.desktopSearchSection .searchButton {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  border: 1px solid var(--border-color);
  background: #3b82f6;
  color: white;
  font-size: 1.125rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.desktopSearchSection .searchButton:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

.desktopSearchSection .searchButton:active {
  transform: scale(0.97);
}

/* Desktop Stats Row */
.desktopSearchSection .statsRow {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

.desktopSearchSection .statItem {
  flex: 1;
  min-width: 120px;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 1rem;
  text-align: center;
}

.desktopSearchSection .statLabel {
  display: block;
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

.desktopSearchSection .statValue {
  display: block;
  font-size: 1.125rem;
  font-weight: 700;
  color: var(--text-primary);
}

/* Desktop Tabs */
.desktopSearchSection .tabsContainer {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

.desktopSearchSection .tab {
  padding: 0.75rem 1.5rem;
  border-radius: 20px;
  border: 1px solid var(--border-color);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.desktopSearchSection .tab:hover {
  background: var(--bg-tertiary);
}

.desktopSearchSection .activeTab {
  background: #3b82f6 !important;
  color: white !important;
  border-color: #3b82f6 !important;
}

/* Desktop Add Vehicle Button */
.desktopSearchSection .addVehicleButton {
  width: 100%;
  padding: 1rem;
  border-radius: 12px;
  border: none;
  background: #3b82f6;
  color: white;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 48px;
}

.desktopSearchSection .addVehicleButton:hover {
  background: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.desktopSearchSection .addVehicleButton:active {
  transform: scale(0.97);
}

/* Desktop Vehicle Grid */
.desktopVehicleGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 1.5rem;
  padding: 0 1rem 2rem;
}

/* Desktop Vehicle Card Styles */
.desktopVehicleGrid .vehicleCard {
  display: flex;
  gap: 1.5rem;
  padding: 1.5rem;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.desktopVehicleGrid .vehicleCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.desktopVehicleGrid .vehicleCard:active {
  transform: scale(0.98);
}

.desktopVehicleGrid .vehicleImageContainer {
  width: 120px;
  height: 120px;
  border-radius: 12px;
  overflow: hidden;
  flex-shrink: 0;
  border: 1px solid var(--border-color);
}

.desktopVehicleGrid .vehicleImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.desktopVehicleGrid .vehicleDetails {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.desktopVehicleGrid .vehicleHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 1rem;
}

.desktopVehicleGrid .vehicleTitle {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
  line-height: 1.3;
  flex: 1;
}

.desktopVehicleGrid .vehicleActions {
  display: flex;
  gap: 0.75rem;
  flex-shrink: 0;
}

.desktopVehicleGrid .editButton,
.desktopVehicleGrid .moreButton {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.desktopVehicleGrid .editButton:hover,
.desktopVehicleGrid .moreButton:hover {
  background: var(--bg-tertiary);
  transform: scale(1.05);
}

.desktopVehicleGrid .vehicleInfo {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;
}

.desktopVehicleGrid .infoRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.desktopVehicleGrid .infoLabel {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.desktopVehicleGrid .infoValue {
  font-size: 0.875rem;
  color: var(--text-primary);
  font-weight: 500;
}

.desktopVehicleGrid .vehicleFooter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
}

.desktopVehicleGrid .vehiclePrice {
  font-size: 1.25rem;
  font-weight: 700;
  color: #10b981;
}

.desktopVehicleGrid .statusBadge {
  padding: 0.5rem 1rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Mobile Content - Hidden by default */
.mobileContent {
  display: none;
}

.mobileHeader {
  display: none;
}

/* MOBILE ONLY STYLES */
@media screen and (max-width: 768px) {
  .inventoryContainer {
    padding: 0.5rem;
  }

  /* Hide desktop content on mobile */
  .desktopContent {
    display: none;
  }

  /* Show mobile content */
  .mobileContent {
    display: block;
  }

  .mobileHeader {
    display: block;
    padding: 1rem 0.75rem;
    background: var(--bg-secondary);
    border-radius: 12px;
    margin-bottom: 1.5rem;
    border: 1px solid var(--border-color);
  }

  .mobileHeader h1 {
    font-size: clamp(1.25rem, 4vw, 1.5rem);
    margin: 0;
    color: var(--text-primary);
    text-align: center;
  }

  /* Search Section */
  .searchSection {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    padding: 1.25rem;
    margin-bottom: 1.5rem;
  }

  .searchContainer {
    display: flex;
    gap: 0.75rem;
    margin-bottom: 1rem;
  }

  .searchInput {
    flex: 1;
    padding: 1rem;
    font-size: clamp(0.875rem, 2.5vw, 1rem);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: all 0.2s ease;
    min-height: 48px;
  }

  .searchInput:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  .searchButton {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    border: 1px solid var(--border-color);
    background: #3b82f6;
    color: white;
    font-size: 1.125rem;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .searchButton:hover {
    background: #2563eb;
    transform: translateY(-1px);
  }

  .searchButton:active {
    transform: scale(0.97);
  }

  /* Stats Row */
  .statsRow {
    display: flex;
    gap: 0.75rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
  }

  .statItem {
    flex: 1;
    min-width: 0;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 0.75rem 0.5rem;
    text-align: center;
  }

  .statLabel {
    display: block;
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin-bottom: 0.25rem;
  }

  .statValue {
    display: block;
    font-size: 0.875rem;
    font-weight: 700;
    color: var(--text-primary);
  }

  /* Tabs Container */
  .tabsContainer {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
    overflow-x: auto;
    padding-bottom: 0.5rem;
  }

  .tab {
    padding: 0.75rem 1rem;
    border-radius: 20px;
    border: 1px solid var(--border-color);
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    flex-shrink: 0;
  }

  .tab:hover {
    background: var(--bg-tertiary);
  }

  .activeTab {
    background: #3b82f6 !important;
    color: white !important;
    border-color: #3b82f6 !important;
  }

  /* Add Vehicle Button */
  .addVehicleButton {
    width: 100%;
    padding: 1rem;
    border-radius: 12px;
    border: none;
    background: #3b82f6;
    color: white;
    font-size: clamp(1rem, 2.5vw, 1.125rem);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 48px;
  }

  .addVehicleButton:hover {
    background: #2563eb;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  }

  .addVehicleButton:active {
    transform: scale(0.97);
  }

  /* Vehicle Listings */
  .vehicleListings {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .noVehicles {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--text-secondary);
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 16px;
  }

  /* Vehicle Card */
  .vehicleCard {
    display: flex;
    gap: 1rem;
    padding: 1rem;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .vehicleCard:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }

  .vehicleCard:active {
    transform: scale(0.98);
  }

  .vehicleImageContainer {
    width: 100px;
    height: 100px;
    border-radius: 12px;
    overflow: hidden;
    flex-shrink: 0;
    border: 1px solid var(--border-color);
  }

  .vehicleImage {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
  }

  .vehicleDetails {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .vehicleHeader {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 1rem;
  }

  .vehicleTitle {
    font-size: clamp(1rem, 3vw, 1.125rem);
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
    line-height: 1.3;
    flex: 1;
  }

  .vehicleActions {
    display: flex;
    gap: 0.5rem;
    flex-shrink: 0;
  }

  .editButton,
  .moreButton {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .editButton:hover,
  .moreButton:hover {
    background: var(--bg-tertiary);
    transform: scale(1.05);
  }

  .vehicleInfo {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  .infoRow {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .infoLabel {
    font-size: 0.875rem;
    color: var(--text-secondary);
  }

  .infoValue {
    font-size: 0.875rem;
    color: var(--text-primary);
    font-weight: 500;
  }

  .vehicleFooter {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 0.5rem;
    border-top: 1px solid var(--border-color);
  }

  .vehiclePrice {
    font-size: 1rem;
    font-weight: 700;
    color: #10b981;
  }

  .statusBadge {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .statusBadge.active {
    background: #d4edda;
    color: #155724;
  }

  .statusBadge.draft {
    background: #fff3cd;
    color: #856404;
  }

  .statusBadge.sold {
    background: #d1ecf1;
    color: #0c5460;
  }

  .statusBadge.pending {
    background: #f8d7da;
    color: #721c24;
  }

  .statusBadge.archived {
    background: #e2e8f0;
    color: #475569;
  }

  .statusBadge.coming_soon {
    background: #fef3c7;
    color: #92400e;
  }

  .statusBadge.in_service {
    background: #ddd6fe;
    color: #5b21b6;
  }

  .statusBadge.financial {
    background: #fed7d7;
    color: #c53030;
  }
}

/* Dark theme status badge adjustments */
[data-theme="dark"] .statusBadge.active {
  background: rgba(16, 185, 129, 0.2);
  color: #10b981;
}

[data-theme="dark"] .statusBadge.draft {
  background: rgba(245, 158, 11, 0.2);
  color: #f59e0b;
}

[data-theme="dark"] .statusBadge.sold {
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
}

[data-theme="dark"] .statusBadge.pending {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

[data-theme="dark"] .statusBadge.archived {
  background: rgba(148, 163, 184, 0.2);
  color: #94a3b8;
}

[data-theme="dark"] .statusBadge.coming_soon {
  background: rgba(245, 158, 11, 0.2);
  color: #f59e0b;
}

[data-theme="dark"] .statusBadge.in_service {
  background: rgba(139, 92, 246, 0.2);
  color: #8b5cf6;
}

[data-theme="dark"] .statusBadge.financial {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}
}

/* Tablet and Desktop Responsive Adjustments */
@media (min-width: 769px) {
  .header {
    padding: 2rem 3rem 0;
  }

  .desktopSearchSection {
    margin: 0 3rem 2rem;
  }

  .desktopVehicleGrid {
    padding: 0 3rem 2rem;
    grid-template-columns: repeat(auto-fill, minmax(450px, 1fr));
  }
}

@media (min-width: 1024px) {
  .desktopVehicleGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1200px) {
  .header {
    padding: 2rem 4rem 0;
  }

  .desktopSearchSection {
    margin: 0 4rem 2rem;
  }

  .desktopVehicleGrid {
    padding: 0 4rem 2rem;
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1600px) {
  .desktopVehicleGrid {
    grid-template-columns: repeat(4, 1fr);
  }
}
