'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useLanguage } from '../../../contexts/LanguageContext';
import styles from './Inventory.module.css';

export default function AdminInventory() {
  const [vehicles, setVehicles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [filters, setFilters] = useState({
    search: '',
    status: 'all',
    make: '',
    model: '',
    year: ''
  });
  const [activeTab, setActiveTab] = useState('all');
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    deactivated: 0,
    totalRetailPrice: 0
  });

  const router = useRouter();
  const { t } = useLanguage();

  useEffect(() => {
    fetchVehicles();
  }, [filters, activeTab]);

  const fetchVehicles = async () => {
    try {
      setLoading(true);
      const queryParams = new URLSearchParams({
        admin: 'true',
        ...Object.fromEntries(Object.entries(filters).filter(([_, value]) => value && value !== 'all'))
      });

      if (activeTab !== 'all') {
        queryParams.set('status', activeTab);
      }

      const response = await fetch(`/api/vehicles?${queryParams}`);
      const data = await response.json();

      if (data.success) {
        const vehicleData = data.data || [];
        setVehicles(vehicleData);
        
        // Calculate stats
        const newStats = {
          total: vehicleData.length,
          active: vehicleData.filter(v => v.status === 'active').length,
          deactivated: vehicleData.filter(v => v.status !== 'active').length,
          totalRetailPrice: vehicleData.reduce((sum, v) => sum + (Number(v.price) || 0), 0)
        };
        setStats(newStats);
      } else {
        setError(data.error || 'Failed to fetch vehicles');
      }
    } catch (err) {
      setError('Error fetching vehicles: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const getImageUrl = (vehicle) => {
    if (vehicle.images && vehicle.images.length > 0) {
      return vehicle.images[0];
    }
    return vehicle.imageUrl || '/placeholder-car.jpg';
  };

  const handleVehicleClick = (vehicleId) => {
    router.push(`/admin/inventory/${vehicleId}`);
  };

  const handleAddVehicle = () => {
    router.push('/admin/vehicles/upload');
  };

  const tabs = [
    { id: 'all', label: 'All', count: stats.total },
    { id: 'active', label: 'Current', count: stats.active },
    { id: 'coming_soon', label: 'Coming Soon', count: vehicles.filter(v => v.status === 'coming_soon').length },
    { id: 'pending', label: 'Pending', count: vehicles.filter(v => v.status === 'pending').length },
    { id: 'sold', label: 'Sold', count: vehicles.filter(v => v.status === 'sold').length }
  ];

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.spinner}></div>
        <p>Loading inventory...</p>
      </div>
    );
  }

  return (
    <div className={styles.inventoryContainer}>
      {/* Mobile Header */}
      <div className={styles.mobileHeader}>
        <h1>{t('admin.inventory_management', 'Inventory Management')}</h1>
      </div>

      {/* Desktop Content - Card-based layout */}
      <div className={styles.desktopContent}>
        <div className={styles.header}>
          <h1>{t('admin.inventory_management', 'Inventory Management')}</h1>
          <p>Manage your vehicle inventory</p>
        </div>

        {/* Search and Filters for Desktop */}
        <div className={styles.desktopSearchSection}>
          <div className={styles.searchContainer}>
            <input
              type="text"
              placeholder="Find your Vehicle (year make model) -All"
              value={filters.search}
              onChange={(e) => setFilters({ ...filters, search: e.target.value })}
              className={styles.searchInput}
            />
            <button className={styles.searchButton}>
              🔍
            </button>
          </div>

          {/* Stats Summary */}
          <div className={styles.statsRow}>
            <div className={styles.statItem}>
              <span className={styles.statLabel}>Total:</span>
              <span className={styles.statValue}>{stats.total}</span>
            </div>
            <div className={styles.statItem}>
              <span className={styles.statLabel}>Active:</span>
              <span className={styles.statValue}>{stats.active}</span>
            </div>
            <div className={styles.statItem}>
              <span className={styles.statLabel}>Deactivate:</span>
              <span className={styles.statValue}>{stats.deactivated}</span>
            </div>
            <div className={styles.statItem}>
              <span className={styles.statLabel}>$</span>
              <span className={styles.statValue}>
                ${stats.totalRetailPrice.toLocaleString()}
              </span>
            </div>
          </div>

          {/* Filter Tabs */}
          <div className={styles.tabsContainer}>
            {tabs.map((tab) => (
              <button
                key={tab.id}
                className={`${styles.tab} ${activeTab === tab.id ? styles.activeTab : ''}`}
                onClick={() => setActiveTab(tab.id)}
              >
                {tab.label}
              </button>
            ))}
          </div>

          {/* Add Vehicle Button */}
          <button
            className={styles.addVehicleButton}
            onClick={handleAddVehicle}
          >
            + Add Vehicle
          </button>
        </div>

        {/* Desktop Vehicle Grid */}
        <div className={styles.desktopVehicleGrid}>
          {vehicles.length === 0 ? (
            <div className={styles.noVehicles}>
              <p>No vehicles found</p>
            </div>
          ) : (
            vehicles.map((vehicle) => (
              <div
                key={vehicle._id}
                className={styles.vehicleCard}
                onClick={() => handleVehicleClick(vehicle._id)}
              >
                <div className={styles.vehicleImageContainer}>
                  <img
                    src={getImageUrl(vehicle)}
                    alt={`${vehicle.year} ${vehicle.make} ${vehicle.model}`}
                    className={styles.vehicleImage}
                    onError={(e) => {
                      e.target.src = '/placeholder-car.jpg';
                    }}
                  />
                </div>

                <div className={styles.vehicleDetails}>
                  <div className={styles.vehicleHeader}>
                    <h3 className={styles.vehicleTitle}>
                      {vehicle.year} {vehicle.make} {vehicle.model}
                    </h3>
                    <div className={styles.vehicleActions}>
                      <button
                        className={styles.editButton}
                        title="Edit Vehicle"
                        onClick={(e) => {
                          e.stopPropagation();
                          router.push(`/admin/vehicles/edit/${vehicle._id}`);
                        }}
                      >
                        ✏️
                      </button>
                      <button
                        className={styles.moreButton}
                        title="More Options"
                      >
                        ⋮
                      </button>
                    </div>
                  </div>

                  <div className={styles.vehicleInfo}>
                    <div className={styles.infoRow}>
                      <span className={styles.infoLabel}>Year:</span>
                      <span className={styles.infoValue}>
                        {vehicle.year || 'N/A'}
                      </span>
                    </div>
                    <div className={styles.infoRow}>
                      <span className={styles.infoLabel}>Make:</span>
                      <span className={styles.infoValue}>
                        {vehicle.make || 'N/A'}
                      </span>
                    </div>
                    <div className={styles.infoRow}>
                      <span className={styles.infoLabel}>Model:</span>
                      <span className={styles.infoValue}>
                        {vehicle.model || 'N/A'}
                      </span>
                    </div>
                    <div className={styles.infoRow}>
                      <span className={styles.infoLabel}>VIN:</span>
                      <span className={styles.infoValue}>
                        {vehicle.vin ? vehicle.vin.slice(-8) : 'N/A'}
                      </span>
                    </div>
                    <div className={styles.infoRow}>
                      <span className={styles.infoLabel}>Stock #:</span>
                      <span className={styles.infoValue}>
                        {vehicle.stockNumber || 'N/A'}
                      </span>
                    </div>
                  </div>

                  <div className={styles.vehicleFooter}>
                    <div className={styles.vehiclePrice}>
                      {vehicle.price ? `$${Number(vehicle.price).toLocaleString()}` : 'Price TBD'}
                    </div>
                    <div className={`${styles.statusBadge} ${styles[vehicle.status]}`}>
                      {vehicle.status}
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Mobile Content */}
      <div className={styles.mobileContent}>
        {/* Search and Filters */}
        <div className={styles.searchSection}>
          <div className={styles.searchContainer}>
            <input
              type="text"
              placeholder="Find your Vehicle (year make model) -All"
              value={filters.search}
              onChange={(e) => setFilters({ ...filters, search: e.target.value })}
              className={styles.searchInput}
            />
            <button className={styles.searchButton}>
              🔍
            </button>
          </div>

          {/* Stats Summary */}
          <div className={styles.statsRow}>
            <div className={styles.statItem}>
              <span className={styles.statLabel}>Total:</span>
              <span className={styles.statValue}>{stats.total}</span>
            </div>
            <div className={styles.statItem}>
              <span className={styles.statLabel}>Active:</span>
              <span className={styles.statValue}>{stats.active}</span>
            </div>
            <div className={styles.statItem}>
              <span className={styles.statLabel}>Deactivate:</span>
              <span className={styles.statValue}>{stats.deactivated}</span>
            </div>
            <div className={styles.statItem}>
              <span className={styles.statLabel}>$</span>
              <span className={styles.statValue}>
                ${stats.totalRetailPrice.toLocaleString()}
              </span>
            </div>
          </div>

          {/* Filter Tabs */}
          <div className={styles.tabsContainer}>
            {tabs.map((tab) => (
              <button
                key={tab.id}
                className={`${styles.tab} ${activeTab === tab.id ? styles.activeTab : ''}`}
                onClick={() => setActiveTab(tab.id)}
              >
                {tab.label}
              </button>
            ))}
          </div>

          {/* Add Vehicle Button */}
          <button 
            className={styles.addVehicleButton}
            onClick={handleAddVehicle}
          >
            + Add Vehicle
          </button>
        </div>

        {/* Vehicle Listings */}
        <div className={styles.vehicleListings}>
          {vehicles.length === 0 ? (
            <div className={styles.noVehicles}>
              <p>No vehicles found</p>
            </div>
          ) : (
            vehicles.map((vehicle) => (
              <div 
                key={vehicle._id} 
                className={styles.vehicleCard}
                onClick={() => handleVehicleClick(vehicle._id)}
              >
                <div className={styles.vehicleImageContainer}>
                  <img 
                    src={getImageUrl(vehicle)} 
                    alt={`${vehicle.year} ${vehicle.make} ${vehicle.model}`}
                    className={styles.vehicleImage}
                    onError={(e) => {
                      e.target.src = '/placeholder-car.jpg';
                    }}
                  />
                </div>
                
                <div className={styles.vehicleDetails}>
                  <div className={styles.vehicleHeader}>
                    <h3 className={styles.vehicleTitle}>
                      {vehicle.year} {vehicle.make} {vehicle.model}
                    </h3>
                    <div className={styles.vehicleActions}>
                      <button
                        className={styles.editButton}
                        title="Edit Vehicle"
                        onClick={(e) => {
                          e.stopPropagation();
                          router.push(`/admin/vehicles/edit/${vehicle._id}`);
                        }}
                      >
                        ✏️
                      </button>
                      <button
                        className={styles.moreButton}
                        title="More Options"
                      >
                        ⋮
                      </button>
                    </div>
                  </div>
                  
                  <div className={styles.vehicleInfo}>
                    <div className={styles.infoRow}>
                      <span className={styles.infoLabel}>Year:</span>
                      <span className={styles.infoValue}>
                        {vehicle.year || 'N/A'}
                      </span>
                    </div>
                    <div className={styles.infoRow}>
                      <span className={styles.infoLabel}>Make:</span>
                      <span className={styles.infoValue}>
                        {vehicle.make || 'N/A'}
                      </span>
                    </div>
                    <div className={styles.infoRow}>
                      <span className={styles.infoLabel}>Model:</span>
                      <span className={styles.infoValue}>
                        {vehicle.model || 'N/A'}
                      </span>
                    </div>
                    <div className={styles.infoRow}>
                      <span className={styles.infoLabel}>VIN:</span>
                      <span className={styles.infoValue}>
                        {vehicle.vin ? vehicle.vin.slice(-8) : 'N/A'}
                      </span>
                    </div>
                    <div className={styles.infoRow}>
                      <span className={styles.infoLabel}>Stock #:</span>
                      <span className={styles.infoValue}>
                        {vehicle.stockNumber || 'N/A'}
                      </span>
                    </div>
                  </div>
                  
                  <div className={styles.vehicleFooter}>
                    <div className={styles.vehiclePrice}>
                      {vehicle.price ? `$${Number(vehicle.price).toLocaleString()}` : 'Price TBD'}
                    </div>
                    <div className={`${styles.statusBadge} ${styles[vehicle.status]}`}>
                      {vehicle.status}
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
}
