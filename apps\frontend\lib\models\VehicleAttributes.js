import mongoose from 'mongoose';

const VehicleAttributesSchema = new mongoose.Schema({
  type: {
    type: String,
    required: true,
    enum: ['engine', 'transmission', 'drivetrain', 'fueltype', 'bodyclass', 'doors', 'cylinders'],
    unique: true
  },
  values: {
    type: [String],
    required: true,
    default: []
  }
}, {
  timestamps: true
});

// Create indexes for better query performance
VehicleAttributesSchema.index({ type: 1 });

export default mongoose.models.VehicleAttributes || mongoose.model('VehicleAttributes', VehicleAttributesSchema, 'vehicle_attributes');
