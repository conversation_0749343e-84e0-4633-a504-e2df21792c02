/**
 * Authentication Utilities
 * Provides functions for checking user authentication and roles
 */

/**
 * Get current user from localStorage
 * @returns {Object|null} - User object or null if not authenticated
 */
export function getCurrentUser() {
  if (typeof window === 'undefined') {
    // Server-side rendering - no localStorage access
    return null;
  }

  try {
    const storedUser = localStorage.getItem('user');
    if (!storedUser) {
      return null;
    }

    const userData = JSON.parse(storedUser);
    
    // Validate user data structure
    if (!userData || !userData.email || !userData.role) {
      console.warn('Invalid user data found in localStorage, clearing...');
      localStorage.removeItem('user');
      return null;
    }

    // Additional validation
    if (typeof userData.email !== 'string' || 
        typeof userData.role !== 'string' ||
        !userData.email.includes('@')) {
      console.warn('Malformed user data found in localStorage, clearing...');
      localStorage.removeItem('user');
      return null;
    }

    return userData;
  } catch (error) {
    console.error('Error parsing user data from localStorage:', error);
    localStorage.removeItem('user');
    return null;
  }
}

/**
 * Check if user is currently logged in
 * @returns {boolean} - True if user is authenticated
 */
export function isAuthenticated() {
  const user = getCurrentUser();
  return user !== null;
}

/**
 * Check if current user has a specific role
 * @param {string} requiredRole - Required role ('admin', 'dealer', etc.)
 * @returns {boolean} - True if user has the required role
 */
export function hasRole(requiredRole) {
  const user = getCurrentUser();
  if (!user) {
    return false;
  }

  // Admin users have access to all roles
  if (user.role === 'admin') {
    return true;
  }

  return user.role === requiredRole;
}

/**
 * Check if current user is a dealer (has dealer or admin role)
 * @returns {boolean} - True if user is a dealer or admin
 */
export function isDealer() {
  return hasRole('dealer') || hasRole('admin');
}

/**
 * Check if current user is an admin
 * @returns {boolean} - True if user is an admin
 */
export function isAdmin() {
  return hasRole('admin');
}

/**
 * Check if user can access MarketCheck pricing data
 * Only dealers and admins can access MarketCheck to preserve API quota
 * @returns {boolean} - True if user can access MarketCheck data
 */
export function canAccessMarketCheck() {
  return isDealer();
}

/**
 * Get user role display name
 * @returns {string} - Human-readable role name
 */
export function getUserRoleDisplay() {
  const user = getCurrentUser();
  if (!user) {
    return 'Guest';
  }

  switch (user.role) {
    case 'admin':
      return 'Administrator';
    case 'dealer':
      return 'Dealer';
    default:
      return 'User';
  }
}

/**
 * Logout current user
 * Clears user data from localStorage
 */
export function logout() {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('user');
    // Also clear any other auth-related data
    localStorage.removeItem('authToken');
    localStorage.removeItem('refreshToken');
  }
}

/**
 * Login user and store in localStorage
 * @param {Object} userData - User data to store
 * @param {string} userData.email - User email
 * @param {string} userData.role - User role
 * @param {string} [userData.name] - User name
 * @param {string} [userData.token] - Auth token
 */
export function loginUser(userData) {
  if (typeof window === 'undefined') {
    console.warn('Cannot login user on server side');
    return;
  }

  // Validate required fields
  if (!userData.email || !userData.role) {
    throw new Error('Email and role are required for login');
  }

  try {
    localStorage.setItem('user', JSON.stringify(userData));
    console.log('✅ User logged in successfully:', userData.email, userData.role);
  } catch (error) {
    console.error('❌ Failed to store user data:', error);
    throw new Error('Failed to store user data');
  }
}

/**
 * Check if user session is still valid
 * This is a basic check - in a production app you might want to verify with the server
 * @returns {boolean} - True if session appears valid
 */
export function isSessionValid() {
  const user = getCurrentUser();
  if (!user) {
    return false;
  }

  // Add any additional session validation logic here
  // For example, check token expiration, last activity, etc.
  
  return true;
}

/**
 * Get authorization header for API requests
 * @returns {Object} - Authorization headers object
 */
export function getAuthHeaders() {
  const user = getCurrentUser();
  if (!user || !user.token) {
    return {};
  }

  return {
    'Authorization': `Bearer ${user.token}`
  };
}

/**
 * Higher-order function to protect components that require authentication
 * @param {React.Component} Component - Component to protect
 * @param {string} [requiredRole] - Required role for access
 * @returns {React.Component} - Protected component
 */
export function withAuth(Component, requiredRole = null) {
  return function AuthenticatedComponent(props) {
    const user = getCurrentUser();
    
    if (!user) {
      return (
        <div style={{ padding: '2rem', textAlign: 'center' }}>
          <h2>Authentication Required</h2>
          <p>Please log in to access this page.</p>
          <a href="/login" style={{ color: '#007bff', textDecoration: 'none' }}>
            Go to Login
          </a>
        </div>
      );
    }

    if (requiredRole && !hasRole(requiredRole)) {
      return (
        <div style={{ padding: '2rem', textAlign: 'center' }}>
          <h2>Access Denied</h2>
          <p>You don't have permission to access this page.</p>
          <p>Required role: {requiredRole}</p>
          <p>Your role: {user.role}</p>
        </div>
      );
    }

    return <Component {...props} />;
  };
}

/**
 * Hook for using authentication in React components
 * @returns {Object} - Authentication state and functions
 */
export function useAuth() {
  const user = getCurrentUser();
  
  return {
    user,
    isAuthenticated: isAuthenticated(),
    isDealer: isDealer(),
    isAdmin: isAdmin(),
    canAccessMarketCheck: canAccessMarketCheck(),
    hasRole: hasRole,
    logout: logout,
    getUserRoleDisplay: getUserRoleDisplay(),
    isSessionValid: isSessionValid()
  };
}

export default {
  getCurrentUser,
  isAuthenticated,
  hasRole,
  isDealer,
  isAdmin,
  canAccessMarketCheck,
  getUserRoleDisplay,
  logout,
  loginUser,
  isSessionValid,
  getAuthHeaders,
  withAuth,
  useAuth
};
