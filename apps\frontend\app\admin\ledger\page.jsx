'use client';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import styles from './ledger.module.css';

export default function LedgerPage() {
  const router = useRouter();
  const [ledgerEntries, setLedgerEntries] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [summary, setSummary] = useState({
    totalIncome: 0,
    totalExpenses: 0,
    incomeCount: 0,
    expenseCount: 0,
    netAmount: 0
  });
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 50,
    total: 0,
    pages: 0
  });

  // Filter states
  const [filters, setFilters] = useState({
    search: '',
    type: '',
    category: '',
    isIncome: '',
    vehicleId: '',
    addedBy: '',
    startDate: '',
    endDate: '',
    minAmount: '',
    maxAmount: ''
  });

  const [showFilters, setShowFilters] = useState(false);

  // Transaction types for filter dropdown
  const transactionTypes = [
    'Vehicle Purchase',
    'Repair Cost',
    'General Cost',
    'Sale',
    'Insurance',
    'Registration',
    'Inspection',
    'Marketing',
    'Office Supplies',
    'Utilities',
    'Other Income',
    'Other Expense'
  ];

  const categories = [
    'vehicle-purchase',
    'vehicle-repair',
    'general-cost',
    'sale',
    'insurance',
    'registration',
    'inspection',
    'marketing',
    'office',
    'utilities',
    'other'
  ];

  useEffect(() => {
    fetchLedgerEntries();
  }, [pagination.page, filters]);

  const fetchLedgerEntries = async () => {
    try {
      setLoading(true);
      
      const queryParams = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        ...Object.fromEntries(
          Object.entries(filters).filter(([_, value]) => value !== '')
        )
      });

      const response = await fetch(`/api/ledger?${queryParams}`);
      const data = await response.json();

      if (data.success) {
        setLedgerEntries(data.data.entries);
        setPagination(data.data.pagination);
        setSummary(data.data.summary);
      } else {
        setError(data.error);
      }
    } catch (error) {
      console.error('Error fetching ledger entries:', error);
      setError('Failed to fetch ledger entries');
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
    setPagination(prev => ({ ...prev, page: 1 })); // Reset to first page
  };

  const clearFilters = () => {
    setFilters({
      search: '',
      type: '',
      category: '',
      isIncome: '',
      vehicleId: '',
      addedBy: '',
      startDate: '',
      endDate: '',
      minAmount: '',
      maxAmount: ''
    });
  };

  const handleExport = async (format = 'csv') => {
    try {
      const queryParams = new URLSearchParams({
        format,
        ...Object.fromEntries(
          Object.entries(filters).filter(([_, value]) => value !== '')
        )
      });

      const response = await fetch(`/api/ledger/export?${queryParams}`);

      if (format === 'csv' || format === 'pdf') {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `ledger-export-${new Date().toISOString().split('T')[0]}.${format}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      console.error('Error exporting data:', error);
      alert('Failed to export data');
    }
  };

  const formatCurrency = (amount, currency = 'CAD') => {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: currency
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-CA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading && ledgerEntries.length === 0) {
    return (
      <div className={styles.container}>
        <div className={styles.loading}>
          <div className={styles.spinner}></div>
          <p>Loading ledger entries...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.titleSection}>
          <h1>💰 Accounting Ledger</h1>
          <p>Track all financial transactions and receipts</p>
        </div>
        
        <div className={styles.actions}>
          <Link href="/admin/ledger/new" className={styles.addButton}>
            ➕ Add Transaction
          </Link>
          <button 
            onClick={() => setShowFilters(!showFilters)}
            className={styles.filterToggle}
          >
            🔍 {showFilters ? 'Hide' : 'Show'} Filters
          </button>
          <button
            onClick={() => handleExport('csv')}
            className={styles.exportButton}
          >
            📊 Export CSV
          </button>
          <button
            onClick={() => handleExport('pdf')}
            className={styles.exportButton}
          >
            📄 Export PDF
          </button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className={styles.summaryCards}>
        <div className={styles.summaryCard}>
          <h3>💚 Total Income</h3>
          <p className={styles.incomeAmount}>{formatCurrency(summary.totalIncome)}</p>
          <small>{summary.incomeCount} transactions</small>
        </div>
        <div className={styles.summaryCard}>
          <h3>💸 Total Expenses</h3>
          <p className={styles.expenseAmount}>{formatCurrency(summary.totalExpenses)}</p>
          <small>{summary.expenseCount} transactions</small>
        </div>
        <div className={styles.summaryCard}>
          <h3>📊 Net Amount</h3>
          <p className={summary.netAmount >= 0 ? styles.incomeAmount : styles.expenseAmount}>
            {formatCurrency(summary.netAmount)}
          </p>
          <small>{summary.incomeCount + summary.expenseCount} total transactions</small>
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <div className={styles.filtersPanel}>
          <div className={styles.filtersGrid}>
            <input
              type="text"
              placeholder="Search description..."
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              className={styles.filterInput}
            />
            
            <select
              value={filters.type}
              onChange={(e) => handleFilterChange('type', e.target.value)}
              className={styles.filterSelect}
            >
              <option value="">All Types</option>
              {transactionTypes.map(type => (
                <option key={type} value={type}>{type}</option>
              ))}
            </select>

            <select
              value={filters.isIncome}
              onChange={(e) => handleFilterChange('isIncome', e.target.value)}
              className={styles.filterSelect}
            >
              <option value="">Income & Expenses</option>
              <option value="true">Income Only</option>
              <option value="false">Expenses Only</option>
            </select>

            <input
              type="date"
              placeholder="Start Date"
              value={filters.startDate}
              onChange={(e) => handleFilterChange('startDate', e.target.value)}
              className={styles.filterInput}
            />

            <input
              type="date"
              placeholder="End Date"
              value={filters.endDate}
              onChange={(e) => handleFilterChange('endDate', e.target.value)}
              className={styles.filterInput}
            />

            <input
              type="number"
              placeholder="Min Amount"
              value={filters.minAmount}
              onChange={(e) => handleFilterChange('minAmount', e.target.value)}
              className={styles.filterInput}
            />

            <input
              type="number"
              placeholder="Max Amount"
              value={filters.maxAmount}
              onChange={(e) => handleFilterChange('maxAmount', e.target.value)}
              className={styles.filterInput}
            />

            <input
              type="text"
              placeholder="Added by..."
              value={filters.addedBy}
              onChange={(e) => handleFilterChange('addedBy', e.target.value)}
              className={styles.filterInput}
            />
          </div>
          
          <div className={styles.filterActions}>
            <button onClick={clearFilters} className={styles.clearButton}>
              🗑️ Clear Filters
            </button>
          </div>
        </div>
      )}

      {error && (
        <div className={styles.error}>
          <p>❌ {error}</p>
        </div>
      )}

      {/* Ledger Table */}
      <div className={styles.tableContainer}>
        <table className={styles.ledgerTable}>
          <thead>
            <tr>
              <th>Date</th>
              <th>Type</th>
              <th>Description</th>
              <th>Amount</th>
              <th>Vehicle</th>
              <th>Receipts</th>
              <th>Added By</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {ledgerEntries.map((entry) => (
              <tr key={entry._id} className={entry.isIncome ? styles.incomeRow : styles.expenseRow}>
                <td>{formatDate(entry.date)}</td>
                <td>
                  <span className={`${styles.typeTag} ${styles[entry.category]}`}>
                    {entry.type}
                  </span>
                </td>
                <td className={styles.description}>{entry.description}</td>
                <td className={entry.isIncome ? styles.incomeAmount : styles.expenseAmount}>
                  {entry.isIncome ? '+' : '-'}{formatCurrency(entry.amount, entry.currency)}
                </td>
                <td>
                  {entry.vehicleId ? (
                    <span className={styles.vehicleInfo}>
                      {entry.vehicleId.displayName || `${entry.vehicleId.year} ${entry.vehicleId.make} ${entry.vehicleId.model}`}
                    </span>
                  ) : (
                    <span className={styles.noVehicle}>—</span>
                  )}
                </td>
                <td>
                  {entry.receiptCount > 0 ? (
                    <span className={styles.receiptCount}>
                      📎 {entry.receiptCount}
                    </span>
                  ) : (
                    <span className={styles.noReceipts}>—</span>
                  )}
                </td>
                <td>{entry.addedBy}</td>
                <td>
                  <Link 
                    href={`/admin/ledger/${entry._id}`}
                    className={styles.viewButton}
                  >
                    👁️ View
                  </Link>
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        {ledgerEntries.length === 0 && !loading && (
          <div className={styles.noData}>
            <p>📝 No ledger entries found</p>
            <Link href="/admin/ledger/new" className={styles.addButton}>
              Add your first transaction
            </Link>
          </div>
        )}
      </div>

      {/* Pagination */}
      {pagination.pages > 1 && (
        <div className={styles.pagination}>
          <button
            onClick={() => setPagination(prev => ({ ...prev, page: Math.max(1, prev.page - 1) }))}
            disabled={pagination.page === 1}
            className={styles.pageButton}
          >
            ← Previous
          </button>
          
          <span className={styles.pageInfo}>
            Page {pagination.page} of {pagination.pages} ({pagination.total} total)
          </span>
          
          <button
            onClick={() => setPagination(prev => ({ ...prev, page: Math.min(prev.pages, prev.page + 1) }))}
            disabled={pagination.page === pagination.pages}
            className={styles.pageButton}
          >
            Next →
          </button>
        </div>
      )}
    </div>
  );
}
