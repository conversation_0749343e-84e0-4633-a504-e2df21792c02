/* VIN Scanner Component Styles */

.scannerOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  backdrop-filter: blur(4px);
}

.scannerContainer {
  background: var(--bg-primary);
  border-radius: 16px;
  padding: 1.5rem;
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: var(--card-shadow-hover);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid var(--border-primary);
}

.header h3 {
  margin: 0;
  color: var(--text-primary);
  font-size: 1.5rem;
  font-weight: 600;
}

.closeBtn {
  background: none;
  border: none;
  font-size: 2rem;
  color: #718096;
  cursor: pointer;
  padding: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.closeBtn:hover {
  background: #f7fafc;
  color: #e53e3e;
}

.videoContainer {
  position: relative;
  margin-bottom: 1rem;
  border-radius: 12px;
  overflow: hidden;
  background: #000;
}

.video {
  width: 100%;
  height: 300px;
  object-fit: cover;
  display: block;
}

.scanningOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.3);
}

.scanFrame {
  position: relative;
  width: 280px;
  height: 80px;
  border: 2px solid #e53e3e;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.corner {
  position: absolute;
  width: 20px;
  height: 20px;
  border: 3px solid #ffffff;
}

.corner:nth-child(1) {
  top: -3px;
  left: -3px;
  border-right: none;
  border-bottom: none;
}

.corner:nth-child(2) {
  top: -3px;
  right: -3px;
  border-left: none;
  border-bottom: none;
}

.corner:nth-child(3) {
  bottom: -3px;
  left: -3px;
  border-right: none;
  border-top: none;
}

.corner:nth-child(4) {
  bottom: -3px;
  right: -3px;
  border-left: none;
  border-top: none;
}

.instruction {
  color: white;
  text-align: center;
  margin: 0;
  font-size: 0.9rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

.error {
  background: #fed7d7;
  color: #c53030;
  padding: 0.75rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  font-size: 0.9rem;
  border: 1px solid #feb2b2;
}

.detectedText {
  background: #c6f6d5;
  color: #22543d;
  padding: 0.75rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  font-size: 0.9rem;
  border: 1px solid #9ae6b4;
  word-break: break-all;
}

.controls {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.scanBtn {
  flex: 1;
  background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(229, 62, 62, 0.2);
}

.scanBtn:hover:not(:disabled) {
  background: linear-gradient(135deg, #c53030 0%, #9b2c2c 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(229, 62, 62, 0.3);
}

.scanBtn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.manualBtn {
  flex: 1;
  background: #f7fafc;
  color: #4a5568;
  border: 2px solid #e2e8f0;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.manualBtn:hover {
  background: #edf2f7;
  border-color: #cbd5e0;
  color: #2d3748;
}

.tips {
  background: #f7fafc;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.tips h4 {
  margin: 0 0 0.5rem 0;
  color: #2d3748;
  font-size: 0.9rem;
  font-weight: 600;
}

.tips ul {
  margin: 0;
  padding-left: 1.2rem;
  color: #4a5568;
  font-size: 0.85rem;
}

.tips li {
  margin-bottom: 0.25rem;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .scannerContainer {
    width: 95%;
    padding: 1rem;
    max-height: 95vh;
  }
  
  .header h3 {
    font-size: 1.25rem;
  }
  
  .video {
    height: 250px;
  }
  
  .scanFrame {
    width: 240px;
    height: 70px;
  }
  
  .controls {
    flex-direction: column;
  }
  
  .scanBtn,
  .manualBtn {
    width: 100%;
  }
}
