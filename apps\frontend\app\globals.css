
:root {
  /* Light theme variables */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-tertiary: #94a3b8;
  --border-primary: rgba(226, 232, 240, 0.4);
  --border-secondary: rgba(203, 213, 225, 0.6);
  --accent-primary: rgba(99, 179, 237, 0.9);
  --accent-hover: #2563eb;
  --accent-secondary: #10b981;

  /* Status colors */
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: #3b82f6;

  /* Error states for light mode */
  --error-bg: #fed7d7;
  --error-border: #feb2b2;

  /* Component specific */
  --card-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --card-shadow-hover: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --bg-hover: #f8f9fa;

  /* FazeNAuto brand colors */
  --brand-red: #ef4444;
  --brand-white: #ffffff;

  /* Input styling variables */
  --input-border: 1px solid rgba(226, 232, 240, 0.4);
  --input-border-light: 1px solid rgba(0, 0, 0, 0.08);
  --input-focus-border-light: 1px solid #e74c3c;
  --input-radius: 8px;
  --input-padding: 10px 14px;
  --input-bg-light: #ffffff;
  --input-bg-dark: #2d3748;
  --input-color-light: #4a5568;
  --input-color-dark: #e2e8f0;

  /* Legacy support */
  --background: var(--bg-primary);
  --foreground: var(--text-primary);
}

[data-theme="dark"] {
  /* Dark theme variables with gradient backgrounds */
  --bg-primary: linear-gradient(to bottom right, #0d1117, #161b22);
  --bg-secondary: #161b22;
  --bg-tertiary: #334155;
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-tertiary: #94a3b8;
  --border-primary: rgba(226, 232, 240, 0.3);
  --border-secondary: rgba(71, 85, 105, 0.6);
  --accent-primary: rgba(99, 179, 237, 0.9);
  --accent-hover: #2563eb;
  --accent-secondary: #10b981;

  /* Status colors for dark mode */
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: #3b82f6;

  /* Error states for dark mode */
  --error-bg: #1f2937;
  --error-border: #374151;

  /* Component specific for dark mode */
  --card-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
  --card-shadow-hover: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --bg-hover: #475569;

  /* Legacy support */
  --background: var(--bg-primary);
  --foreground: var(--text-primary);
}

/* Base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  overflow-x: hidden;
}

body {
  background: var(--bg-primary);
  color: var(--text-primary);
  font-family: "Poppins", sans-serif, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: background 0.3s ease, color 0.3s ease;
  max-width: 100vw;
  overflow-x: hidden;
}

/* Root layout styles */
.root-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: var(--bg-primary);
  color: var(--text-primary);
}

/* App layout wrapper */
.app-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* Main content area */
.main-content {
  flex: 1;
  background: var(--bg-primary);
  color: var(--text-primary);
}

/* Global FOUC prevention - hide entire app during translation loading */
.app-layout.translations-loading {
  opacity: 0;
  pointer-events: none;
}

.app-layout:not(.translations-loading) {
  opacity: 1;
  transition: opacity 0.15s ease-in;
}

/* Smooth transitions for theme changes */
*,
*::before,
*::after {
  transition: background 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Common component styles */
.card {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: 0.5rem;
  box-shadow: var(--card-shadow);
  transition: box-shadow 0.2s ease;
}

.card:hover {
  box-shadow: var(--card-shadow-hover);
}

.button-primary {
  background: var(--accent-primary);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 150ms ease-in-out;
  position: relative;
  overflow: hidden;
  box-shadow:
    inset 0 1px rgba(255, 255, 255, 0.15),
    0 2px 4px rgba(0, 0, 0, 0.1);
}

.button-primary::before {
  content: "";
  position: absolute;
  inset: 0;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.2), transparent);
  pointer-events: none;
  z-index: 1;
}

.button-primary:hover {
  background: var(--accent-hover);
  filter: brightness(1.05);
}

.button-primary:active {
  transform: scale(0.97);
  box-shadow:
    inset 0 2px 4px rgba(0, 0, 0, 0.25);
}

.button-primary > * {
  position: relative;
  z-index: 2;
}

.button-secondary {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-primary);
  padding: 0.75rem 1.5rem;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 150ms ease-in-out;
  position: relative;
  overflow: hidden;
  box-shadow:
    inset 0 1px rgba(255, 255, 255, 0.15),
    0 2px 4px rgba(0, 0, 0, 0.1);
}

.button-secondary::before {
  content: "";
  position: absolute;
  inset: 0;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.2), transparent);
  pointer-events: none;
  z-index: 1;
}

.button-secondary:hover {
  background: var(--bg-tertiary);
  filter: brightness(1.05);
}

.button-secondary:active {
  transform: scale(0.97);
  box-shadow:
    inset 0 2px 4px rgba(0, 0, 0, 0.25);
}

.button-secondary > * {
  position: relative;
  z-index: 2;
}

/* ===== GLOSSY BUTTON EFFECTS ===== */

/* Base glossy button styling for View Details and Contact Us buttons */
.glossy-button {
  position: relative;
  cursor: pointer;
  overflow: hidden;
  border-radius: 6px;
  border: none;
  font-weight: bold;
  color: white;
  box-shadow:
    inset 0 1px rgba(255, 255, 255, 0.15),
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -2px rgba(0, 0, 0, 0.1);
  transition: all 150ms ease-in-out;
}

.glossy-button::before {
  content: "";
  position: absolute;
  inset: 0;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.2), transparent);
  pointer-events: none;
  z-index: 1;
}

.glossy-button:hover {
  filter: brightness(1.05);
}

.glossy-button:active {
  transform: scale(0.97);
  box-shadow:
    inset 0 2px 4px rgba(0, 0, 0, 0.25),
    0 0 #0000;
}

.glossy-button > * {
  position: relative;
  z-index: 2;
}

/* Specific colors for View Details and Contact Us buttons */
.view-btn {
  background-color: #3b82f6; /* Blue */
}

.contact-btn {
  background-color: #10b981; /* Green */
}

/* Enhanced styling for all other buttons - use .enhanced-button class */
.enhanced-button {
  position: relative;
  overflow: hidden;
  cursor: pointer;
  box-shadow:
    inset 0 1px rgba(255, 255, 255, 0.15),
    0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 150ms ease-in-out;
}

.enhanced-button::before {
  content: "";
  position: absolute;
  inset: 0;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.2), transparent);
  pointer-events: none;
  z-index: 1;
}

.enhanced-button:hover {
  filter: brightness(1.05);
}

.enhanced-button:active {
  transform: scale(0.97);
  box-shadow:
    inset 0 2px 4px rgba(0, 0, 0, 0.25);
}

.enhanced-button > * {
  position: relative;
  z-index: 2;
}



/* Global Input, Select, and Textarea Styling - Unified Style */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="tel"],
input[type="url"],
input[type="search"],
input[type="date"],
input[type="time"],
input[type="datetime-local"],
input[type="month"],
input[type="week"],
select,
textarea,
.input,
.textInput {
  background: var(--input-bg-light);
  color: var(--input-color-light);
  border: var(--input-border);
  padding: var(--input-padding);
  border-radius: var(--input-radius);
  font-size: 1rem;
  font-weight: 400;
  transition: border 0.2s ease;
  line-height: 1.5;
  width: 100%;
}

/* Standardized Form Input Class - Unified Styling */
.formInput {
  width: 100%;
  padding: var(--input-padding);
  border: var(--input-border);
  border-radius: var(--input-radius);
  font-size: 1rem;
  background: var(--input-bg-light);
  color: var(--input-color-light);
  transition: border 0.2s ease;
  font-family: inherit;
  line-height: 1.5;
  font-weight: 400;
}

[data-theme="light"] .formInput {
  border: var(--input-border-light);
  box-sizing: border-box;
  outline: none;
}

[data-theme="light"] .formInput:focus {
  border: var(--input-focus-border-light);
  box-sizing: border-box;
  outline: none;
  box-shadow: none;
}

[data-theme="dark"] .formInput {
  background: var(--input-bg-dark);
  color: var(--input-color-dark);
}

.formInput:focus {
  outline: none;
  border: var(--input-border);
  box-shadow: none;
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type="number"] {
  -moz-appearance: textfield;
}

input[type="text"]:hover,
input[type="email"]:hover,
input[type="password"]:hover,
input[type="number"]:hover,
input[type="tel"]:hover,
input[type="url"]:hover,
input[type="search"]:hover,
input[type="date"]:hover,
input[type="time"]:hover,
input[type="datetime-local"]:hover,
input[type="month"]:hover,
input[type="week"]:hover,
select:hover,
textarea:hover,
.input:hover,
.textInput:hover {
  border-color: var(--border-secondary);
}

.formInput:hover {
  border-color: var(--border-secondary);
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
input[type="number"]:focus,
input[type="tel"]:focus,
input[type="url"]:focus,
input[type="search"]:focus,
input[type="date"]:focus,
input[type="time"]:focus,
input[type="datetime-local"]:focus,
input[type="month"]:focus,
input[type="week"]:focus,
select:focus,
textarea:focus,
.input:focus,
.textInput:focus {
  outline: none;
  border-color: var(--accent-primary, rgba(99, 179, 237, 0.9));
}

.formInput:focus {
  outline: none;
  border-color: var(--accent-primary, rgba(99, 179, 237, 0.9));
}

/* Light mode specific styling - Neutral Default, Red Focus */
[data-theme="light"] input[type="text"],
[data-theme="light"] input[type="email"],
[data-theme="light"] input[type="password"],
[data-theme="light"] input[type="number"],
[data-theme="light"] input[type="tel"],
[data-theme="light"] input[type="url"],
[data-theme="light"] input[type="search"],
[data-theme="light"] input[type="date"],
[data-theme="light"] input[type="time"],
[data-theme="light"] input[type="datetime-local"],
[data-theme="light"] input[type="month"],
[data-theme="light"] input[type="week"],
[data-theme="light"] select,
[data-theme="light"] textarea,
[data-theme="light"] .input,
[data-theme="light"] .textInput {
  border: var(--input-border-light);
  transition: border-color 0.2s ease;
  box-sizing: border-box;
  outline: none;
}

[data-theme="light"] input[type="text"]:focus,
[data-theme="light"] input[type="email"]:focus,
[data-theme="light"] input[type="password"]:focus,
[data-theme="light"] input[type="number"]:focus,
[data-theme="light"] input[type="tel"]:focus,
[data-theme="light"] input[type="url"]:focus,
[data-theme="light"] input[type="search"]:focus,
[data-theme="light"] input[type="date"]:focus,
[data-theme="light"] input[type="time"]:focus,
[data-theme="light"] input[type="datetime-local"]:focus,
[data-theme="light"] input[type="month"]:focus,
[data-theme="light"] input[type="week"]:focus,
[data-theme="light"] select:focus,
[data-theme="light"] textarea:focus,
[data-theme="light"] .input:focus,
[data-theme="light"] .textInput:focus {
  border: var(--input-focus-border-light);
  box-sizing: border-box;
  outline: none;
}



/* Dark mode specific styling - Gradient Background Input Fields */
[data-theme="dark"] input[type="text"],
[data-theme="dark"] input[type="email"],
[data-theme="dark"] input[type="password"],
[data-theme="dark"] input[type="number"],
[data-theme="dark"] input[type="tel"],
[data-theme="dark"] input[type="url"],
[data-theme="dark"] input[type="search"],
[data-theme="dark"] input[type="date"],
[data-theme="dark"] input[type="time"],
[data-theme="dark"] input[type="datetime-local"],
[data-theme="dark"] input[type="month"],
[data-theme="dark"] input[type="week"],
[data-theme="dark"] select,
[data-theme="dark"] textarea,
[data-theme="dark"] .input,
[data-theme="dark"] .textInput {
  background: linear-gradient(to bottom right, #0d1117, #161b22);
  border: 1px solid rgba(226, 232, 240, 0.3);
  border-radius: 10px;
  color: #e2e8f0;
  padding: 10px;
  transition: border 0.2s ease-in-out;
}

/* Dark mode gradient backgrounds for main containers and dropdowns */
[data-theme="dark"] .dropdown-menu,
[data-theme="dark"] .user-dropdown,
[data-theme="dark"] .mobile-dropdown,
[data-theme="dark"] .language-dropdown {
  background: linear-gradient(to bottom right, #0d1117, #161b22) !important;
}

/* Ensure navbar dropdowns maintain gradient in dark mode */
[data-theme="dark"] .navbar .dropdown-menu {
  background: linear-gradient(135deg, #0d1117 0%, #161b22 100%) !important;
  backdrop-filter: blur(15px);
}

/* Dark mode gradient for form containers */
[data-theme="dark"] .form-container,
[data-theme="dark"] .admin-container,
[data-theme="dark"] .page-container {
  background: linear-gradient(to bottom right, #0d1117, #161b22);
}

[data-theme="dark"] .formInput {
  background: linear-gradient(to bottom right, #0d1117, #161b22);
  border: 1px solid rgba(226, 232, 240, 0.2);
  border-radius: 10px;
  color: #e2e8f0;
  padding: 10px;
  transition: border 0.2s ease-in-out;
}

[data-theme="dark"] input[type="text"]:hover,
[data-theme="dark"] input[type="email"]:hover,
[data-theme="dark"] input[type="password"]:hover,
[data-theme="dark"] input[type="number"]:hover,
[data-theme="dark"] input[type="tel"]:hover,
[data-theme="dark"] input[type="url"]:hover,
[data-theme="dark"] input[type="search"]:hover,
[data-theme="dark"] input[type="date"]:hover,
[data-theme="dark"] input[type="time"]:hover,
[data-theme="dark"] input[type="datetime-local"]:hover,
[data-theme="dark"] input[type="month"]:hover,
[data-theme="dark"] input[type="week"]:hover,
[data-theme="dark"] select:hover,
[data-theme="dark"] textarea:hover,
[data-theme="dark"] .input:hover,
[data-theme="dark"] .textInput:hover {
  border-color: #f0f0f0;
}

[data-theme="dark"] .formInput:hover {
  border-color: #f0f0f0;
}

[data-theme="dark"] input[type="text"]:focus,
[data-theme="dark"] input[type="email"]:focus,
[data-theme="dark"] input[type="password"]:focus,
[data-theme="dark"] input[type="number"]:focus,
[data-theme="dark"] input[type="tel"]:focus,
[data-theme="dark"] input[type="url"]:focus,
[data-theme="dark"] input[type="search"]:focus,
[data-theme="dark"] input[type="date"]:focus,
[data-theme="dark"] input[type="time"]:focus,
[data-theme="dark"] input[type="datetime-local"]:focus,
[data-theme="dark"] input[type="month"]:focus,
[data-theme="dark"] input[type="week"]:focus,
[data-theme="dark"] select:focus,
[data-theme="dark"] textarea:focus,
[data-theme="dark"] .input:focus,
[data-theme="dark"] .textInput:focus {
  outline: none;
  border-color: var(--accent-primary, rgba(99, 179, 237, 0.9));
}

[data-theme="dark"] .formInput:focus {
  outline: none;
  border-color: var(--accent-primary, rgba(99, 179, 237, 0.9));
}

[data-theme="dark"] select option {
  background: #161b22 !important;
  color: #e2e8f0 !important;
}

/* Additional option styling for better browser compatibility */
[data-theme="dark"] option {
  background: #161b22 !important;
  color: #e2e8f0 !important;
}

/* Placeholder styling */
input::placeholder,
textarea::placeholder {
  color: var(--text-tertiary);
}

[data-theme="dark"] input::placeholder,
[data-theme="dark"] textarea::placeholder {
  color: #aaa;
}



/* Dark mode scrollbar styling */
[data-theme="dark"] ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

[data-theme="dark"] ::-webkit-scrollbar-track {
  background: #1a1a1a;
  border-radius: 4px;
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
  background: #555;
  border-radius: 4px;
  transition: background 0.2s ease;
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
  background: #333;
}

[data-theme="dark"] ::-webkit-scrollbar-corner {
  background: #1a1a1a;
}

/* Edit vehicle page styles */
.edit-vehicle-container {
  padding: 2rem;
  background: var(--bg-primary);
  color: var(--text-primary);
}

.edit-vehicle-container h2 {
  color: var(--text-primary);
  margin-bottom: 1.5rem;
}

.edit-vehicle-container h3 {
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
  font-weight: 500;
}

.form-group .input {
  display: block;
  width: 100%;
}

.features-section {
  margin-bottom: 2rem;
}

/* RTL Language Support */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] .navbar {
  direction: rtl;
}

[dir="rtl"] .dropdown-menu {
  right: 0;
  left: auto;
}

[dir="rtl"] .sidebar {
  right: 0;
  left: auto;
}

[dir="rtl"] .formInput {
  text-align: right;
}

[dir="rtl"] .button {
  direction: rtl;
}

[dir="rtl"] .card {
  direction: rtl;
}

[dir="rtl"] .flex {
  direction: rtl;
}

/* Language-specific font families */
[lang="ar"], [lang="ar"] * {
  font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
  direction: rtl;
  text-align: right;
}

[lang="ur"], [lang="ur"] * {
  font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
  direction: rtl;
  text-align: right;
}

/* Ensure proper spacing for RTL languages */
[dir="rtl"] .gap-2 > * + * {
  margin-right: 0.5rem;
  margin-left: 0;
}

[dir="rtl"] .gap-4 > * + * {
  margin-right: 1rem;
  margin-left: 0;
}

