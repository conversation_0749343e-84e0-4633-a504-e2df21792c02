// Google Analytics 4 utility functions
// Import these functions in your components to track events

const GA_MEASUREMENT_ID = 'G-JBNQD8SDQ1';

// Track page views
export function trackPageView(url, title) {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('config', GA_MEASUREMENT_ID, {
      page_title: title,
      page_location: url,
    });
  }
}

// Generic event tracking
export function gtag(...args) {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag(...args);
  }
}

// Track custom events with enhanced parameters
export function trackEvent(eventName, parameters = {}) {
  gtag('event', eventName, {
    event_category: parameters.category || 'engagement',
    event_label: parameters.label,
    value: parameters.value,
    custom_parameter_1: parameters.custom1,
    custom_parameter_2: parameters.custom2,
    ...parameters,
  });
}

// E-commerce and vehicle-specific tracking
export function trackVehicleView(vehicleData) {
  gtag('event', 'view_item', {
    currency: 'CAD',
    value: vehicleData.price,
    items: [{
      item_id: vehicleData.id,
      item_name: `${vehicleData.year} ${vehicleData.make} ${vehicleData.model}`,
      item_category: 'vehicle',
      item_brand: vehicleData.make,
      price: vehicleData.price,
      quantity: 1
    }],
    // Enhanced vehicle data properties
    vehicle_id: vehicleData.id,
    vehicle_make: vehicleData.make,
    vehicle_model: vehicleData.model,
    vehicle_year: vehicleData.year,
    dealer_id: vehicleData.dealerId || null,
    event_category: 'vehicles'
  });
}

export function trackVehicleInterest(vehicleData, action = 'contact') {
  gtag('event', 'generate_lead', {
    currency: 'CAD',
    value: vehicleData.price,
    items: [{
      item_id: vehicleData.id,
      item_name: `${vehicleData.year} ${vehicleData.make} ${vehicleData.model}`,
      item_category: 'vehicle',
      item_brand: vehicleData.make,
      price: vehicleData.price,
      quantity: 1
    }],
    // Enhanced vehicle data properties
    vehicle_id: vehicleData.id,
    vehicle_make: vehicleData.make,
    vehicle_model: vehicleData.model,
    vehicle_year: vehicleData.year,
    dealer_id: vehicleData.dealerId || null,
    lead_type: action,
    event_category: 'lead_generation'
  });
}

// Enhanced search tracking with results count
export function trackSearch(searchTerm, filters = {}, resultsCount = null) {
  gtag('event', 'search', {
    search_term: searchTerm,
    search_filters: Object.keys(filters).length > 0 ? JSON.stringify(filters) : null,
    results_count: resultsCount,
    filter_count: Object.keys(filters).length,
    event_category: 'search',
    event_label: searchTerm
  });
}

// Enhanced contact form tracking with vehicle context
export function trackContactForm(formType, vehicleId = null, vehicleData = null) {
  const eventData = {
    form_type: formType,
    event_category: 'lead_generation',
    event_label: formType
  };

  // Add vehicle context if provided
  if (vehicleId) {
    eventData.vehicle_id = vehicleId;
  }

  if (vehicleData) {
    eventData.vehicle_make = vehicleData.make;
    eventData.vehicle_model = vehicleData.model;
    eventData.vehicle_year = vehicleData.year;
    eventData.dealer_id = vehicleData.dealerId || null;
    eventData.vehicle_price = vehicleData.price;
  }

  gtag('event', 'form_submit', eventData);
}

// Test drive interest tracking
export function trackTestDrive(vehicleId, vehicleMake, vehicleModel, vehicleData = null) {
  const eventData = {
    event_category: 'lead_generation',
    event_label: 'test_drive_request',
    vehicle_id: vehicleId,
    vehicle_make: vehicleMake,
    vehicle_model: vehicleModel,
    lead_type: 'test_drive'
  };

  // Add additional vehicle context if provided
  if (vehicleData) {
    eventData.vehicle_year = vehicleData.year;
    eventData.dealer_id = vehicleData.dealerId || null;
    eventData.vehicle_price = vehicleData.price;
    eventData.value = vehicleData.price; // For conversion value tracking
    eventData.currency = 'CAD';
  }

  gtag('event', 'generate_lead', eventData);
}

// Click-to-call and contact method tracking
export function trackContactClick(method, location, vehicleId = null, vehicleData = null) {
  const eventData = {
    event_category: 'contact_interaction',
    event_label: `${method}_click`,
    contact_method: method, // 'phone', 'whatsapp', 'messenger', 'email'
    click_location: location, // 'vehicle_detail_page', 'header', 'footer', etc.
    event_action: 'click'
  };

  // Add vehicle context if provided
  if (vehicleId) {
    eventData.vehicle_id = vehicleId;
  }

  if (vehicleData) {
    eventData.vehicle_make = vehicleData.make;
    eventData.vehicle_model = vehicleData.model;
    eventData.vehicle_year = vehicleData.year;
    eventData.dealer_id = vehicleData.dealerId || null;
  }

  gtag('event', 'contact_click', eventData);
}

// Navigation and engagement tracking
export function trackNavigation(destination, source = 'menu') {
  gtag('event', 'page_navigation', {
    destination: destination,
    source: source,
    event_category: 'navigation'
  });
}

export function trackDownload(fileName, fileType) {
  gtag('event', 'file_download', {
    file_name: fileName,
    file_type: fileType,
    event_category: 'downloads'
  });
}

export function trackOutboundLink(url, linkText) {
  gtag('event', 'click', {
    event_category: 'outbound',
    event_label: url,
    transport_type: 'beacon',
    event_callback: function() {
      // Optional: Add callback logic here
    }
  });
}

// User engagement tracking
export function trackUserEngagement(action, details = {}) {
  gtag('event', 'engagement', {
    engagement_time_msec: details.timeSpent || 0,
    action: action,
    event_category: 'user_engagement',
    ...details
  });
}

// Admin/dealer specific tracking
export function trackAdminAction(action, details = {}) {
  gtag('event', 'admin_action', {
    action: action,
    event_category: 'admin',
    ...details
  });
}

// Conversion tracking
export function trackConversion(conversionType, value = 0) {
  gtag('event', 'conversion', {
    send_to: GA_MEASUREMENT_ID,
    conversion_type: conversionType,
    value: value,
    currency: 'CAD'
  });
}

// Enhanced vehicle engagement tracking
export function trackVehicleEngagement(action, vehicleData, additionalData = {}) {
  const eventData = {
    event_category: 'vehicle_engagement',
    event_action: action, // 'image_view', 'video_play', 'feature_expand', etc.
    vehicle_id: vehicleData.id,
    vehicle_make: vehicleData.make,
    vehicle_model: vehicleData.model,
    vehicle_year: vehicleData.year,
    dealer_id: vehicleData.dealerId || null,
    ...additionalData
  };

  gtag('event', 'vehicle_engagement', eventData);
}

// Financing interest tracking
export function trackFinancingInterest(vehicleData, financingType = 'general') {
  gtag('event', 'financing_interest', {
    event_category: 'lead_generation',
    event_label: 'financing_inquiry',
    vehicle_id: vehicleData.id,
    vehicle_make: vehicleData.make,
    vehicle_model: vehicleData.model,
    vehicle_year: vehicleData.year,
    vehicle_price: vehicleData.price,
    financing_type: financingType, // 'general', 'calculator', 'pre_approval'
    dealer_id: vehicleData.dealerId || null,
    value: vehicleData.price,
    currency: 'CAD'
  });
}

// Inventory browsing patterns
export function trackInventoryBrowsing(action, filters = {}, resultsCount = null) {
  gtag('event', 'inventory_browse', {
    event_category: 'inventory',
    event_action: action, // 'filter_applied', 'sort_changed', 'page_changed'
    active_filters: Object.keys(filters).length > 0 ? JSON.stringify(filters) : null,
    filter_count: Object.keys(filters).length,
    results_count: resultsCount
  });
}

// Social sharing tracking
export function trackSocialShare(platform, vehicleData = null, contentType = 'vehicle') {
  const eventData = {
    event_category: 'social_sharing',
    event_action: 'share',
    platform: platform, // 'facebook', 'twitter', 'whatsapp', etc.
    content_type: contentType
  };

  if (vehicleData) {
    eventData.vehicle_id = vehicleData.id;
    eventData.vehicle_make = vehicleData.make;
    eventData.vehicle_model = vehicleData.model;
    eventData.shared_item = `${vehicleData.year} ${vehicleData.make} ${vehicleData.model}`;
  }

  gtag('event', 'share', eventData);
}
