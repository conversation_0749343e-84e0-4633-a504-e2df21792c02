#!/usr/bin/env node

/**
 * Initialize Invoice Counters Script
 * Initializes both test and production counters for Bill of Sale invoice numbering
 * 
 * Usage:
 *   node scripts/init-counters.js
 */

import dotenv from 'dotenv';
import { initializeCounters, getCounterStatus } from '../apps/frontend/src/lib/utils/invoiceGenerator.js';

// Load environment variables from frontend directory
dotenv.config({ path: '../apps/frontend/.env.local' });

async function initCounters() {
  try {
    console.log('🔢 Initializing invoice counters...');
    
    // Initialize both test and prod counters
    await initializeCounters();
    
    // Get status of both counters
    console.log('\n📊 Counter Status:');
    
    const testStatus = await getCounterStatus('test');
    console.log(`   Test Mode: ${testStatus.nextInvoiceNumber} (Current: ${testStatus.currentSequence})`);
    
    const prodStatus = await getCounterStatus('prod');
    console.log(`   Prod Mode: ${prodStatus.nextInvoiceNumber} (Current: ${prodStatus.currentSequence})`);
    
    console.log('\n✅ Invoice counters initialized successfully!');
    console.log('\n📝 Usage:');
    console.log('   - Test Mode: Used for development and internal testing');
    console.log('   - Prod Mode: Used for live sales after dealer license activation');
    console.log('\n🎯 Next Steps:');
    console.log('   1. Use Test Mode during development');
    console.log('   2. Switch to Prod Mode when ready for live sales');
    console.log('   3. Invoice numbers will auto-increment: BOS-YYYY-#####');
    
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Error initializing counters:', error);
    process.exit(1);
  }
}

// Run the initialization
initCounters();
