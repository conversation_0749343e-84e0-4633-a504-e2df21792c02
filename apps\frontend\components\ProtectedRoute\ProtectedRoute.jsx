'use client';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import styles from './ProtectedRoute.module.css';

// Helper function to validate and fix user data
function validateAndFixUserData(userData) {
  if (!userData) return null;

  // Create a clean user object with required fields
  const cleanUser = {
    email: userData.email || '',
    role: userData.role || '',
    name: userData.name || userData.fullName || '',
    id: userData.id || userData._id || '',
  };

  // Validate required fields
  if (!cleanUser.email || !cleanUser.email.includes('@')) {
    console.warn('Invalid email in user data');
    return null;
  }

  // Normalize role to lowercase
  cleanUser.role = cleanUser.role.toLowerCase();

  // Ensure role is valid
  if (!['admin', 'dealer', 'salesman', 'user'].includes(cleanUser.role)) {
    console.warn(`Invalid role: ${cleanUser.role}`);
    return null;
  }

  return cleanUser;
}

export default function ProtectedRoute({ children, requiredRole = null }) {
  const router = useRouter();
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState(null);

  useEffect(() => {
    const checkAuth = () => {
      try {
        console.log('🔍 ProtectedRoute: Checking authentication...');
        // Check if user is stored in localStorage
        const storedUser = localStorage.getItem('user');

        if (!storedUser) {
          console.log('❌ ProtectedRoute: No user found in localStorage');
          // No user found, redirect to login
          router.push('/login');
          return;
        }

        const rawUserData = JSON.parse(storedUser);
        console.log('👤 ProtectedRoute: Raw user data:', { email: rawUserData?.email, role: rawUserData?.role });

        // Validate and clean user data
        const userData = validateAndFixUserData(rawUserData);
        if (!userData) {
          console.log('❌ ProtectedRoute: Invalid user data structure after validation');
          // Invalid user data, clear storage and redirect
          localStorage.removeItem('user');
          router.push('/login');
          return;
        }

        // Update localStorage with clean data
        localStorage.setItem('user', JSON.stringify(userData));
        console.log('✅ ProtectedRoute: Cleaned user data:', { email: userData.email, role: userData.role });

        // Check if user has required role (if specified)
        if (requiredRole) {
          const hasRequiredRole =
            userData.role === requiredRole ||
            userData.role === 'admin' ||
            (requiredRole === 'dealer' && (userData.role === 'dealer' || userData.role === 'admin' || userData.role === 'salesman'));

          if (!hasRequiredRole) {
            console.log(`❌ ProtectedRoute: User role '${userData.role}' does not have access. Required: '${requiredRole}'`);
            // User doesn't have required role, redirect to unauthorized page
            router.push('/unauthorized');
            return;
          }
        }

        console.log('✅ ProtectedRoute: Authentication successful');
        // User is authenticated and authorized
        setUser(userData);
        setIsAuthenticated(true);
      } catch (error) {
        console.error('❌ ProtectedRoute: Auth check error:', error);
        // Clear invalid data and redirect
        localStorage.removeItem('user');
        router.push('/login');
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, [router, requiredRole]);

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.spinner}></div>
        <p>Verifying authentication...</p>
        <small style={{ marginTop: '10px', color: '#666' }}>
          If this takes too long, try refreshing the page or logging in again.
        </small>
      </div>
    );
  }

  // Show nothing if not authenticated (will redirect)
  if (!isAuthenticated) {
    return null;
  }

  // Render protected content if authenticated
  return (
    <div className={styles.protectedContent}>
      {children}
    </div>
  );
}
