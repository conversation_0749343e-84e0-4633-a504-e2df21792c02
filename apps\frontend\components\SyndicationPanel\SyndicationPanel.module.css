.syndicationPanel {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  transition: background 0.3s ease, border-color 0.3s ease;
}

/* Dark mode styling for syndication panel */
[data-theme="dark"] .syndicationPanel {
  background: #0d1117;
  border: 1px solid #333;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #f1f3f4;
}

.header h3 {
  margin: 0;
  color: #2d3748;
  font-size: 1.5rem;
  font-weight: 600;
  transition: color 0.3s ease;
}

/* Dark mode styling for header text */
[data-theme="dark"] .header h3 {
  color: #ffffff;
}

.selectedCount {
  background: #e53e3e;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

.section {
  margin-bottom: 2rem;
}

.section h4 {
  margin: 0 0 1rem 0;
  color: #4a5568;
  font-size: 1.125rem;
  font-weight: 600;
  transition: color 0.3s ease;
}

/* Dark mode styling for section headers */
[data-theme="dark"] .section h4 {
  color: #cbd5e1;
}

/* Platform Selection */
.platformGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.platformCard {
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  padding: 1rem;
  transition: all 0.2s ease;
  cursor: pointer;
}

/* Dark mode styling for platform cards */
[data-theme="dark"] .platformCard {
  border: 2px solid #333;
  background: #161b22;
}

.platformCard:hover {
  border-color: #e53e3e;
  box-shadow: 0 2px 8px rgba(229, 62, 62, 0.1);
}

.platformCard.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: #f7fafc;
}

.platformLabel {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  cursor: pointer;
  width: 100%;
}

.platformLabel input[type="checkbox"] {
  margin-top: 0.25rem;
  width: 18px;
  height: 18px;
  accent-color: #e53e3e;
}

.platformInfo {
  flex: 1;
}

.platformName {
  display: block;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.25rem;
  transition: color 0.3s ease;
}

.platformDesc {
  display: block;
  font-size: 0.875rem;
  color: #718096;
  line-height: 1.4;
  transition: color 0.3s ease;
}

/* Dark mode styling for platform text */
[data-theme="dark"] .platformName {
  color: #ffffff;
}

[data-theme="dark"] .platformDesc {
  color: #cbd5e1;
}

.platformStatus {
  display: block;
  font-size: 0.75rem;
  color: #e53e3e;
  font-weight: 500;
  margin-top: 0.25rem;
}

/* Actions */
.actions {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: center;
  padding: 1.5rem;
  background: #f7fafc;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  transition: background 0.3s ease;
}

/* Dark mode styling for actions */
[data-theme="dark"] .actions {
  background: #161b22;
}

.primaryBtn {
  background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.primaryBtn:hover:not(:disabled) {
  background: linear-gradient(135deg, #c53030 0%, #9b2c2c 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(229, 62, 62, 0.3);
}

.primaryBtn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.secondaryBtn {
  background: #4a5568;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.secondaryBtn:hover {
  background: #2d3748;
  transform: translateY(-1px);
}

.dangerBtn {
  background: #e53e3e;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.dangerBtn:hover:not(:disabled) {
  background: #c53030;
  transform: translateY(-1px);
}

.dangerBtn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.exportGroup {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-left: auto;
}

.exportGroup span {
  font-weight: 500;
  color: #4a5568;
  margin-right: 0.5rem;
}

/* Status Display */
.loading {
  text-align: center;
  padding: 2rem;
  color: #718096;
  font-style: italic;
}

.statusGrid {
  display: grid;
  gap: 1rem;
}

.vehicleStatus {
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1rem;
}

.vehicleId {
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.75rem;
  font-size: 0.875rem;
}

.platformStatuses {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.5rem;
}

.platformStatus {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background: white;
  border-radius: 4px;
  font-size: 0.875rem;
}

.platformStatus .platformName {
  font-weight: 500;
  color: #4a5568;
}

.status {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.status.success {
  background: #c6f6d5;
  color: #22543d;
}

.status.pending {
  background: #fef5e7;
  color: #c05621;
}

.status.failed {
  background: #fed7d7;
  color: #c53030;
}

.status.none {
  background: #edf2f7;
  color: #718096;
}

/* Results */
.results {
  background: #f7fafc;
  border-radius: 8px;
  padding: 1.5rem;
  border-left: 4px solid #e53e3e;
}

.results h4 {
  margin: 0 0 1rem 0;
  color: #2d3748;
}

.successResults .summary {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.successResults .summary span {
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
}

.successResults .summary .success {
  background: #c6f6d5;
  color: #22543d;
}

.successResults .summary .error {
  background: #fed7d7;
  color: #c53030;
}

.resultDetails {
  display: grid;
  gap: 0.5rem;
}

.resultItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: white;
  border-radius: 6px;
  font-size: 0.875rem;
}

.resultItem .success {
  color: #22543d;
  font-weight: 600;
}

.resultItem .error {
  color: #c53030;
  font-weight: 600;
}

.errorResults {
  color: #c53030;
  font-weight: 600;
  padding: 1rem;
  background: #fed7d7;
  border-radius: 6px;
}

/* Responsive */
@media (max-width: 768px) {
  .syndicationPanel {
    padding: 1rem;
  }
  
  .header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
  
  .platformGrid {
    grid-template-columns: 1fr;
  }
  
  .actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .exportGroup {
    margin-left: 0;
    justify-content: center;
  }
  
  .platformStatuses {
    grid-template-columns: 1fr;
  }
}
