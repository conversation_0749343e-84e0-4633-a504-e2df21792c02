import { S3Client, PutObjectCommand, DeleteObjectCommand, GetObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { v4 as uuidv4 } from 'uuid';

const s3 = new S3Client({
  region: process.env.CUSTOM_AWS_REGION,
  credentials: {
    accessKeyId: process.env.CUSTOM_AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.CUSTOM_AWS_SECRET_ACCESS_KEY,
  },
});

const BUCKET_NAME = 'fazenauto-ledger-documents';

/**
 * Generate S3 path for ledger receipts
 * @param {string} category - Transaction category
 * @param {Date} date - Transaction date
 * @param {string} transactionId - Transaction ID
 * @returns {string} S3 path
 */
export function generateS3Path(category, date, transactionId) {
  const transactionDate = new Date(date);
  const year = transactionDate.getFullYear();
  const month = String(transactionDate.getMonth() + 1).padStart(2, '0');
  
  return `receipts/${category}/${year}-${month}/${transactionId}/`;
}

/**
 * Upload receipt files to S3
 * @param {File[]} files - Array of files to upload
 * @param {string} s3Path - S3 path prefix
 * @param {string} addedBy - User who added the transaction
 * @param {string} transactionId - Transaction ID
 * @returns {Promise<string[]>} Array of uploaded file URLs
 */
export async function uploadReceiptFiles(files, s3Path, addedBy, transactionId) {
  const receiptUrls = [];
  const allowedExtensions = ['pdf', 'jpg', 'jpeg', 'png'];
  const maxFileSize = 10 * 1024 * 1024; // 10MB

  for (const file of files) {
    if (file.size === 0) continue;

    // Validate file size
    if (file.size > maxFileSize) {
      throw new Error(`File ${file.name} is too large. Maximum size is 10MB.`);
    }

    // Validate file extension
    const fileExtension = file.name.split('.').pop().toLowerCase();
    if (!allowedExtensions.includes(fileExtension)) {
      throw new Error(`File ${file.name} has unsupported format. Allowed: ${allowedExtensions.join(', ')}`);
    }

    // Generate unique filename
    const fileName = `${uuidv4()}.${fileExtension}`;
    const s3Key = `${s3Path}${fileName}`;
    
    // Convert file to buffer
    const buffer = Buffer.from(await file.arrayBuffer());
    
    // Upload parameters
    const uploadParams = {
      Bucket: BUCKET_NAME,
      Key: s3Key,
      Body: buffer,
      ContentType: file.type,
      ServerSideEncryption: 'AES256',
      Metadata: {
        originalName: file.name,
        uploadedBy: addedBy,
        transactionId: transactionId,
        uploadDate: new Date().toISOString(),
        fileSize: file.size.toString()
      }
    };

    try {
      await s3.send(new PutObjectCommand(uploadParams));
      receiptUrls.push(`https://${BUCKET_NAME}.s3.${process.env.CUSTOM_AWS_REGION}.amazonaws.com/${s3Key}`);
    } catch (error) {
      console.error(`Error uploading file ${file.name}:`, error);
      throw new Error(`Failed to upload file ${file.name}`);
    }
  }

  return receiptUrls;
}

/**
 * Create and upload metadata.json file
 * @param {Object} transactionData - Transaction data
 * @param {string} s3Path - S3 path prefix
 * @param {string[]} receiptUrls - Array of receipt URLs
 * @returns {Promise<void>}
 */
export async function uploadMetadataFile(transactionData, s3Path, receiptUrls) {
  const metadata = {
    vehicleId: transactionData.vehicleId || null,
    date: new Date(transactionData.date).toISOString().split('T')[0],
    type: transactionData.type,
    description: transactionData.description,
    amount: transactionData.amount,
    currency: transactionData.currency,
    receiptFiles: receiptUrls.map(url => url.split('/').pop()),
    addedBy: transactionData.addedBy,
    notes: transactionData.notes || '',
    createdAt: new Date().toISOString(),
    transactionId: transactionData._id || transactionData.transactionId
  };

  const metadataBuffer = Buffer.from(JSON.stringify(metadata, null, 2));
  const metadataParams = {
    Bucket: BUCKET_NAME,
    Key: `${s3Path}metadata.json`,
    Body: metadataBuffer,
    ContentType: 'application/json',
    ServerSideEncryption: 'AES256',
    Metadata: {
      transactionId: transactionData._id || transactionData.transactionId,
      createdAt: new Date().toISOString()
    }
  };

  try {
    await s3.send(new PutObjectCommand(metadataParams));
  } catch (error) {
    console.error('Error uploading metadata file:', error);
    throw new Error('Failed to upload metadata file');
  }
}

/**
 * Generate signed URL for receipt download
 * @param {string} s3Key - S3 key of the file
 * @param {number} expiresIn - URL expiration time in seconds (default: 1 hour)
 * @returns {Promise<string>} Signed URL
 */
export async function generateSignedUrl(s3Key, expiresIn = 3600) {
  const command = new GetObjectCommand({
    Bucket: BUCKET_NAME,
    Key: s3Key,
  });

  try {
    return await getSignedUrl(s3, command, { expiresIn });
  } catch (error) {
    console.error('Error generating signed URL:', error);
    throw new Error('Failed to generate download link');
  }
}

/**
 * Delete receipt files from S3
 * @param {string[]} receiptUrls - Array of receipt URLs to delete
 * @param {string} s3Path - S3 path prefix
 * @returns {Promise<void>}
 */
export async function deleteReceiptFiles(receiptUrls, s3Path) {
  const deletePromises = [];

  // Delete receipt files
  for (const receiptUrl of receiptUrls) {
    const urlParts = receiptUrl.split('/');
    const bucketIndex = urlParts.findIndex(part => part.includes(BUCKET_NAME));
    const s3Key = urlParts.slice(bucketIndex + 1).join('/');

    const deleteCommand = new DeleteObjectCommand({
      Bucket: BUCKET_NAME,
      Key: s3Key,
    });

    deletePromises.push(s3.send(deleteCommand));
  }

  // Delete metadata.json
  const metadataDeleteCommand = new DeleteObjectCommand({
    Bucket: BUCKET_NAME,
    Key: `${s3Path}metadata.json`,
  });

  deletePromises.push(s3.send(metadataDeleteCommand));

  try {
    await Promise.all(deletePromises);
  } catch (error) {
    console.error('Error deleting S3 files:', error);
    throw new Error('Failed to delete some files from S3');
  }
}

/**
 * Extract S3 key from URL
 * @param {string} url - S3 URL
 * @returns {string} S3 key
 */
export function extractS3KeyFromUrl(url) {
  const urlParts = url.split('/');
  const bucketIndex = urlParts.findIndex(part => part.includes(BUCKET_NAME));
  return urlParts.slice(bucketIndex + 1).join('/');
}

/**
 * Validate file for upload
 * @param {File} file - File to validate
 * @returns {Object} Validation result
 */
export function validateFile(file) {
  const allowedExtensions = ['pdf', 'jpg', 'jpeg', 'png'];
  const maxFileSize = 10 * 1024 * 1024; // 10MB

  if (file.size === 0) {
    return { valid: false, error: 'File is empty' };
  }

  if (file.size > maxFileSize) {
    return { valid: false, error: 'File is too large. Maximum size is 10MB.' };
  }

  const fileExtension = file.name.split('.').pop().toLowerCase();
  if (!allowedExtensions.includes(fileExtension)) {
    return { valid: false, error: `Unsupported file format. Allowed: ${allowedExtensions.join(', ')}` };
  }

  return { valid: true };
}
