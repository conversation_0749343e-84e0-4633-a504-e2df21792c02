/* The Team Page Styles */

.teamPage {
  min-height: 100vh;
  background: var(--bg-primary);
  padding: 2rem 1rem;
  transition: background 0.3s ease;
}

/* Dark mode background */
[data-theme="dark"] .teamPage {
  background: linear-gradient(to bottom right, #0d1117, #161b22);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  text-align: center;
  margin-bottom: 3rem;
  padding-top: 2rem;
}

.title {
  font-size: 3rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 1rem;
  transition: color 0.3s ease;
}

.content {
  display: flex;
  flex-direction: column;
  gap: 3rem;
}

.section {
  background: var(--bg-secondary);
  border-radius: 12px;
  padding: 2rem;
  box-shadow: var(--card-shadow);
  border: 1px solid var(--border-primary);
  transition: all 0.3s ease;
}

/* Dark mode section styling */
[data-theme="dark"] .section {
  background: rgba(22, 27, 34, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.sectionTitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 2rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e53e3e;
  transition: color 0.3s ease;
}

.teamGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.memberCard {
  background: var(--bg-primary);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--border-primary);
  transition: all 0.3s ease;
}

/* Dark mode member card styling */
[data-theme="dark"] .memberCard {
  background: rgba(13, 17, 23, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.memberCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.memberPhoto {
  width: 100%;
  height: 200px;
  background: #f7fafc;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

/* Dark mode photo background */
[data-theme="dark"] .memberPhoto {
  background: rgba(45, 55, 72, 0.5);
}

.photoImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.placeholderPhoto {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e0 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Dark mode placeholder */
[data-theme="dark"] .placeholderPhoto {
  background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
}

.userIcon {
  font-size: 4rem;
  color: #a0aec0;
}

/* Dark mode user icon */
[data-theme="dark"] .userIcon {
  color: #718096;
}

.memberInfo {
  padding: 1.5rem;
  background: #2d3748;
  color: white;
  text-align: left;
}

.memberName {
  font-size: 1.1rem;
  font-weight: 600;
  color: white;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.memberPosition {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.memberDescription {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 0.75rem;
  font-style: italic;
  line-height: 1.4;
}

.memberEmail {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 0.25rem;
  word-break: break-word;
}

.memberPhone {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .title {
    font-size: 2.5rem;
  }
  
  .section {
    padding: 1.5rem;
  }
  
  .teamGrid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
  }
  
  .sectionTitle {
    font-size: 1.3rem;
  }
}

@media (max-width: 480px) {
  .teamPage {
    padding: 1rem 0.5rem;
  }
  
  .title {
    font-size: 2rem;
  }
  
  .section {
    padding: 1rem;
  }
  
  .teamGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .memberPhoto {
    height: 150px;
  }
  
  .memberInfo {
    padding: 1rem;
  }
  
  .userIcon {
    font-size: 3rem;
  }
}

/* RTL Support */
[dir="rtl"] .teamPage {
  direction: rtl;
}

[dir="rtl"] .memberInfo {
  text-align: right;
}

[dir="rtl"] .sectionTitle {
  text-align: right;
}
