import mongoose, { Schema } from 'mongoose';

/**
 * Counter Schema for generating sequential invoice numbers
 * Used for Bill of Sale form numbering system
 */
const counterSchema = new Schema({
  _id: { 
    type: String, 
    required: true 
  }, // e.g., "billOfSale_prod" or "billOfSale_test"
  year: { 
    type: Number, 
    required: true 
  }, // Current year (e.g., 2025)
  sequenceValue: { 
    type: Number, 
    required: true, 
    default: 0 
  } // Current sequence number
}, {
  timestamps: true
});

// Create compound index for efficient queries
counterSchema.index({ _id: 1, year: 1 });

// Static method to get next sequence number
counterSchema.statics.getNextSequence = async function(counterId) {
  const currentYear = new Date().getFullYear();

  // First, try to find and increment existing counter
  const counter = await this.findOneAndUpdate(
    {
      _id: counterId,
      year: currentYear
    },
    {
      $inc: { sequenceValue: 1 }
    },
    {
      new: true
    }
  );

  if (counter) {
    return counter.sequenceValue;
  }

  // If no counter exists, create one with sequenceValue: 1
  try {
    const newCounter = await this.create({
      _id: counterId,
      year: currentYear,
      sequenceValue: 1
    });
    return newCounter.sequenceValue;
  } catch (error) {
    // Handle race condition - another process might have created the counter
    if (error.code === 11000) { // Duplicate key error
      // Try to increment again
      const existingCounter = await this.findOneAndUpdate(
        {
          _id: counterId,
          year: currentYear
        },
        {
          $inc: { sequenceValue: 1 }
        },
        {
          new: true
        }
      );
      return existingCounter.sequenceValue;
    }
    throw error;
  }
};

// Static method to reset counter for new year
counterSchema.statics.resetForNewYear = async function(counterId) {
  const currentYear = new Date().getFullYear();
  
  // Check if counter exists for current year
  const existingCounter = await this.findOne({ 
    _id: counterId, 
    year: currentYear 
  });
  
  if (!existingCounter) {
    // Create new counter for current year
    await this.create({
      _id: counterId,
      year: currentYear,
      sequenceValue: 0
    });
  }
  
  return true;
};

export default mongoose.models.Counter || mongoose.model('Counter', counterSchema);
