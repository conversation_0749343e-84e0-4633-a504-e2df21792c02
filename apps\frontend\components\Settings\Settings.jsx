'use client';

import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import ThemeToggle from '../ThemeToggle/ThemeToggle';
import LanguageSelector from '../LanguageSelector/LanguageSelector';
import styles from './Settings.module.css';

export default function Settings({ className = '' }) {
  const { currentTheme } = useTheme();
  const { t, currentLanguage } = useLanguage();

  return (
    <div className={`${styles.container} ${className}`}>
      <div className={styles.header}>
        <h2 className={styles.title}>{t('settings.title', 'Settings')}</h2>
        <p className={styles.subtitle}>
          {t('settings.subtitle', 'Manage your account preferences and settings')}
        </p>
      </div>

      <div className={styles.content}>
        <div className={styles.section}>
          <h3 className={styles.sectionTitle}>
            {t('settings.preferences', 'Preferences')}
          </h3>
          <div className={styles.setting}>
            <div className={styles.settingInfo}>
              <label className={styles.settingLabel}>
                {t('settings.theme', 'Theme')}
              </label>
              <p className={styles.settingDescription}>
                {t('settings.theme.description', 'Choose your preferred theme')}
              </p>
            </div>
            <ThemeToggle showLabel={false} size="medium" />
          </div>
        </div>

        <div className={styles.section}>
          <h3 className={styles.sectionTitle}>
            {t('settings.language', 'Language')}
          </h3>
          <div className={styles.setting}>
            <div className={styles.settingInfo}>
              <label className={styles.settingLabel}>
                {t('settings.language', 'Language')}
              </label>
              <p className={styles.settingDescription}>
                {t('settings.language.description', 'Select your preferred language')}
              </p>
            </div>
            <LanguageSelector showLabel={false} showFlags={true} />
          </div>
        </div>

        <div className={styles.section}>
          <h3 className={styles.sectionTitle}>
            {t('settings.account', 'Account')}
          </h3>
          <div className={styles.currentSettings}>
            <div className={styles.currentSetting}>
              <span className={styles.currentLabel}>
                {t('settings.theme', 'Current Theme')}:
              </span>
              <span className={styles.currentValue}>
                {currentTheme === 'dark' ? 'Dark' : 'Light'}
              </span>
            </div>
            <div className={styles.currentSetting}>
              <span className={styles.currentLabel}>
                {t('settings.language', 'Current Language')}:
              </span>
              <span className={styles.currentValue}>
                {currentLanguage === 'en' ? 'English' :
                 currentLanguage === 'es' ? 'Español' :
                 currentLanguage === 'fr' ? 'Français' : currentLanguage}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
