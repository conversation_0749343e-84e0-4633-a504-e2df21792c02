import "./globals.css";

import { Suspense } from "react";
import { SidebarProvider } from "../contexts/SidebarContext";
import { ThemeProvider } from "../contexts/ThemeContext";
import { LanguageProvider } from "../contexts/LanguageContext";
import { CompareProvider } from "../contexts/CompareContext";
import ClientLayout from "../components/ClientLayout/ClientLayout";
import GoogleAnalytics from "../components/GoogleAnalytics/GoogleAnalytics";




export const metadata = {
  title: "FazeNAuto",
  description: "Dealership Website",
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body className="root-layout">
        <ThemeProvider>
          <LanguageProvider>
            <CompareProvider>
              <SidebarProvider>
                <Suspense fallback={null}>
                  <GoogleAnalytics />
                </Suspense>
                <ClientLayout>
                  {children}
                </ClientLayout>
              </SidebarProvider>
            </CompareProvider>
          </LanguageProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
