'use client';

import { useState, useEffect } from 'react';
import {
  CheckIcon,
  XMarkIcon,
  QuestionMarkCircleIcon,
  SparklesIcon,
  CogIcon,
  ShieldCheckIcon,
  MusicalNoteIcon,
  WrenchScrewdriverIcon
} from '@heroicons/react/24/outline';
import styles from './FeaturesSelector.module.css';

// Icon mapping for different categories
const getCategoryIcon = (categoryValue) => {
  const iconMap = {
    exterior: SparklesIcon,
    interior: CogIcon,
    mechanical: WrenchScrewdriverIcon,
    safety: ShieldCheckIcon,
    entertainment: MusicalNoteIcon
  };
  return iconMap[categoryValue] || CogIcon;
};

// Get uniform feature icon based on category
const getFeatureIcon = (categoryValue) => {
  const IconComponent = getCategoryIcon(categoryValue);
  return <IconComponent className={styles.featureIcon} />;
};

export default function FeaturesSelector({ selectedFeatures = {}, onFeaturesChange }) {
  const [availableFeatures, setAvailableFeatures] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [expandedCategories, setExpandedCategories] = useState({});

  const categories = [
    {
      value: 'exterior',
      label: 'Exterior Features',
      icon: '🚗',
      description: 'External vehicle features and styling'
    },
    {
      value: 'interior',
      label: 'Interior Features',
      icon: '🪑',
      description: 'Cabin comfort and convenience features'
    },
    {
      value: 'mechanical',
      label: 'Mechanical Features',
      icon: '⚙️',
      description: 'Engine, transmission, and drivetrain features'
    },
    {
      value: 'safety',
      label: 'Safety Features',
      icon: '🛡️',
      description: 'Safety systems and protective features'
    },
    {
      value: 'entertainment',
      label: 'Entertainment Features',
      icon: '📱',
      description: 'Infotainment and connectivity features'
    }
  ];

  // Feature icons mapping
  const featureIcons = {
    // Exterior
    'Alloy Wheels': '🛞', 'Sunroof': '🌞', 'LED Headlights': '💡', 'Roof Rails': '📏',
    'Tinted Windows': '🕶️', 'Chrome Trim': '✨', 'Running Boards': '🪜', 'Heated Mirrors': '🔥',
    'Fog Lights': '🌫️', 'Heated Exterior Mirrors': '🪞', 'Power Mirrors': '🔌', 'Tinted Glass': '🕶️',

    // Interior
    'Leather Seats': '🪑', 'Heated Seats': '🔥', 'Ventilated Seats': '💨', 'Power Seats': '⚡',
    'Memory Seats': '💾', 'Dual Zone Climate Control': '🌡️', 'Rear Air Conditioning': '❄️',
    'Premium Sound System': '🔊', '360 Degree Camera': '📹', 'Wireless Charging': '🔋',
    'USB Ports': '🔌', 'Cup Holders': '☕', 'Storage Compartments': '📦',
    'Air Conditioning': '❄️', 'Auto Dimming Mirrors': '🪞', 'Cup Holder': '☕',
    'Digital Clock': '🕐', 'Navigation System': '🗺️', 'Power Locks': '🔒',
    'Rear Window Defroster': '🌬️', 'Trip Odometer': '📊',

    // Mechanical
    'All-Wheel Drive': '🚙', 'Four-Wheel Drive': '🚜', 'Turbo Engine': '💨', 'Hybrid System': '🔋',
    'Manual Transmission': '⚙️', 'Automatic Transmission': '🔄', 'CVT Transmission': '🔄',
    'Sport Mode': '🏎️', 'Eco Mode': '🌱', 'Tow Package': '🚛', 'Limited Slip Differential': '⚙️',
    'Performance Suspension': '🏁', 'Bluetooth': '📶', 'Cruise Control': '🎯', 'Keyless Entry': '🔑',

    // Safety
    'Anti-lock Brakes (ABS)': '🛑', 'Electronic Stability Control': '🎯', 'Airbags': '🎈',
    'Side Airbags': '🎈', 'Curtain Airbags': '🎈', 'Blind Spot Monitoring': '👁️',
    'Lane Departure Warning': '🛣️', 'Forward Collision Warning': '⚠️', 'Automatic Emergency Braking': '🚨',
    'Adaptive Cruise Control': '🎯', 'Parking Sensors': '📡', 'Tire Pressure Monitoring': '🛞',
    'Child Safety Locks': '👶', 'ABS Brakes': '🛑', 'Backup Camera': '📹',
    'Driver Side Airbag': '🎈', 'Low Tire Pressure Warning': '⚠️', 'Passenger Airbag': '🎈',
    'Traction Control': '🎯',

    // Entertainment
    'Bluetooth Connectivity': '📶', 'Apple CarPlay': '📱', 'Android Auto': '🤖',
    'AM/FM Radio': '📻', 'Rear Entertainment System': '📺', 'WiFi Hotspot': '📶',
    'Voice Control': '🎤', 'Steering Wheel Controls': '🎛️', 'Touchscreen Display': '📱',
    'AM/FM Stereo': '📻', 'CD Player': '💿', 'DVD Player': '📀', 'Entertainment System': '📺',
    'Satellite Radio': '📡'
  };

  const toggleCategory = (categoryValue) => {
    setExpandedCategories(prev => ({
      ...prev,
      [categoryValue]: !prev[categoryValue]
    }));
  };

  useEffect(() => {
    fetchFeatures();
    // Expand first category by default
    setExpandedCategories({ exterior: true });
  }, []);

  const fetchFeatures = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/features?grouped=true');
      const data = await response.json();

      if (data.success) {
        setAvailableFeatures(data.data);
      } else {
        setError('Failed to load features');
      }
    } catch (err) {
      setError('Network error while loading features');
    } finally {
      setLoading(false);
    }
  };

  const handleFeatureStatusChange = (category, featureValue, status) => {
    const currentCategoryFeatures = selectedFeatures[category] || [];

    // Ensure currentCategoryFeatures is an array
    const featuresArray = Array.isArray(currentCategoryFeatures) ? currentCategoryFeatures : [];

    let updatedFeatures;

    // Remove existing entry for this feature if it exists
    const filteredFeatures = featuresArray.filter(f => f && f.value !== featureValue);

    if (status === 'none') {
      // Don't add anything if status is 'none' (no selection)
      updatedFeatures = filteredFeatures;
    } else {
      // Add feature with new status
      updatedFeatures = [...filteredFeatures, { value: featureValue, status }];
    }

    const newSelectedFeatures = {
      ...selectedFeatures,
      [category]: updatedFeatures
    };

    onFeaturesChange(newSelectedFeatures);
  };

  const getFeatureStatus = (category, featureValue) => {
    const categoryFeatures = selectedFeatures[category] || [];

    // Ensure categoryFeatures is an array
    if (!Array.isArray(categoryFeatures)) {
      console.warn(`Category features for ${category} is not an array:`, categoryFeatures);
      return 'none';
    }

    const feature = categoryFeatures.find(f => f && f.value === featureValue);
    return feature ? feature.status : 'none';
  };

  if (loading) {
    return (
      <div className={styles.container}>
        <h3>Vehicle Features</h3>
        <div className={styles.loading}>Loading features...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.container}>
        <h3>Vehicle Features</h3>
        <div className={styles.error}>{error}</div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <h3 className={styles.title}>Vehicle Features</h3>
      <p className={styles.subtitle}>Select all applicable features for this vehicle</p>

      <div className={styles.categoriesContainer}>
        {categories.map(category => (
          <div key={category.value} className={styles.categoryCard}>
            <div
              className={styles.categoryHeader}
              onClick={() => toggleCategory(category.value)}
            >
              <div className={styles.categoryInfo}>
                <span className={styles.categoryIcon}>{category.icon}</span>
                <div className={styles.categoryText}>
                  <h4 className={styles.categoryTitle}>{category.label}</h4>
                  <p className={styles.categoryDescription}>{category.description}</p>
                </div>
              </div>
              <div className={styles.categoryToggle}>
                <span className={`${styles.toggleIcon} ${expandedCategories[category.value] ? styles.expanded : ''}`}>
                  ▼
                </span>
                <span className={styles.featureCount}>
                  {availableFeatures[category.value]?.length || 0} features
                </span>
              </div>
            </div>

            <div className={`${styles.categoryContent} ${expandedCategories[category.value] ? styles.expanded : ''}`}>
              <div className={styles.featuresGrid}>
                {availableFeatures[category.value] && availableFeatures[category.value].length > 0 ? (
                  availableFeatures[category.value].map(feature => {
                    const currentStatus = getFeatureStatus(category.value, feature.value);

                    return (
                      <div key={feature._id} className={styles.featureItem}>
                        <div className={styles.featureHeader}>
                          {getFeatureIcon(category.value)}
                          <span className={styles.featureText}>{feature.value}</span>
                        </div>
                        <div className={styles.chipGroup}>
                          <button
                            type="button"
                            className={`${styles.chip} ${styles.chipIncluded} ${currentStatus === 'included' ? styles.chipSelected : ''}`}
                            onClick={() => handleFeatureStatusChange(category.value, feature.value, 'included')}
                          >
                            <CheckIcon className={styles.chipIcon} />
                            <span className={styles.chipText}>Included</span>
                          </button>
                          <button
                            type="button"
                            className={`${styles.chip} ${styles.chipNotAvailable} ${currentStatus === 'not_available' ? styles.chipSelected : ''}`}
                            onClick={() => handleFeatureStatusChange(category.value, feature.value, 'not_available')}
                          >
                            <XMarkIcon className={styles.chipIcon} />
                            <span className={styles.chipText}>N/A</span>
                          </button>
                          <button
                            type="button"
                            className={`${styles.chip} ${styles.chipUnknown} ${currentStatus === 'none' ? styles.chipSelected : ''}`}
                            onClick={() => handleFeatureStatusChange(category.value, feature.value, 'none')}
                          >
                            <QuestionMarkCircleIcon className={styles.chipIcon} />
                            <span className={styles.chipText}>Unknown</span>
                          </button>
                        </div>
                      </div>
                    );
                  })
                ) : (
                  <div className={styles.noFeatures}>
                    No features available in this category
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Selected Features Summary */}
      <div className={styles.summary}>
        <h4>Selected Features Summary:</h4>
        {Object.keys(selectedFeatures).some(cat => selectedFeatures[cat]?.length > 0) ? (
          <div className={styles.selectedFeatures}>
            {categories.map(category => {
              const categoryFeatures = selectedFeatures[category.value] || [];

              // Ensure categoryFeatures is an array
              if (!Array.isArray(categoryFeatures) || categoryFeatures.length === 0) return null;

              const includedFeatures = categoryFeatures.filter(f => f && f.status === 'included');
              const notAvailableFeatures = categoryFeatures.filter(f => f && f.status === 'not_available');

              return (
                <div key={category.value} className={styles.summaryCategory}>
                  <strong>{category.label}:</strong>
                  {includedFeatures.length > 0 && (
                    <div className={styles.summaryGroup}>
                      <span className={styles.summaryLabel}>✅ Included:</span> {includedFeatures.map(f => f.value).join(', ')}
                    </div>
                  )}
                  {notAvailableFeatures.length > 0 && (
                    <div className={styles.summaryGroup}>
                      <span className={styles.summaryLabel}>❌ Not Available:</span> {notAvailableFeatures.map(f => f.value).join(', ')}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        ) : (
          <div className={styles.noSelection}>No features selected</div>
        )}
      </div>
    </div>
  );
}
