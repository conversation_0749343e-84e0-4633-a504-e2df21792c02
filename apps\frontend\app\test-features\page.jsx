'use client';

import { useState } from 'react';
import { useLanguage } from '../../contexts/LanguageContext';
import LanguageSelector from '../../components/LanguageSelector/LanguageSelector';
import FeaturesSelector from '../../components/FeaturesSelector/FeaturesSelector';

export default function TestFeaturesPage() {
  const { currentLanguage, t, isLoading, changeLanguage } = useLanguage();

  // Test function to manually load and test translations
  const testTranslations = async () => {
    console.log('🧪 Testing translations manually...');
    try {
      const { loadTranslation, getTranslation } = await import('../../lib/translations');

      // Test loading English
      const enData = await loadTranslation('en');
      console.log('🧪 English data:', enData);
      console.log('🧪 English keys:', Object.keys(enData));

      // Test getTranslation function
      const enTranslator = getTranslation('en');
      console.log('🧪 nav.home (en):', enTranslator('nav.home'));
      console.log('🧪 nav.inventory (en):', enTranslator('nav.inventory'));

      // Test Spanish
      const esData = await loadTranslation('es');
      console.log('🧪 Spanish data:', esData);
      const esTranslator = getTranslation('es');
      console.log('🧪 nav.home (es):', esTranslator('nav.home'));

    } catch (error) {
      console.error('🧪 Translation test failed:', error);
    }
  };
  const [selectedFeatures, setSelectedFeatures] = useState({
    exterior: [
      { value: 'Alloy Wheels', status: 'included' },
      { value: 'Sunroof', status: 'not_available' }
    ],
    interior: [
      { value: 'Leather Seats', status: 'included' }
    ],
    mechanical: [],
    safety: [],
    entertainment: []
  });

  const handleFeaturesChange = (newFeatures) => {
    console.log('Features changed:', newFeatures);
    setSelectedFeatures(newFeatures);
  };

  return (
    <div style={{ padding: '2rem', maxWidth: '1200px', margin: '0 auto' }}>
      <h1>Test Features Selector & Language</h1>
      <p>This page tests the FeaturesSelector component and language integration.</p>

      {/* Language Testing Section */}
      <div style={{ marginTop: '2rem', padding: '1rem', backgroundColor: '#e8f4fd', borderRadius: '8px', border: '1px solid #ccc' }}>
        <h2>Language Testing</h2>
        <div style={{ marginBottom: '1rem' }}>
          <p><strong>Current Language:</strong> {currentLanguage}</p>
          <p><strong>Loading:</strong> {isLoading ? 'Yes' : 'No'}</p>
        </div>

        <div style={{ marginBottom: '1rem' }}>
          <h3>Translation Tests:</h3>
          <p><strong>nav.home:</strong> "{t('nav.home')}"</p>
          <p><strong>nav.inventory:</strong> "{t('nav.inventory')}"</p>
          <p><strong>settings.title:</strong> "{t('settings.title')}"</p>
          <p><strong>Missing key:</strong> "{t('missing.key')}"</p>
        </div>

        <div style={{ marginBottom: '1rem' }}>
          <h3>Home Page Translation Tests:</h3>
          <p><strong>home.hero.title:</strong> "{t('home.hero.title')}"</p>
          <p><strong>home.hero.subtitle:</strong> "{t('home.hero.subtitle')}"</p>
          <p><strong>home.about.title:</strong> "{t('home.about.title')}"</p>
          <p><strong>home.services.title:</strong> "{t('home.services.title')}"</p>
        </div>

        <div style={{ marginBottom: '1rem' }}>
          <h3>Footer Translation Tests:</h3>
          <p><strong>footer.brand_description:</strong> "{t('footer.brand_description')}"</p>
          <p><strong>footer.contact.title:</strong> "{t('footer.contact.title')}"</p>
          <p><strong>footer.copyright:</strong> "{t('footer.copyright')}"</p>
        </div>

        <div style={{ marginBottom: '1rem' }}>
          <h3>Language Selector Component:</h3>
          <LanguageSelector />
        </div>

        <div style={{ marginBottom: '1rem' }}>
          <h3>Manual Language Change:</h3>
          <button
            onClick={() => changeLanguage('en')}
            style={{ marginRight: '0.5rem', padding: '0.5rem 1rem', backgroundColor: '#007bff', color: 'white', border: 'none', borderRadius: '4px', cursor: 'pointer' }}
          >
            English
          </button>
          <button
            onClick={() => changeLanguage('es')}
            style={{ marginRight: '0.5rem', padding: '0.5rem 1rem', backgroundColor: '#007bff', color: 'white', border: 'none', borderRadius: '4px', cursor: 'pointer' }}
          >
            Español
          </button>
          <button
            onClick={() => changeLanguage('fr')}
            style={{ padding: '0.5rem 1rem', backgroundColor: '#007bff', color: 'white', border: 'none', borderRadius: '4px', cursor: 'pointer' }}
          >
            Français
          </button>
        </div>

        <div style={{ marginBottom: '1rem' }}>
          <h3>Debug Test:</h3>
          <button
            onClick={testTranslations}
            style={{ padding: '0.5rem 1rem', backgroundColor: '#28a745', color: 'white', border: 'none', borderRadius: '4px', cursor: 'pointer' }}
          >
            Test Translations Manually
          </button>
        </div>
      </div>

      {/* Features Selector Section */}
      <div style={{ marginTop: '2rem' }}>
        <h2>Features Selector Test</h2>
        <FeaturesSelector
          selectedFeatures={selectedFeatures}
          onFeaturesChange={handleFeaturesChange}
        />
      </div>

      <div style={{ marginTop: '2rem', padding: '1rem', backgroundColor: '#f5f5f5', borderRadius: '8px' }}>
        <h3>Current Selected Features (Debug):</h3>
        <pre style={{ fontSize: '12px', overflow: 'auto' }}>
          {JSON.stringify(selectedFeatures, null, 2)}
        </pre>
      </div>
    </div>
  );
}

