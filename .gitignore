# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/
*/.next/
*/out/
**/.next/
**/out/

# Build artifacts and cache
**/node_modules/
**/.next/cache/
**/.next/static/
**/.next/server/
**/build/
**/dist/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*
.env.local
.env.example

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts


.continue/
.continue/*

