.editContainer {
  max-width: none;
  margin: 0;
  padding: 0;
  background-color: #ffffff;
  min-height: 100vh;
  transition: background 0.3s ease;
}

/* Dark mode: Apply consistent gradient background */
[data-theme="dark"] .editContainer {
  background: var(--bg-primary);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 2rem 1rem 1.5rem;
  background: transparent;
  border-bottom: 3px solid #dc3545;
  transition: all 0.3s ease;
}

/* Dark mode styling for header */
[data-theme="dark"] .header {
  border-bottom-color: #dc3545;
}

/* Responsive padding adjustments for header */
@media (min-width: 769px) {
  .header {
    padding: 2rem 3rem 1.5rem;
  }
}

@media (min-width: 1200px) {
  .header {
    padding: 2rem 4rem 1.5rem;
  }
}

.header h1 {
  margin: 0;
  color: #333;
  font-size: 2rem;
  font-weight: 600;
  transition: color 0.3s ease;
}

/* Dark mode styling for header text */
[data-theme="dark"] .header h1 {
  color: #ffffff;
}

.backBtn {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.backBtn:hover {
  background: linear-gradient(135deg, #c82333 0%, #a71e2a 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

.backBtn:active {
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
}

.successAlert {
  background: var(--success-bg, #d4edda);
  color: var(--success, #155724);
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 2rem;
  border: 1px solid var(--success-border, #c3e6cb);
}

.errorAlert {
  background: var(--error-bg, #f8d7da);
  color: var(--error, #721c24);
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 2rem;
  border: 1px solid var(--error-border, #f5c6cb);
}

.form {
  background: transparent;
  padding: 0 1rem 2rem;
  margin-bottom: 2rem;
  transition: all 0.3s ease;
}

/* Responsive padding adjustments for form */
@media (min-width: 769px) {
  .form {
    padding: 0 3rem 2rem;
  }
}

@media (min-width: 1200px) {
  .form {
    padding: 0 4rem 2rem;
  }
}

.formGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
}

.formGroup label {
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #333;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: color 0.3s ease;
}

/* Dark mode styling for labels */
[data-theme="dark"] .formGroup label {
  color: #ffffff;
}

.labelIcon {
  color: #dc3545;
  font-size: 0.9rem;
}

.fullWidth {
  grid-column: 1 / -1;
}

/* Textarea specific styling - inherits base from .formInput */
.textarea {
  resize: vertical;
  min-height: 120px;
}

.fullWidth {
  grid-column: 1 / -1;
  margin-top: 1rem;
}

.formActions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  padding-top: 2rem;
}

.cancelBtn {
  padding: 0.75rem 1.5rem;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.cancelBtn:hover {
  background: #5a6268;
}

.saveBtn {
  padding: 0.75rem 1.5rem;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.saveBtn:hover:not(:disabled) {
  background: #218838;
}

.saveBtn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.photoSection {
  margin-top: 2rem;
  padding: 1.5rem 0;
  background: none;
  border-radius: 0;
  box-shadow: none;
  transition: all 0.3s ease;
  border-top: 1px solid var(--border-primary);
}

/* Dark mode styling for photo section */
[data-theme="dark"] .photoSection {
  background: none;
  border: none;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: none;
}

.photoSection h3 {
  margin-bottom: 1.5rem;
  color: #333;
  font-size: 1.5rem;
  transition: color 0.3s ease;
}

.photoSection h4 {
  margin-bottom: 1rem;
  color: #555;
  font-size: 1.2rem;
  transition: color 0.3s ease;
}

.photoSection h5 {
  margin-bottom: 0.5rem;
  color: #666;
  font-size: 1rem;
  transition: color 0.3s ease;
}

/* Dark mode styling for photo section headings */
[data-theme="dark"] .photoSection h3,
[data-theme="dark"] .photoSection h4,
[data-theme="dark"] .photoSection h5 {
  color: #ffffff;
}

.currentImages {
  margin-bottom: 2rem;
}

.addImages {
  border-top: none;
  padding-top: 1.5rem;
}

.imageGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

.imageContainer {
  position: relative;
}

.vehicleImage {
  width: 100%;
  height: 150px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.deleteBtn {
  position: absolute;
  top: 5px;
  right: 5px;
  background: #dc3545;
  color: white;
  border: none;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.75rem;
}

.deleteBtn:hover {
  background: #c82333;
}

.undoDeleteBtn {
  position: absolute;
  top: 5px;
  right: 5px;
  background: #28a745;
  color: white;
  border: none;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.75rem;
}

.undoDeleteBtn:hover {
  background: #218838;
}

.deleteOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(220, 53, 69, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  border-radius: 4px;
}

.fileInput {
  padding: 0.5rem;
  border: 2px dashed #ddd;
  border-radius: 4px;
  background: #f9f9f9;
  width: 100%;
  margin-bottom: 1rem;
}

.newImagesPreview {
  margin-top: 1rem;
}

/* Responsive */
@media (max-width: 768px) {
  .editContainer {
    padding: 1rem;
  }

  .header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
    padding: 1rem;
  }

  .form {
    padding: 1.5rem;
  }

  .formGrid {
    grid-template-columns: 1fr;
  }

  .formActions {
    flex-direction: column;
  }

  .imageGrid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
}


