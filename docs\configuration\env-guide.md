# Environment Variables Guide

This guide explains every environment variable used in the FazeNAuto application. 🔧

## 📋 Quick Setup

1. Copy `.env.example` to `.env.local`
2. Fill in your actual values
3. Never commit `.env.local` to version control!

```bash
cp .env.example .env.local
```

## 🗄️ Database Configuration

### `MONGODB_URI`
**Purpose**: MongoDB database connection string
**Required**: ✅ Yes
**Examples**:
- Local: `mongodb://localhost:27017/fazenauto-dev`
- Atlas: `mongodb+srv://username:<EMAIL>/fazenauto`

**How to get**:
1. **Local MongoDB**: Install MongoDB locally
2. **MongoDB Atlas**: Create free cluster at mongodb.com/atlas

## ☁️ AWS S3 Configuration

### `CUSTOM_AWS_ACCESS_KEY_ID`
**Purpose**: AWS access key for S3 uploads
**Required**: ✅ Yes
**Example**: `AKIAIOSFODNN7EXAMPLE`

### `CUSTOM_AWS_SECRET_ACCESS_KEY`
**Purpose**: AWS secret key for S3 uploads
**Required**: ✅ Yes
**Example**: `wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY`

### `CUSTOM_AWS_REGION`
**Purpose**: AWS region where S3 bucket is located
**Required**: ✅ Yes
**Example**: `us-east-1`

### `CUSTOM_AWS_S3_BUCKET_NAME`
**Purpose**: S3 bucket name for storing images/videos
**Required**: ✅ Yes
**Example**: `fazenauto-vehicle-images`

**How to set up AWS S3**:
1. Create AWS account
2. Create S3 bucket
3. Create IAM user with S3 permissions
4. Generate access keys

## 🔐 Google OAuth

### `GOOGLE_CLIENT_ID`
**Purpose**: Google OAuth client ID for user authentication
**Required**: ✅ Yes (for Google login)
**Example**: `*********-abcdef.apps.googleusercontent.com`

### `GOOGLE_CLIENT_SECRET`
**Purpose**: Google OAuth client secret
**Required**: ✅ Yes (for Google login)
**Example**: `GOCSPX-abcdef123456`

**How to set up Google OAuth**:
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create new project or select existing
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URIs:
   - `http://localhost:3000/api/auth/callback/google` (dev)
   - `https://yourdomain.com/api/auth/callback/google` (prod)

## 🍎 Apple OAuth

### `APPLE_CLIENT_ID`
**Purpose**: Apple OAuth service ID
**Required**: ✅ Yes (for Apple login)
**Example**: `com.fazenauto.signin`

### `APPLE_CLIENT_SECRET`
**Purpose**: Apple OAuth client secret (JWT token)
**Required**: ✅ Yes (for Apple login)

### `APPLE_TEAM_ID`
**Purpose**: Apple Developer Team ID
**Required**: ✅ Yes (for Apple login)
**Example**: `ABCD123456`

### `APPLE_KEY_ID`
**Purpose**: Apple private key identifier
**Required**: ✅ Yes (for Apple login)
**Example**: `ABCD123456`

### `APPLE_PRIVATE_KEY`
**Purpose**: Apple private key content (.p8 file)
**Required**: ✅ Yes (for Apple login)

**How to set up Apple OAuth**:
1. Go to [Apple Developer Console](https://developer.apple.com/)
2. Create App ID
3. Create Service ID
4. Create private key (.p8 file)
5. Configure Sign in with Apple

## 🔑 NextAuth Configuration

### `NEXTAUTH_URL`
**Purpose**: Your application's base URL
**Required**: ✅ Yes
**Examples**:
- Development: `http://localhost:3000`
- Production: `https://fazenauto.com`

### `NEXTAUTH_SECRET`
**Purpose**: Secret key for encrypting JWT tokens
**Required**: ✅ Yes
**Generate with**: `openssl rand -base64 32`
**Example**: `your-random-32-character-secret-key`

## 🌐 Application Settings

### `NODE_ENV`
**Purpose**: Application environment
**Required**: ✅ Yes
**Values**: `development`, `staging`, `production`

### `NEXT_PUBLIC_APP_URL`
**Purpose**: Public-facing application URL
**Required**: ✅ Yes
**Note**: Variables starting with `NEXT_PUBLIC_` are exposed to the browser

### `NEXT_PUBLIC_PHONE`
**Purpose**: Business phone number displayed on site
**Required**: ❌ No
**Example**: `************`

### `NEXT_PUBLIC_ADDRESS`
**Purpose**: Business address displayed on site
**Required**: ❌ No
**Example**: `1120 Meighen Way`

## 🌍 External APIs

### `NHTSA_API_URL`
**Purpose**: NHTSA VIN decoder API endpoint
**Required**: ❌ No (for VIN decoding feature)
**Default**: `https://vpic.nhtsa.dot.gov/api`
**Note**: Free API, no key required

### `IPAPI_URL`
**Purpose**: IP geolocation service
**Required**: ❌ No (for visitor tracking)
**Default**: `http://ipapi.co`
**Note**: 1000 requests/month free

## 📧 Email Configuration

### `SMTP_HOST`
**Purpose**: SMTP server for sending emails
**Required**: ❌ No (for email features)
**Example**: `smtp.gmail.com`

### `SMTP_PORT`
**Purpose**: SMTP server port
**Required**: ❌ No
**Example**: `587`

### `SMTP_USER` & `SMTP_PASS`
**Purpose**: SMTP authentication credentials
**Required**: ❌ No
**Note**: Use app passwords for Gmail

## 📱 Social Media Integration

### `FACEBOOK_PAGE_ID`
**Purpose**: Facebook business page ID
**Required**: ❌ No (for Facebook integration)
**Example**: `61577486330108`

### `FACEBOOK_APP_ID` & `FACEBOOK_APP_SECRET`
**Purpose**: Facebook app credentials for API access
**Required**: ❌ No (for Facebook posting)

## 📊 Analytics & Monitoring

### `NEXT_PUBLIC_GA_MEASUREMENT_ID`
**Purpose**: Google Analytics tracking ID
**Required**: ❌ No (for analytics)
**Example**: `G-XXXXXXXXXX`

### `SENTRY_DSN`
**Purpose**: Sentry error tracking
**Required**: ❌ No (for error monitoring)

## 🛠️ Development Tools

### `DEBUG`
**Purpose**: Enable debug logging
**Required**: ❌ No
**Values**: `true`, `false`

### `NEXT_TELEMETRY_DISABLED`
**Purpose**: Disable Next.js telemetry
**Required**: ❌ No
**Value**: `1` (to disable)

## 🔒 Security Settings

### `JWT_SECRET`
**Purpose**: Secret for custom JWT tokens
**Required**: ❌ No (if using custom auth)

### `SESSION_TIMEOUT`
**Purpose**: Session timeout in seconds
**Required**: ❌ No
**Default**: `3600` (1 hour)

### `MAX_FILE_SIZE`
**Purpose**: Maximum file upload size in bytes
**Required**: ❌ No
**Default**: `10485760` (10MB)

### `ALLOWED_FILE_TYPES`
**Purpose**: Comma-separated list of allowed MIME types
**Required**: ❌ No
**Default**: `image/jpeg,image/png,image/webp,video/mp4`

## 🚦 Rate Limiting

### `RATE_LIMIT_RPM`
**Purpose**: API requests per minute limit
**Required**: ❌ No
**Default**: `100`

### `UPLOAD_LIMIT_PER_HOUR`
**Purpose**: File uploads per hour limit
**Required**: ❌ No
**Default**: `50`

## 🚨 Common Issues

### Missing Required Variables
**Error**: Application won't start
**Solution**: Check all required variables are set

### Wrong MongoDB URI
**Error**: Database connection failed
**Solution**: Verify connection string format and credentials

### Invalid AWS Credentials
**Error**: S3 upload fails
**Solution**: Check access keys and bucket permissions

### OAuth Redirect Mismatch
**Error**: OAuth login fails
**Solution**: Verify redirect URIs in OAuth provider settings

## 🔍 Environment-Specific Tips

### Development
- Use local MongoDB or free Atlas cluster
- Use test AWS credentials
- Set `DEBUG=true`
- Use `http://localhost:3000` for URLs

### Staging
- Mirror production setup with test data
- Use staging AWS bucket
- Test OAuth with staging URLs

### Production
- Use production credentials
- Enable monitoring (Sentry, Analytics)
- Set secure secrets
- Use HTTPS URLs

---

*Keep your environment variables secure and never commit them to version control! 🔐*
