# Vehicle Information & VIN Decoding System

This document provides a comprehensive overview of all code, components, and configurations related to VIN decoding and vehicle information retrieval in the FazeNAuto project.

## Table of Contents

1. [Environment Variables](#environment-variables)
2. [API Routes](#api-routes)
3. [Utility Libraries](#utility-libraries)
4. [React Components](#react-components)
5. [Authentication & Authorization](#authentication--authorization)
6. [Error Handling & Retry Logic](#error-handling--retry-logic)
7. [Data Transformation](#data-transformation)

## Environment Variables

### MarketCheck API Configuration
```bash
# apps/frontend/.env.local
MARKETCHECK_API_KEY=FMFLQmNE8ixrzROCZc80bCmZiUJv2sMF
MARKETCHECK_API_SECRET=uNmqH5e3sZwjRXN2
```

### Authentication Configuration
```bash
# apps/frontend/.env.local
ADMIN_SECRET=TestSeceret123
AUTHORIZED_EMAILS=<EMAIL>,<EMAIL>
```

## API Routes

### Primary Vehicle Information API
**File:** `apps/frontend/app/api/vehicle-info/route.js`

Main API endpoint that orchestrates all vehicle information retrieval:

```javascript
export async function POST(request) {
  // Validates VIN format
  // Fetches NHTSA data with retry logic
  // Fetches recalls data
  // Fetches MarketCheck data (server-side only)
  // Returns comprehensive vehicle information
}
```

**Key Features:**
- VIN validation with regex pattern: `/^[A-HJ-NPR-Z0-9]{17}$/`
- NHTSA API integration with 3-attempt retry logic
- Recalls API integration with 2-attempt retry logic
- MarketCheck API integration with fallback endpoints
- Progressive timeout handling (10s, 20s, 30s for NHTSA)
- Comprehensive field mapping for frontend compatibility

**NHTSA Integration:**
```javascript
async function fetchNHTSADataWithRetry(vin, maxRetries = 3) {
  // Endpoint: https://vpic.nhtsa.dot.gov/api/vehicles/DecodeVin/{vin}?format=json
  // Headers: User-Agent: 'FazeNAuto-VehicleLookup/1.0'
  // Timeout: Progressive (10s, 20s, 30s)
  // Retry logic with exponential backoff
}
```

**MarketCheck Integration:**
```javascript
async function fetchMarketCheckDataWithRetry(vin, vehicleData, apiKey, apiSecret) {
  // Primary endpoint: https://mc-api.marketcheck.com/v2/search/car/active
  // Fallback: Search by make/model/year
  // Authentication: API key in query params
  // Timeout: 15s, 30s for retries
}
```

**Recalls Integration:**
```javascript
async function fetchRecallsDataWithRetry(make, model, year, maxRetries = 2) {
  // Endpoint: https://api.nhtsa.gov/recalls/recallsByVehicle
  // Parameters: make, model, modelYear
  // Timeout: 10s, 20s for retries
}
```

## Utility Libraries

### VIN Decoder Utility
**File:** `apps/frontend/utils/vinDecoder.js`

Core VIN decoding functionality:

```javascript
export async function decodeVIN(vin) {
  // VIN validation and cleaning
  // NHTSA API call with 15-second timeout
  // Response parsing and data structuring
  // Error handling with specific messages
}

export function validateVIN(vin) {
  // Format validation (17 characters, no I/O/Q)
  // Returns boolean
}

export function mapVINToFormData(vinData) {
  // Maps decoded VIN data to form structure
  // Used in vehicle upload forms
}
```

**NHTSA Field Mapping:**
- Supports 50+ NHTSA fields including engine, transmission, dimensions
- Handles multiple field name variants
- Filters out "N/A", "Not Applicable", empty values

### MarketCheck Service
**File:** `apps/frontend/lib/marketcheck.js`

Dealer-only pricing data service:

```javascript
export async function getMarketValue(vin, vehicleData = {}) {
  // Market value lookup by VIN
  // Fallback to make/model/year search
  // 20-second timeout with abort controller
}

export async function getComparableListings(make, model, year, options = {}) {
  // Find similar vehicles for comparison
  // Supports price and mileage filters
}

export async function getPriceHistory(vin) {
  // Historical pricing data for specific VIN
  // Trend analysis and market insights
}
```

### Ad Generator Integration
**File:** `apps/frontend/lib/adGenerator.js`

Uses VIN decoder for automated listing generation:

```javascript
export class AdGenerator {
  async generateFromVIN(vin, vehicleDetails, options = {}) {
    // Step 1: Decode VIN using decodeVIN()
    // Step 2: Generate GPT-4 powered descriptions
    // Step 3: Create platform-specific ads
  }
}
```

## React Components

### Vehicle Lookup Page (Public)
**File:** `apps/frontend/app/vehicle-lookup/page.jsx`

Public-facing VIN lookup interface:

```javascript
export default function PublicVehicleLookupPage() {
  const { canAccessMarketCheck, isDealer, user } = useAuth();
  
  // VIN input handling
  // Vehicle data display
  // Conditional MarketCheck data rendering
  // Recalls information display
}
```

**Key Features:**
- VIN input with real-time validation
- VIN scanner integration
- Role-based MarketCheck data access
- Comprehensive vehicle information display
- Recalls and safety information

### Admin Vehicle Info Page
**File:** `apps/frontend/app/admin/vehicle-info/page.jsx`

Admin/dealer interface with enhanced features:

```javascript
export default function VehicleInfoPage() {
  const { canAccessMarketCheck, isDealer, isAdmin, user } = useAuth();
  
  // Enhanced VIN lookup with admin features
  // Full MarketCheck integration
  // Advanced vehicle data management
}
```

### VIN Scanner Component
**File:** `apps/frontend/components/VINScanner/VINScanner.jsx`

Camera-based VIN scanning:

```javascript
const VINScanner = ({ onVINDetected, onClose }) => {
  // Camera access and video stream
  // OCR text recognition
  // VIN pattern detection
  // Real-time scanning feedback
}
```

**Features:**
- Mobile-optimized camera interface
- Real-time VIN detection
- OCR integration for text recognition
- Error handling for camera permissions

### Vehicle Upload Integration
**File:** `apps/frontend/app/vehicles/upload/page.jsx`

VIN decoder integration in vehicle upload:

```javascript
// Auto-populate form fields from VIN
const handleVinDecode = async () => {
  const result = await decodeVIN(formData.vin);
  if (result.success) {
    const mappedData = mapVINToFormData(result.data);
    // Update form fields automatically
  }
};
```

## Authentication & Authorization

### Auth Library
**File:** `apps/frontend/lib/auth.js`

Core authentication and role management:

```javascript
export function canAccessMarketCheck() {
  // Only dealers and admins can access MarketCheck
  // Preserves API quota by restricting access
  return isDealer();
}

export function isDealer() {
  return hasRole('dealer') || hasRole('admin');
}

export function useAuth() {
  // React hook for authentication state
  // Returns: user, isAuthenticated, canAccessMarketCheck, etc.
}
```

### Protected Routes
**File:** `apps/frontend/components/ProtectedRoute/ProtectedRoute.jsx`

Route protection for admin/dealer features:

```javascript
export default function ProtectedRoute({ children, requiredRole }) {
  // Validates user authentication
  // Checks role-based permissions
  // Redirects unauthorized users
}
```

### User Model
**File:** `apps/frontend/lib/models/User.js`

Database schema with dealer role support:

```javascript
userSchema.methods.isDealer = function() {
  return this.role === 'dealer' || this.role === 'admin';
};

userSchema.methods.hasDealerInfo = function() {
  return this.dealerInfo && (this.dealerInfo.businessName || this.dealerInfo.rin);
};
```

## Error Handling & Retry Logic

### NHTSA API Error Handling
```javascript
// Progressive timeout: 10s, 20s, 30s
// Exponential backoff: 1s, 2s, 4s delays
// Specific error messages for different failure types
// Graceful degradation on persistent failures
```

### MarketCheck API Error Handling
```javascript
// 404 handling for VIN not found
// Fallback to make/model/year search
// Dealer-only access control
// API quota preservation
```

### Recalls API Error Handling
```javascript
// 2-attempt retry with progressive timeout
// Fallback to empty array on failure
// Non-blocking for main vehicle data
```

## Data Transformation

### NHTSA Response Mapping
The API maps 50+ NHTSA fields to frontend-compatible format:

```javascript
const mappedVehicleInfo = {
  // Basic fields
  make: vehicleInfo.make,
  model: vehicleInfo.model,
  year: vehicleInfo.year,
  
  // Engine fields with smart construction
  engine: vehicleInfo.engineModel || 
          `${vehicleInfo.cylinders} Cylinder ${vehicleInfo.engineConfiguration}`,
  cylinders: vehicleInfo.cylinders,
  
  // Displacement in multiple units
  displacementCC: vehicleInfo.engineDisplacementCC,
  displacementCI: vehicleInfo.engineDisplacementCI,
  displacementL: vehicleInfo.engineDisplacementL,
  
  // Power information
  horsepowerFrom: vehicleInfo.engineBrakeHpFrom,
  horsepowerTo: vehicleInfo.engineBrakeHpTo,
  
  // Drivetrain (dual mapping for compatibility)
  driveType: vehicleInfo.driveType,
  drivetrain: vehicleInfo.driveType,
};
```

### MarketCheck Response Processing
```javascript
function parseMarketValueResponse(data) {
  // Extract market value, price ranges
  // Process comparable listings
  // Calculate market insights
  // Format for frontend display
}
```

## Integration Points

### Navigation Integration
VIN lookup accessible from multiple navigation points:
- Public: `/vehicle-lookup`
- Admin: `/admin/vehicle-info`
- Mobile: VIN scanner in upload forms

### Form Auto-Population
VIN decoder integrates with:
- Vehicle upload forms
- Inventory management
- Compliance form generation

### API Endpoints Summary
- **NHTSA VIN Decoder:** `https://vpic.nhtsa.dot.gov/api/vehicles/DecodeVin/{vin}?format=json`
- **NHTSA Recalls:** `https://api.nhtsa.gov/recalls/recallsByVehicle`
- **MarketCheck Search:** `https://mc-api.marketcheck.com/v2/search/car/active`
- **Internal API:** `/api/vehicle-info` (POST)

This system provides comprehensive vehicle information retrieval with robust error handling, role-based access control, and seamless integration across the application.

## Detailed Code Examples

### Complete API Route Implementation
**File:** `apps/frontend/app/api/vehicle-info/route.js`

```javascript
import { NextResponse } from 'next/server';

export async function POST(request) {
  try {
    const { vin } = await request.json();

    // VIN validation
    if (!vin || vin.length !== 17) {
      return NextResponse.json({
        success: false,
        error: 'Valid 17-character VIN required'
      }, { status: 400 });
    }

    const vinPattern = /^[A-HJ-NPR-Z0-9]{17}$/;
    if (!vinPattern.test(vin.toUpperCase())) {
      return NextResponse.json({
        success: false,
        error: 'Invalid VIN format'
      }, { status: 400 });
    }

    console.log('🔍 Processing vehicle info request for VIN:', vin);

    // Fetch NHTSA data with retry logic
    let nhtsaData;
    try {
      nhtsaData = await fetchNHTSADataWithRetry(vin);
    } catch (error) {
      console.error('❌ NHTSA API error after retries:', error);

      let errorMessage = 'Failed to fetch vehicle data from NHTSA';
      if (error.name === 'AbortError' || error.message.includes('timeout')) {
        errorMessage = 'NHTSA API timeout - the service is currently slow. Please try again.';
      } else if (error.message.includes('fetch failed') || error.message.includes('network')) {
        errorMessage = 'Network connection issue - NHTSA API unavailable. Please check your internet connection.';
      }

      return NextResponse.json({
        success: false,
        error: errorMessage,
        details: error.message
      }, { status: 503 });
    }

    // Parse NHTSA response with comprehensive field mapping
    const results = nhtsaData.Results || [];
    const vehicleInfo = {};

    results.forEach(item => {
      if (item.Value && item.Value !== 'Not Applicable' && item.Value !== '' && item.Value !== 'N/A') {
        switch (item.Variable) {
          // Basic vehicle information
          case 'Make':
            vehicleInfo.make = item.Value;
            break;
          case 'Model':
            vehicleInfo.model = item.Value;
            break;
          case 'Model Year':
            vehicleInfo.year = parseInt(item.Value);
            break;
          case 'Vehicle Type':
            vehicleInfo.vehicleType = item.Value;
            break;
          case 'Body Class':
            vehicleInfo.bodyClass = item.Value;
            break;
          case 'Manufacturer Name':
            vehicleInfo.manufacturer = item.Value;
            break;
          case 'Plant City':
            vehicleInfo.plantCity = item.Value;
            break;
          case 'Plant Country':
            vehicleInfo.plantCountry = item.Value;
            break;
          case 'Trim':
            vehicleInfo.trim = item.Value;
            break;

          // Engine information
          case 'Engine Number of Cylinders':
            vehicleInfo.cylinders = item.Value;
            break;
          case 'Engine Configuration':
            vehicleInfo.engineConfiguration = item.Value;
            break;
          case 'Engine Model':
            vehicleInfo.engineModel = item.Value;
            break;
          case 'Engine Manufacturer':
            vehicleInfo.engineManufacturer = item.Value;
            break;
          case 'Engine Brake (hp) From':
            vehicleInfo.engineBrakeHpFrom = item.Value;
            break;
          case 'Engine Brake (hp) To':
            vehicleInfo.engineBrakeHpTo = item.Value;
            break;

          // Displacement - multiple field names supported
          case 'Displacement (CC)':
          case 'Engine Displacement (CC)':
            vehicleInfo.engineDisplacementCC = item.Value;
            break;
          case 'Displacement (CI)':
          case 'Engine Displacement (CI)':
            vehicleInfo.engineDisplacementCI = item.Value;
            break;
          case 'Displacement (L)':
          case 'Engine Displacement (L)':
            vehicleInfo.engineDisplacementL = item.Value;
            break;

          // Fuel and transmission
          case 'Fuel Type - Primary':
            vehicleInfo.fuelType = item.Value;
            break;
          case 'Transmission Style':
            vehicleInfo.transmission = item.Value;
            break;
          case 'Drive Type':
            vehicleInfo.driveType = item.Value;
            break;

          // Physical characteristics
          case 'Number of Doors':
          case 'Doors':
            vehicleInfo.doors = item.Value;
            break;
          case 'Number of Seats':
            vehicleInfo.seats = item.Value;
            break;
          case 'Curb Weight (pounds)':
            vehicleInfo.curbWeight = item.Value;
            break;
        }
      }
    });

    // Fetch recalls data with retry logic
    let recallsData = null;
    try {
      if (vehicleInfo.make && vehicleInfo.model && vehicleInfo.year) {
        recallsData = await fetchRecallsDataWithRetry(vehicleInfo.make, vehicleInfo.model, vehicleInfo.year);
      }
    } catch (error) {
      console.warn('Failed to fetch recalls data after retries:', error);
    }

    // Fetch MarketCheck data (server-side only)
    let marketData = null;
    try {
      const marketCheckResult = await getCompleteMarketDataServerSide(vin, vehicleInfo);
      if (marketCheckResult.success) {
        marketData = marketCheckResult.data;
      } else {
        console.log('MarketCheck data fetch failed:', marketCheckResult.error);
      }
    } catch (error) {
      console.error('MarketCheck error:', error);
    }

    // Map API response to match frontend expectations
    const mappedVehicleInfo = {
      // Basic fields
      make: vehicleInfo.make,
      manufacturer: vehicleInfo.manufacturer,
      model: vehicleInfo.model,
      year: vehicleInfo.year,
      plantCity: vehicleInfo.plantCity,
      vehicleType: vehicleInfo.vehicleType,
      plantCountry: vehicleInfo.plantCountry,
      bodyClass: vehicleInfo.bodyClass,
      doors: vehicleInfo.doors,
      seats: vehicleInfo.seats,
      trim: vehicleInfo.trim,

      // Engine fields - comprehensive mapping
      engine: vehicleInfo.engineModel ||
              (vehicleInfo.cylinders && vehicleInfo.engineConfiguration ?
                `${vehicleInfo.cylinders} Cylinder ${vehicleInfo.engineConfiguration}` :
                vehicleInfo.cylinders ? `${vehicleInfo.cylinders} Cylinder` :
                vehicleInfo.engineConfiguration || null),
      cylinders: vehicleInfo.cylinders,
      engineConfiguration: vehicleInfo.engineConfiguration,
      engineModel: vehicleInfo.engineModel,
      engineManufacturer: vehicleInfo.engineManufacturer,

      // Displacement fields
      displacementCC: vehicleInfo.engineDisplacementCC,
      displacementCI: vehicleInfo.engineDisplacementCI,
      displacementL: vehicleInfo.engineDisplacementL,

      // Power fields
      horsepowerFrom: vehicleInfo.engineBrakeHpFrom,
      horsepowerTo: vehicleInfo.engineBrakeHpTo,

      // Fuel and drivetrain
      fuelType: vehicleInfo.fuelType,
      transmission: vehicleInfo.transmission,
      driveType: vehicleInfo.driveType,
      drivetrain: vehicleInfo.driveType, // Frontend expects both

      // Weight and capacity
      curbWeight: vehicleInfo.curbWeight
    };

    const response = {
      success: true,
      vin: vin,
      vehicleInfo: mappedVehicleInfo,
      recalls: recallsData,
      marketData: marketData,
      source: 'NHTSA',
      fetchedAt: new Date().toISOString()
    };

    console.log('✅ Vehicle info processed successfully');
    return NextResponse.json(response);

  } catch (error) {
    console.error('❌ Vehicle info processing failed:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to process vehicle information'
    }, { status: 500 });
  }
}
```

### Retry Logic Implementation

```javascript
/**
 * Fetch NHTSA data with retry logic and exponential backoff
 */
async function fetchNHTSADataWithRetry(vin, maxRetries = 3) {
  let lastError;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`🔍 NHTSA API attempt ${attempt}/${maxRetries} for VIN: ${vin}`);

      const controller = new AbortController();
      // Increase timeout for each retry: 10s, 20s, 30s
      const timeout = 10000 * attempt;
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      const response = await fetch(
        `https://vpic.nhtsa.dot.gov/api/vehicles/DecodeVin/${vin}?format=json`,
        {
          signal: controller.signal,
          headers: {
            'User-Agent': 'FazeNAuto-VehicleLookup/1.0',
            'Accept': 'application/json',
            'Cache-Control': 'no-cache'
          }
        }
      );

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`NHTSA API HTTP error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log(`✅ NHTSA API success on attempt ${attempt}`);

      // Debug: Log available fields for troubleshooting
      if (data.Results) {
        console.log('🔍 Available NHTSA fields:', data.Results.map(r => r.Variable).filter(v => v).sort());
      }

      return data;

    } catch (error) {
      lastError = error;
      console.warn(`⚠️ NHTSA API attempt ${attempt} failed:`, error.message);

      // Don't retry on certain errors
      if (error.message.includes('400') || error.message.includes('401') || error.message.includes('403')) {
        throw error;
      }

      // Wait before retry (exponential backoff: 1s, 2s, 4s)
      if (attempt < maxRetries) {
        const delay = Math.pow(2, attempt - 1) * 1000;
        console.log(`⏳ Waiting ${delay}ms before retry...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  throw lastError;
}

/**
 * Fetch recalls data with retry logic
 */
async function fetchRecallsDataWithRetry(make, model, year, maxRetries = 2) {
  let lastError;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`🔍 Recalls API attempt ${attempt}/${maxRetries} for ${year} ${make} ${model}`);

      const controller = new AbortController();
      const timeout = 10000 * attempt; // 10s, 20s
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      const response = await fetch(
        `https://api.nhtsa.gov/recalls/recallsByVehicle?make=${encodeURIComponent(make)}&model=${encodeURIComponent(model)}&modelYear=${year}&format=json`,
        {
          signal: controller.signal,
          headers: {
            'User-Agent': 'FazeNAuto-VehicleLookup/1.0',
            'Accept': 'application/json'
          }
        }
      );

      clearTimeout(timeoutId);

      if (response.ok) {
        const recalls = await response.json();
        console.log(`✅ Recalls API success on attempt ${attempt}`);
        return recalls.results || [];
      } else {
        throw new Error(`Recalls API HTTP error: ${response.status} ${response.statusText}`);
      }

    } catch (error) {
      lastError = error;
      console.warn(`⚠️ Recalls API attempt ${attempt} failed:`, error.message);

      // Wait before retry
      if (attempt < maxRetries) {
        const delay = 1000 * attempt; // 1s, 2s
        console.log(`⏳ Waiting ${delay}ms before recalls retry...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  console.warn('❌ All recalls API attempts failed, returning empty array');
  return [];
}
```

### MarketCheck Integration Details

```javascript
/**
 * Server-side MarketCheck API integration
 * This runs on the server where environment variables are available
 */
async function getCompleteMarketDataServerSide(vin, vehicleInfo) {
  const apiKey = process.env.MARKETCHECK_API_KEY;
  const apiSecret = process.env.MARKETCHECK_API_SECRET;

  if (!apiKey || !apiSecret) {
    console.warn('⚠️ MarketCheck API credentials not configured');
    return {
      success: false,
      error: 'MarketCheck API not configured'
    };
  }

  try {
    console.log('🏪 Fetching complete MarketCheck data for VIN:', vin);

    // Get market value
    const marketValue = await getMarketValueServerSide(vin, vehicleInfo, apiKey, apiSecret);

    return {
      success: true,
      data: {
        marketValue: marketValue,
        comparableListings: {
          success: false,
          error: 'Not implemented yet'
        },
        priceHistory: {
          success: false,
          error: 'Not implemented yet'
        }
      }
    };

  } catch (error) {
    console.error('❌ Complete MarketCheck data fetch failed:', error);
    return {
      success: false,
      error: error.message || 'Failed to fetch MarketCheck data'
    };
  }
}

/**
 * Fetch MarketCheck data with multiple endpoints and retry logic
 */
async function fetchMarketCheckDataWithRetry(vin, vehicleData, apiKey, apiSecret, maxRetries = 2) {
  const endpoints = [
    // Primary endpoint: VIN-specific search
    {
      url: `https://mc-api.marketcheck.com/v2/search/car/active`,
      params: {
        api_key: apiKey,
        vin: vin,
        include_relevant_links: 'true'
      }
    },
    // Fallback endpoint: Search by make/model/year if VIN fails
    vehicleData.make && vehicleData.model && vehicleData.year ? {
      url: `https://mc-api.marketcheck.com/v2/search/car/active`,
      params: {
        api_key: apiKey,
        make: vehicleData.make,
        model: vehicleData.model,
        year: vehicleData.year,
        include_relevant_links: 'true'
      }
    } : null
  ].filter(Boolean);

  let lastError;

  for (const endpoint of endpoints) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`🔍 MarketCheck attempt ${attempt}/${maxRetries} with endpoint:`, endpoint.url);

        const params = new URLSearchParams(endpoint.params);

        const controller = new AbortController();
        const timeout = 15000 * attempt; // 15s, 30s
        const timeoutId = setTimeout(() => controller.abort(), timeout);

        const response = await fetch(
          `${endpoint.url}?${params.toString()}`,
          {
            method: 'GET',
            signal: controller.signal,
            headers: {
              'Accept': 'application/json',
              'User-Agent': 'FazeNAuto-MarketCheck/1.0'
            }
          }
        );

        clearTimeout(timeoutId);

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(`MarketCheck API error: ${response.status} - ${errorData.message || response.statusText}`);
        }

        const data = await response.json();

        console.log('✅ MarketCheck market value fetched successfully');

        return {
          success: true,
          data: parseMarketValueResponse(data),
          source: 'MarketCheck',
          fetchedAt: new Date().toISOString()
        };

      } catch (error) {
        lastError = error;
        console.warn(`⚠️ MarketCheck attempt ${attempt} failed:`, error.message);

        // Don't retry on certain errors
        if (error.message.includes('401') || error.message.includes('403')) {
          throw error;
        }

        // Wait before retry
        if (attempt < maxRetries) {
          const delay = 2000 * attempt; // 2s, 4s
          console.log(`⏳ Waiting ${delay}ms before MarketCheck retry...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }
  }

  throw lastError;
}
```

### Frontend Component Examples

#### Vehicle Lookup Page Component
**File:** `apps/frontend/app/vehicle-lookup/page.jsx`

```javascript
'use client';

import { useState } from 'react';
import { useAuth } from '../lib/auth';
import VINScanner from '../components/VINScanner/VINScanner';

export default function PublicVehicleLookupPage() {
  const [vin, setVin] = useState('');
  const [vehicleData, setVehicleData] = useState(null);
  const [recalls, setRecalls] = useState([]);
  const [marketData, setMarketData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showScanner, setShowScanner] = useState(false);

  const { canAccessMarketCheck, isDealer, user } = useAuth();

  const handleVinSubmit = async (e) => {
    e.preventDefault();
    if (!vin.trim()) return;

    setLoading(true);
    setError('');
    setVehicleData(null);
    setRecalls([]);
    setMarketData(null);

    try {
      const response = await fetch('/api/vehicle-info', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ vin: vin.trim().toUpperCase() }),
      });

      const result = await response.json();

      if (result.success) {
        setVehicleData(result.vehicleInfo);
        setRecalls(result.recalls || []);

        // Only show MarketCheck data to authorized users
        if (canAccessMarketCheck() && result.marketData) {
          setMarketData(result.marketData);
        }
      } else {
        setError(result.error || 'Failed to decode VIN');
      }
    } catch (err) {
      console.error('VIN lookup error:', err);
      setError('Network error - please try again');
    } finally {
      setLoading(false);
    }
  };

  const handleVINDetected = (detectedVin) => {
    setVin(detectedVin);
    setShowScanner(false);
  };

  return (
    <div className="vehicle-lookup-page">
      <div className="container">
        <h1>Vehicle Information Lookup</h1>
        <p>Scan or enter a VIN to get detailed vehicle information</p>

        <form onSubmit={handleVinSubmit} className="vin-form">
          <div className="input-group">
            <input
              type="text"
              value={vin}
              onChange={(e) => setVin(e.target.value.toUpperCase())}
              placeholder="Enter 17-character VIN"
              maxLength={17}
              className="formInput"
              disabled={loading}
            />
            <button
              type="button"
              onClick={() => setShowScanner(true)}
              className="scan-button"
              disabled={loading}
            >
              📷 Scan VIN
            </button>
            <button
              type="submit"
              disabled={loading || vin.length !== 17}
              className="submit-button"
            >
              {loading ? 'Decoding...' : 'Decode VIN'}
            </button>
          </div>
        </form>

        {error && (
          <div className="error-message">
            {error}
          </div>
        )}

        {vehicleData && (
          <div className="vehicle-info-display">
            <h2>Decoded Vehicle Information: ({vin})</h2>

            {/* Basic Information */}
            <div className="info-section">
              <h3>Basic Information</h3>
              <div className="info-grid">
                <div className="info-item">
                  <label>Make:</label>
                  <span>{vehicleData.make || 'N/A'}</span>
                </div>
                <div className="info-item">
                  <label>Model:</label>
                  <span>{vehicleData.model || 'N/A'}</span>
                </div>
                <div className="info-item">
                  <label>Year:</label>
                  <span>{vehicleData.year || 'N/A'}</span>
                </div>
                <div className="info-item">
                  <label>Trim:</label>
                  <span>{vehicleData.trim || 'N/A'}</span>
                </div>
                <div className="info-item">
                  <label>Body Class:</label>
                  <span>{vehicleData.bodyClass || 'N/A'}</span>
                </div>
              </div>
            </div>

            {/* Engine & Performance */}
            <div className="info-section">
              <h3>Engine & Performance</h3>
              <div className="info-grid">
                <div className="info-item">
                  <label>Engine:</label>
                  <span>{vehicleData.engine || 'N/A'}</span>
                </div>
                <div className="info-item">
                  <label>Cylinders:</label>
                  <span>{vehicleData.cylinders || 'N/A'}</span>
                </div>
                <div className="info-item">
                  <label>Displacement:</label>
                  <span>{vehicleData.displacementCC ? `${vehicleData.displacementCC} CC` : 'N/A'}</span>
                </div>
                <div className="info-item">
                  <label>Displacement (CI):</label>
                  <span>{vehicleData.displacementCI || 'N/A'}</span>
                </div>
                <div className="info-item">
                  <label>Engine Brake (hp) From:</label>
                  <span>{vehicleData.horsepowerFrom || 'N/A'}</span>
                </div>
                <div className="info-item">
                  <label>Fuel Type:</label>
                  <span>{vehicleData.fuelType || 'N/A'}</span>
                </div>
              </div>
            </div>

            {/* Drivetrain & Transmission */}
            <div className="info-section">
              <h3>Drivetrain & Transmission</h3>
              <div className="info-grid">
                <div className="info-item">
                  <label>Transmission:</label>
                  <span>{vehicleData.transmission || 'N/A'}</span>
                </div>
                <div className="info-item">
                  <label>Drive Type:</label>
                  <span>{vehicleData.driveType || 'N/A'}</span>
                </div>
                <div className="info-item">
                  <label>Doors:</label>
                  <span>{vehicleData.doors || 'N/A'}</span>
                </div>
                <div className="info-item">
                  <label>Manufacturer:</label>
                  <span>{vehicleData.manufacturer || 'N/A'}</span>
                </div>
                <div className="info-item">
                  <label>Plant City:</label>
                  <span>{vehicleData.plantCity || 'N/A'}</span>
                </div>
                <div className="info-item">
                  <label>Plant Country:</label>
                  <span>{vehicleData.plantCountry || 'N/A'}</span>
                </div>
              </div>
            </div>

            {/* MarketCheck Data - Dealer Only */}
            {canAccessMarketCheck() && marketData && (
              <div className="info-section market-data">
                <h3>Market Data (Dealer Only)</h3>
                <div className="market-info">
                  {marketData.marketValue && (
                    <div className="market-value">
                      <h4>Market Value</h4>
                      <p>Average Price: ${marketData.marketValue.averagePrice}</p>
                      <p>Price Range: ${marketData.marketValue.minPrice} - ${marketData.marketValue.maxPrice}</p>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Vehicle History & Recalls */}
        {recalls && recalls.length > 0 && (
          <div className="recalls-section">
            <h2>Vehicle History & Recalls</h2>
            <div className="recalls-list">
              {recalls.map((recall, index) => (
                <div key={index} className="recall-item">
                  <h4>{recall.Component}</h4>
                  <p><strong>Date:</strong> {recall.ReportReceivedDate}</p>
                  <p><strong>NHTSA ID:</strong> {recall.NHTSAActionNumber}</p>
                  <p><strong>Summary:</strong> {recall.Summary}</p>
                  <p><strong>Consequence:</strong> {recall.Consequence}</p>
                  <p><strong>Remedy:</strong> {recall.Remedy}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* VIN Scanner Modal */}
        {showScanner && (
          <div className="scanner-modal">
            <VINScanner
              onVINDetected={handleVINDetected}
              onClose={() => setShowScanner(false)}
            />
          </div>
        )}
      </div>
    </div>
  );
}
```

#### VIN Scanner Component
**File:** `apps/frontend/components/VINScanner/VINScanner.jsx`

```javascript
'use client';

import { useState, useRef, useEffect } from 'react';
import styles from './VINScanner.module.css';

const VINScanner = ({ onVINDetected, onClose }) => {
  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  const [isScanning, setIsScanning] = useState(false);
  const [error, setError] = useState('');
  const [detectedText, setDetectedText] = useState('');
  const [stream, setStream] = useState(null);

  useEffect(() => {
    startCamera();
    return () => {
      stopCamera();
    };
  }, []);

  const startCamera = async () => {
    try {
      setError('');
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: 'environment', // Use back camera on mobile
          width: { ideal: 1280 },
          height: { ideal: 720 }
        }
      });

      setStream(mediaStream);
      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;
      }
    } catch (err) {
      console.error('Camera access error:', err);
      setError('Unable to access camera. Please ensure camera permissions are granted.');
    }
  };

  const stopCamera = () => {
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
      setStream(null);
    }
  };

  const captureFrame = () => {
    if (!videoRef.current || !canvasRef.current) return null;

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const context = canvas.getContext('2d');

    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    context.drawImage(video, 0, 0, canvas.width, canvas.height);
    return canvas.toDataURL('image/jpeg', 0.8);
  };

  const scanForVIN = async () => {
    if (isScanning) return;

    setIsScanning(true);
    setError('');

    try {
      const imageData = captureFrame();
      if (!imageData) {
        throw new Error('Failed to capture image');
      }

      // Here you would integrate with an OCR service
      // For now, we'll simulate VIN detection
      const mockVIN = 'WAUKFRFF7F1025016'; // Mock detected VIN

      // Validate VIN format
      const vinPattern = /^[A-HJ-NPR-Z0-9]{17}$/;
      if (vinPattern.test(mockVIN)) {
        setDetectedText(mockVIN);
        onVINDetected(mockVIN);
      } else {
        setError('No valid VIN detected. Please try again.');
      }

    } catch (err) {
      console.error('VIN scanning error:', err);
      setError('Failed to scan VIN. Please try again.');
    } finally {
      setIsScanning(false);
    }
  };

  return (
    <div className={styles.scannerContainer}>
      <div className={styles.scannerModal}>
        <div className={styles.scannerHeader}>
          <h3>Scan VIN Code</h3>
          <button onClick={onClose} className={styles.closeButton}>×</button>
        </div>

        <div className={styles.scannerContent}>
          {error ? (
            <div className={styles.errorMessage}>
              <p>{error}</p>
              <button onClick={startCamera} className={styles.retryButton}>
                Try Again
              </button>
            </div>
          ) : (
            <>
              <div className={styles.videoContainer}>
                <video
                  ref={videoRef}
                  autoPlay
                  playsInline
                  muted
                  className={styles.video}
                />
                <canvas
                  ref={canvasRef}
                  style={{ display: 'none' }}
                />
                <div className={styles.scanOverlay}>
                  <div className={styles.scanFrame}></div>
                  <p>Position VIN within the frame</p>
                </div>
              </div>

              <div className={styles.scannerControls}>
                <button
                  onClick={scanForVIN}
                  disabled={isScanning}
                  className={styles.scanButton}
                >
                  {isScanning ? 'Scanning...' : 'Scan VIN'}
                </button>

                {detectedText && (
                  <div className={styles.detectedText}>
                    <p>Detected: {detectedText}</p>
                  </div>
                )}
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default VINScanner;
```

### Utility Functions

#### VIN Decoder Utility
**File:** `apps/frontend/utils/vinDecoder.js`

```javascript
/**
 * Decode VIN using NHTSA API
 * @param {string} vin - 17-character VIN
 * @returns {Promise<Object>} Decoded vehicle information
 */
export async function decodeVIN(vin) {
  if (!vin || vin.length !== 17) {
    return {
      success: false,
      error: 'VIN must be exactly 17 characters long'
    };
  }

  if (!validateVIN(vin)) {
    return {
      success: false,
      error: 'Invalid VIN format'
    };
  }

  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000);

    const response = await fetch(
      `https://vpic.nhtsa.dot.gov/api/vehicles/DecodeVin/${vin}?format=json`,
      {
        signal: controller.signal,
        headers: {
          'User-Agent': 'FazeNAuto-VINDecoder/1.0',
          'Accept': 'application/json'
        }
      }
    );

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`NHTSA API error: ${response.status}`);
    }

    const data = await response.json();

    if (!data.Results || data.Results.length === 0) {
      return {
        success: false,
        error: 'No vehicle data found for this VIN'
      };
    }

    const vehicleInfo = parseNHTSAResponse(data.Results);

    return {
      success: true,
      data: vehicleInfo,
      source: 'NHTSA',
      fetchedAt: new Date().toISOString()
    };

  } catch (error) {
    console.error('VIN decode error:', error);

    let errorMessage = 'Failed to decode VIN';
    if (error.name === 'AbortError') {
      errorMessage = 'Request timeout - NHTSA service is slow. Please try again.';
    } else if (error.message.includes('fetch failed')) {
      errorMessage = 'Network error - please check your connection.';
    }

    return {
      success: false,
      error: errorMessage
    };
  }
}

/**
 * Validate VIN format
 * @param {string} vin - VIN to validate
 * @returns {boolean} True if valid VIN format
 */
export function validateVIN(vin) {
  if (!vin || typeof vin !== 'string') return false;

  // VIN must be exactly 17 characters
  if (vin.length !== 17) return false;

  // VIN cannot contain I, O, or Q
  const vinPattern = /^[A-HJ-NPR-Z0-9]{17}$/;
  return vinPattern.test(vin.toUpperCase());
}

/**
 * Parse NHTSA API response into structured data
 * @param {Array} results - NHTSA Results array
 * @returns {Object} Structured vehicle information
 */
function parseNHTSAResponse(results) {
  const vehicleInfo = {};

  results.forEach(item => {
    if (item.Value && item.Value !== 'Not Applicable' && item.Value !== '' && item.Value !== 'N/A') {
      switch (item.Variable) {
        case 'Make':
          vehicleInfo.make = item.Value;
          break;
        case 'Model':
          vehicleInfo.model = item.Value;
          break;
        case 'Model Year':
          vehicleInfo.year = parseInt(item.Value);
          break;
        case 'Vehicle Type':
          vehicleInfo.vehicleType = item.Value;
          break;
        case 'Body Class':
          vehicleInfo.bodyClass = item.Value;
          break;
        case 'Engine Number of Cylinders':
          vehicleInfo.cylinders = item.Value;
          break;
        case 'Engine Configuration':
          vehicleInfo.engineConfiguration = item.Value;
          break;
        case 'Fuel Type - Primary':
          vehicleInfo.fuelType = item.Value;
          break;
        case 'Transmission Style':
          vehicleInfo.transmission = item.Value;
          break;
        case 'Drive Type':
          vehicleInfo.driveType = item.Value;
          break;
        case 'Number of Doors':
          vehicleInfo.doors = item.Value;
          break;
        case 'Number of Seats':
          vehicleInfo.seats = item.Value;
          break;
        case 'Manufacturer Name':
          vehicleInfo.manufacturer = item.Value;
          break;
        case 'Plant City':
          vehicleInfo.plantCity = item.Value;
          break;
        case 'Plant Country':
          vehicleInfo.plantCountry = item.Value;
          break;
        case 'Engine Displacement (CC)':
          vehicleInfo.displacementCC = item.Value;
          break;
        case 'Engine Displacement (CI)':
          vehicleInfo.displacementCI = item.Value;
          break;
        case 'Engine Brake (hp) From':
          vehicleInfo.horsepowerFrom = item.Value;
          break;
        case 'Trim':
          vehicleInfo.trim = item.Value;
          break;
      }
    }
  });

  return vehicleInfo;
}

/**
 * Map VIN decoded data to vehicle form structure
 * @param {Object} vinData - Decoded VIN data
 * @returns {Object} Form-compatible data structure
 */
export function mapVINToFormData(vinData) {
  return {
    make: vinData.make || '',
    model: vinData.model || '',
    year: vinData.year || '',
    trim: vinData.trim || '',
    bodyStyle: vinData.bodyClass || '',
    engine: vinData.engineConfiguration || '',
    cylinders: vinData.cylinders || '',
    displacement: vinData.displacementCC || '',
    horsepower: vinData.horsepowerFrom || '',
    fuelType: vinData.fuelType || '',
    transmission: vinData.transmission || '',
    drivetrain: vinData.driveType || '',
    doors: vinData.doors || '',
    seats: vinData.seats || '',
    manufacturer: vinData.manufacturer || '',
    plantCity: vinData.plantCity || '',
    plantCountry: vinData.plantCountry || ''
  };
}
```

#### Authentication Utilities
**File:** `apps/frontend/lib/auth.js`

```javascript
import { useContext, createContext, useState, useEffect } from 'react';

const AuthContext = createContext();

export function AuthProvider({ children }) {
  const [user, setUser] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const response = await fetch('/api/auth/me');
      if (response.ok) {
        const userData = await response.json();
        setUser(userData);
        setIsAuthenticated(true);
      }
    } catch (error) {
      console.error('Auth check failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const isAdmin = () => {
    return user?.role === 'admin';
  };

  const isDealer = () => {
    return user?.role === 'dealer' || user?.role === 'admin';
  };

  const canAccessMarketCheck = () => {
    // Only dealers and admins can access MarketCheck to preserve API quota
    return isDealer();
  };

  const hasRole = (role) => {
    return user?.role === role;
  };

  const value = {
    user,
    isAuthenticated,
    loading,
    isAdmin,
    isDealer,
    canAccessMarketCheck,
    hasRole,
    setUser,
    setIsAuthenticated
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

/**
 * Check if user can access MarketCheck features
 * @param {Object} user - User object
 * @returns {boolean} True if user can access MarketCheck
 */
export function canAccessMarketCheck(user) {
  if (!user) return false;
  return user.role === 'dealer' || user.role === 'admin';
}

/**
 * Validate if user is authorized for dealer features
 * @param {Object} user - User object
 * @returns {boolean} True if user has dealer access
 */
export function isDealerAuthorized(user) {
  if (!user) return false;

  const authorizedEmails = process.env.AUTHORIZED_EMAILS?.split(',') || [];
  const hasAuthorizedEmail = authorizedEmails.includes(user.email);
  const hasValidRole = user.role === 'dealer' || user.role === 'admin';

  return hasAuthorizedEmail && hasValidRole;
}
```

## Configuration Files

### Environment Variables Template
**File:** `apps/frontend/.env.example`

```bash
# MarketCheck API Configuration
MARKETCHECK_API_KEY=your_api_key_here
MARKETCHECK_API_SECRET=your_api_secret_here

# Authentication
ADMIN_SECRET=your_admin_secret
AUTHORIZED_EMAILS=<EMAIL>,<EMAIL>

# Database
MONGODB_URI=mongodb://localhost:27017/fazenauto

# Next.js Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_nextauth_secret
```

### API Endpoints Summary

| Service | Endpoint | Method | Purpose |
|---------|----------|---------|---------|
| NHTSA VIN Decoder | `https://vpic.nhtsa.dot.gov/api/vehicles/DecodeVin/{vin}?format=json` | GET | Decode VIN to vehicle specs |
| NHTSA Recalls | `https://api.nhtsa.gov/recalls/recallsByVehicle?make={make}&model={model}&modelYear={year}&format=json` | GET | Get vehicle recalls |
| MarketCheck Search | `https://mc-api.marketcheck.com/v2/search/car/active?api_key={key}&vin={vin}` | GET | Get market data by VIN |
| Internal Vehicle Info | `/api/vehicle-info` | POST | Orchestrate all vehicle data |

### Error Handling Patterns

```javascript
// Standard error response format
{
  success: false,
  error: "User-friendly error message",
  details: "Technical error details (optional)",
  code: "ERROR_CODE (optional)"
}

// Success response format
{
  success: true,
  data: { /* response data */ },
  source: "NHTSA|MarketCheck|Internal",
  fetchedAt: "2024-01-01T00:00:00.000Z"
}
```

This comprehensive documentation covers all aspects of the VIN decoding and vehicle information system in the FazeNAuto project, including API integrations, authentication, error handling, and component implementations.
```
