/**
 * VIN Decoder Utility
 * Decodes Vehicle Identification Numbers using NHTSA API
 */

const NHTSA_API_BASE = 'https://vpic.nhtsa.dot.gov/api/vehicles';

/**
 * Decode VIN using NHTSA API
 * @param {String} vin - Vehicle Identification Number
 * @returns {Promise<Object>} - Decoded vehicle information
 */
export async function decodeVIN(vin) {
  if (!vin || vin.length !== 17) {
    return {
      success: false,
      error: 'Invalid VIN format. VIN must be 17 characters long.'
    };
  }

  try {
    // Clean VIN (remove spaces, convert to uppercase)
    const cleanVIN = vin.replace(/\s/g, '').toUpperCase();
    
    // Validate VIN format (basic check)
    if (!/^[A-HJ-NPR-Z0-9]{17}$/.test(cleanVIN)) {
      return {
        success: false,
        error: 'Invalid VIN format. VIN contains invalid characters.'
      };
    }

    console.log('🔍 Decoding VIN:', cleanVIN);

    // Call NHTSA API with timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout

    const response = await fetch(
      `${NHTSA_API_BASE}/DecodeVin/${cleanVIN}?format=json`,
      {
        method: 'GET',
        signal: controller.signal,
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'FazeNAuto-VehicleLookup/1.0'
        }
      }
    );

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`NHTSA API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    if (!data.Results || data.Results.length === 0) {
      return {
        success: false,
        error: 'No vehicle information found for this VIN.'
      };
    }

    // Parse NHTSA response
    const vehicleData = parseNHTSAResponse(data.Results);
    
    console.log('✅ VIN decoded successfully:', vehicleData);

    return {
      success: true,
      data: vehicleData,
      source: 'NHTSA',
      decodedAt: new Date().toISOString()
    };

  } catch (error) {
    console.error('❌ VIN decoding failed:', error);

    // Provide more specific error messages
    let errorMessage = 'Failed to decode VIN';
    if (error.name === 'AbortError') {
      errorMessage = 'NHTSA API timeout - please try again later';
    } else if (error.message.includes('fetch failed')) {
      errorMessage = 'Network connection issue - NHTSA API unavailable';
    } else if (error.message.includes('NHTSA API error')) {
      errorMessage = error.message;
    }

    return {
      success: false,
      error: errorMessage
    };
  }
}

/**
 * Parse NHTSA API response into structured vehicle data
 * @param {Array} results - NHTSA API results array
 * @returns {Object} - Structured vehicle data
 */
function parseNHTSAResponse(results) {
  const vehicleData = {};
  
  // Map NHTSA fields to our structure
  const fieldMapping = {
    'Make': 'make',
    'Model': 'model',
    'Model Year': 'year',
    'Vehicle Type': 'vehicleType',
    'Body Class': 'bodyClass',
    // Engine and Cylinders - try multiple field names
    'Engine Number of Cylinders': 'cylinders',
    'Number of Cylinders': 'cylinders',
    'Cylinders': 'cylinders',
    'Engine Configuration': 'engineConfiguration',
    'Engine Model': 'engineModel',
    'Engine Power (kW)': 'enginePowerKW',
    'Engine Stroke Cycles': 'strokeCycles',
    'Displacement (L)': 'displacement',
    'Displacement (CC)': 'displacementCC',
    'Displacement (CI)': 'displacementCI',
    'Fuel Type - Primary': 'fuelType',
    'Fuel Type - Secondary': 'fuelTypeSecondary',
    // Transmission - try multiple field names
    'Transmission Style': 'transmission',
    'Transmission': 'transmission',
    'Transmission Speeds': 'transmissionSpeeds',
    'Drive Type': 'driveType',
    'Doors': 'doors',
    'Number of Doors': 'doors',
    'Number of Seats': 'seats',
    'Manufacturer Name': 'manufacturer',
    'Plant City': 'plantCity',
    'Plant Country': 'plantCountry',
    'Plant State': 'plantState',
    'Series': 'series',
    'Trim': 'trim',
    'Vehicle Descriptor': 'vehicleDescriptor',
    'Gross Vehicle Weight Rating From': 'gvwrFrom',
    'Gross Vehicle Weight Rating To': 'gvwrTo',
    'Curb Weight (pounds)': 'curbWeight',
    'Wheelbase (inches)': 'wheelbase',
    'Track Width (inches)': 'trackWidth',
    'Overall Length (inches)': 'overallLength',
    'Overall Width (inches)': 'overallWidth',
    'Overall Height (inches)': 'overallHeight',
    'Fuel Delivery / Fuel Injection Type': 'fuelInjectionType',
    'Engine Brake (hp) From': 'horsepowerFrom',
    'Engine Brake (hp) To': 'horsepowerTo',
    'Electrification Level': 'electrificationLevel',
    'Other Engine Info': 'otherEngineInfo',
    'Turbo': 'turbo',
    'Top Speed (MPH)': 'topSpeed',
    'Engine Stroke Cycles': 'strokeCycles',
    'Cooling Type': 'coolingType',
    'Number of Battery Cells per Module': 'batteryCells',
    'Battery Current (Amps) From': 'batteryCurrentFrom',
    'Battery Current (Amps) To': 'batteryCurrentTo',
    'Battery Energy (kWh) From': 'batteryEnergyFrom',
    'Battery Energy (kWh) To': 'batteryEnergyTo',
    'Battery Voltage (Volts) From': 'batteryVoltageFrom',
    'Battery Voltage (Volts) To': 'batteryVoltageTo'
  };

  // Process each result
  results.forEach(result => {
    const variable = result.Variable;
    const value = result.Value;

    if (value && value !== 'Not Applicable' && value !== '' && fieldMapping[variable]) {
      vehicleData[fieldMapping[variable]] = value;
    }
  });

  // Additional processing for fields that might have different names
  // Try to find doors field with different variations
  if (!vehicleData.doors) {
    console.log('🔍 Doors not found in main mapping, searching alternatives...');
    const doorResult = results.find(r =>
      r.Variable && r.Value && r.Value !== 'Not Applicable' && r.Value !== '' &&
      (r.Variable.toLowerCase().includes('door') || r.Variable === 'Doors')
    );
    if (doorResult) {
      vehicleData.doors = doorResult.Value;
      console.log('🔍 Found doors field with alternative name:', doorResult.Variable, '=', doorResult.Value);
    } else {
      console.log('🔍 No doors field found at all. All door-related fields:');
      const allDoorFields = results.filter(r => r.Variable.toLowerCase().includes('door'));
      allDoorFields.forEach(f => console.log('  ', f.Variable, '=', f.Value));
    }
  }

  // Try to find cylinders field with different variations
  if (!vehicleData.cylinders) {
    console.log('🔍 Cylinders not found in main mapping, searching alternatives...');
    const cylinderResult = results.find(r =>
      r.Variable && r.Value && r.Value !== 'Not Applicable' && r.Value !== '' &&
      r.Variable.toLowerCase().includes('cylinder')
    );
    if (cylinderResult) {
      vehicleData.cylinders = cylinderResult.Value;
      console.log('🔍 Found cylinders field with alternative name:', cylinderResult.Variable, '=', cylinderResult.Value);
    } else {
      console.log('🔍 No cylinders field found at all. All cylinder-related fields:');
      const allCylinderFields = results.filter(r => r.Variable.toLowerCase().includes('cylinder'));
      allCylinderFields.forEach(f => console.log('  ', f.Variable, '=', f.Value));
    }
  }

  // Try to find body class field with different variations
  if (!vehicleData.bodyClass) {
    const bodyResult = results.find(r =>
      r.Variable && r.Value && r.Value !== 'Not Applicable' && r.Value !== '' &&
      (r.Variable.toLowerCase().includes('body') && r.Variable.toLowerCase().includes('class'))
    );
    if (bodyResult) {
      vehicleData.bodyClass = bodyResult.Value;
      console.log('🔍 Found body class field with alternative name:', bodyResult.Variable, '=', bodyResult.Value);
    }
  }

  // Fallback: Try to infer doors from body class if not found
  if (!vehicleData.doors && vehicleData.bodyClass) {
    const bodyClass = vehicleData.bodyClass.toLowerCase();
    let inferredDoors = '';

    if (bodyClass.includes('sedan') || bodyClass.includes('saloon')) {
      inferredDoors = '4';
    } else if (bodyClass.includes('coupe') || bodyClass.includes('convertible')) {
      inferredDoors = '2';
    } else if (bodyClass.includes('suv') || bodyClass.includes('crossover') || bodyClass.includes('wagon')) {
      inferredDoors = '4'; // Most SUVs and wagons are 4-door
    } else if (bodyClass.includes('truck') || bodyClass.includes('pickup')) {
      inferredDoors = '2'; // Most trucks are 2-door (cab), but could be 4
    }

    if (inferredDoors) {
      vehicleData.doors = inferredDoors;
      console.log('🔍 Inferred doors from body class:', bodyClass, '→', inferredDoors);
    }
  }

  // Fallback: Try to infer cylinders from engine info if not found
  if (!vehicleData.cylinders) {
    // Look for engine-related fields that might contain cylinder info
    const engineFields = results.filter(r =>
      r.Variable && r.Value && r.Value !== 'Not Applicable' && r.Value !== '' &&
      r.Variable.toLowerCase().includes('engine')
    );

    for (const field of engineFields) {
      const value = field.Value.toLowerCase();
      // Look for patterns like "v8", "v6", "4-cylinder", etc.
      const cylinderMatch = value.match(/(\d+)[\s-]?cyl|v(\d+)|(\d+)[\s-]?cylinder/i);
      if (cylinderMatch) {
        const cylinders = cylinderMatch[1] || cylinderMatch[2] || cylinderMatch[3];
        vehicleData.cylinders = cylinders;
        console.log('🔍 Inferred cylinders from engine field:', field.Variable, '=', field.Value, '→', cylinders);
        break;
      }
    }
  }

  // Debug logging to help troubleshoot missing fields
  console.log('🔍 VIN Decoder - Raw NHTSA Results:', results.length, 'fields');
  console.log('🔍 VIN Decoder - Available Fields:', results.map(r => r.Variable).sort());
  console.log('🔍 VIN Decoder - Transmission Fields:', results.filter(r => r.Variable.toLowerCase().includes('transmission')));
  console.log('🔍 VIN Decoder - Cylinder Fields:', results.filter(r => r.Variable.toLowerCase().includes('cylinder')));
  console.log('🔍 VIN Decoder - Door Fields:', results.filter(r => r.Variable.toLowerCase().includes('door')));
  console.log('🔍 VIN Decoder - Body Fields:', results.filter(r => r.Variable.toLowerCase().includes('body')));
  console.log('🔍 VIN Decoder - Parsed Vehicle Data:', vehicleData);

  // Post-process and clean up data
  const cleanedData = cleanVehicleData(vehicleData);

  // Map to expected field names for compatibility with display components
  return {
    ...cleanedData,
    // Map new field names to expected field names
    engineCylinders: cleanedData.cylinders,
    engineLiters: cleanedData.displacement,
    engine: cleanedData.engine || cleanedData.engineModel || cleanedData.engineConfiguration || generateEngineDescription(cleanedData),
  };
}

/**
 * Clean and standardize vehicle data
 * @param {Object} rawData - Raw parsed data
 * @returns {Object} - Cleaned vehicle data
 */
function cleanVehicleData(rawData) {
  const cleaned = { ...rawData };

  // Convert year to number
  if (cleaned.year) {
    cleaned.year = parseInt(cleaned.year);
  }

  // Convert numeric fields
  const numericFields = ['cylinders', 'doors', 'seats', 'displacement', 'displacementCC', 
                        'curbWeight', 'wheelbase', 'trackWidth', 'overallLength', 
                        'overallWidth', 'overallHeight', 'horsepowerFrom', 'horsepowerTo',
                        'topSpeed', 'batteryCells', 'batteryCurrentFrom', 'batteryCurrentTo',
                        'batteryEnergyFrom', 'batteryEnergyTo', 'batteryVoltageFrom', 'batteryVoltageTo'];
  
  numericFields.forEach(field => {
    if (cleaned[field]) {
      const num = parseFloat(cleaned[field]);
      if (!isNaN(num)) {
        cleaned[field] = num;
      }
    }
  });

  // Standardize transmission values
  if (cleaned.transmission) {
    cleaned.transmission = standardizeTransmission(cleaned.transmission);
  }

  // Standardize fuel type
  if (cleaned.fuelType) {
    cleaned.fuelType = standardizeFuelType(cleaned.fuelType);
  }

  // Standardize drive type
  if (cleaned.driveType) {
    cleaned.driveType = standardizeDriveType(cleaned.driveType);
  }

  // Generate engine description
  if (cleaned.displacement || cleaned.cylinders) {
    cleaned.engine = generateEngineDescription(cleaned);
  }

  // Generate a comprehensive description
  cleaned.description = generateVehicleDescription(cleaned);

  return cleaned;
}

/**
 * Standardize transmission values
 */
function standardizeTransmission(transmission) {
  const trans = transmission.toLowerCase();
  if (trans.includes('automatic') || trans.includes('auto')) {
    return 'Automatic';
  } else if (trans.includes('manual') || trans.includes('stick')) {
    return 'Manual';
  } else if (trans.includes('cvt')) {
    return 'CVT';
  }
  return transmission;
}

/**
 * Standardize fuel type values
 */
function standardizeFuelType(fuelType) {
  const fuel = fuelType.toLowerCase();
  if (fuel.includes('gasoline') || fuel.includes('gas')) {
    return 'Gasoline';
  } else if (fuel.includes('diesel')) {
    return 'Diesel';
  } else if (fuel.includes('electric')) {
    return 'Electric';
  } else if (fuel.includes('hybrid')) {
    return 'Hybrid';
  } else if (fuel.includes('flex') || fuel.includes('e85')) {
    return 'Flex Fuel';
  }
  return fuelType;
}

/**
 * Standardize drive type values
 */
function standardizeDriveType(driveType) {
  const drive = driveType.toLowerCase();
  if (drive.includes('front') || drive.includes('fwd')) {
    return 'Front-Wheel Drive';
  } else if (drive.includes('rear') || drive.includes('rwd')) {
    return 'Rear-Wheel Drive';
  } else if (drive.includes('all') || drive.includes('awd')) {
    return 'All-Wheel Drive';
  } else if (drive.includes('4wd') || drive.includes('4x4')) {
    return '4-Wheel Drive';
  }
  return driveType;
}

/**
 * Generate engine description
 */
function generateEngineDescription(vehicleData) {
  const parts = [];
  
  if (vehicleData.displacement) {
    parts.push(`${vehicleData.displacement}L`);
  }
  
  if (vehicleData.cylinders) {
    parts.push(`${vehicleData.cylinders}-Cylinder`);
  }
  
  if (vehicleData.fuelType && vehicleData.fuelType !== 'Gasoline') {
    parts.push(vehicleData.fuelType);
  }
  
  if (vehicleData.turbo && vehicleData.turbo.toLowerCase() === 'yes') {
    parts.push('Turbo');
  }
  
  return parts.length > 0 ? parts.join(' ') : 'Engine';
}

/**
 * Generate vehicle description
 */
function generateVehicleDescription(vehicleData) {
  const parts = [];
  
  if (vehicleData.year) parts.push(vehicleData.year);
  if (vehicleData.make) parts.push(vehicleData.make);
  if (vehicleData.model) parts.push(vehicleData.model);
  if (vehicleData.trim) parts.push(vehicleData.trim);
  
  return parts.join(' ');
}

/**
 * Simple VIN validation (format check)
 * @param {String} vin - VIN to validate
 * @returns {Boolean} - Whether VIN format is valid
 */
export function validateVIN(vin) {
  if (!vin || vin.length !== 17) return false;

  // Clean VIN (remove spaces, convert to uppercase)
  const cleanVIN = vin.replace(/\s/g, '').toUpperCase();

  // Check format (no I, O, Q allowed)
  return /^[A-HJ-NPR-Z0-9]{17}$/.test(cleanVIN);
}

/**
 * Validate VIN checksum (basic validation)
 * @param {String} vin - VIN to validate
 * @returns {Boolean} - Whether VIN checksum is valid
 */
export function validateVINChecksum(vin) {
  if (!vin || vin.length !== 17) return false;

  const weights = [8, 7, 6, 5, 4, 3, 2, 10, 0, 9, 8, 7, 6, 5, 4, 3, 2];
  const values = {
    'A': 1, 'B': 2, 'C': 3, 'D': 4, 'E': 5, 'F': 6, 'G': 7, 'H': 8,
    'J': 1, 'K': 2, 'L': 3, 'M': 4, 'N': 5, 'P': 7, 'R': 9, 'S': 2,
    'T': 3, 'U': 4, 'V': 5, 'W': 6, 'X': 7, 'Y': 8, 'Z': 9,
    '0': 0, '1': 1, '2': 2, '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9
  };

  let sum = 0;
  for (let i = 0; i < 17; i++) {
    if (i === 8) continue; // Skip check digit position
    const char = vin[i];
    const value = values[char];
    if (value === undefined) return false;
    sum += value * weights[i];
  }

  const checkDigit = sum % 11;
  const expectedCheckDigit = checkDigit === 10 ? 'X' : checkDigit.toString();

  return vin[8] === expectedCheckDigit;
}

/**
 * Normalize body class values to match dropdown options
 */
function normalizeBodyClass(bodyClass) {
  if (!bodyClass) return '';

  const normalized = bodyClass.toLowerCase();

  // Handle common variations
  if (normalized.includes('sedan')) return 'Sedan';
  if (normalized.includes('suv')) return 'SUV';
  if (normalized.includes('truck')) return 'Truck';
  if (normalized.includes('coupe')) return 'Coupe';
  if (normalized.includes('convertible')) return 'Convertible';
  if (normalized.includes('wagon')) return 'Wagon';
  if (normalized.includes('hatchback')) return 'Hatchback';
  if (normalized.includes('van')) return 'Van';
  if (normalized.includes('pickup')) return 'Pickup';

  // Return original if no match found
  return bodyClass;
}

/**
 * Normalize door count to string format
 */
function normalizeDoors(doors) {
  if (!doors) return '';

  console.log('🔍 Normalizing doors input:', doors, typeof doors);

  // Convert to string and ensure it's a valid number
  const doorCount = parseInt(doors);
  if (isNaN(doorCount)) {
    console.log('🔍 Door count is NaN, returning empty string');
    return '';
  }

  const result = doorCount.toString();
  console.log('🔍 Normalized doors result:', result);
  return result;
}

/**
 * Normalize cylinder count to string format
 */
function normalizeCylinders(cylinders) {
  if (!cylinders) return '';

  console.log('🔍 Normalizing cylinders input:', cylinders, typeof cylinders);

  // Convert to string and ensure it's a valid number
  const cylinderCount = parseInt(cylinders);
  if (isNaN(cylinderCount)) {
    console.log('🔍 Cylinder count is NaN, returning empty string');
    return '';
  }

  const result = cylinderCount.toString();
  console.log('🔍 Normalized cylinders result:', result);
  return result;
}

/**
 * Map VIN decoded data to form data structure
 * @param {Object} vinData - Decoded VIN data
 * @returns {Object} - Form-compatible data structure
 */
export function mapVINToFormData(vinData) {
  const mapped = {
    make: vinData.make || '',
    model: vinData.model || '',
    year: vinData.year || '',
    engine: vinData.engine || '',
    transmission: vinData.transmission || '',
    driveline: vinData.driveType || '',
    fuelType: vinData.fuelType || '',
    bodyClass: normalizeBodyClass(vinData.bodyClass),
    doors: normalizeDoors(vinData.doors),
    cylinders: normalizeCylinders(vinData.cylinders),
    // Additional fields that Vehicle Info shows
    engineCylinders: normalizeCylinders(vinData.cylinders),
    engineLiters: vinData.displacement || '',
    driveType: vinData.driveType || '',
    // New NHTSA fields
    plantCity: vinData.plantCity || '',
    plantCountry: vinData.plantCountry || '',
    displacementCC: vinData.displacementCC || '',
    displacementCI: vinData.displacementCI || '',
    horsepowerFrom: vinData.horsepowerFrom || ''
  };

  // Debug logging to see the mapping
  console.log('🔍 VIN Mapper - Input data:', {
    bodyClass: vinData.bodyClass,
    doors: vinData.doors,
    cylinders: vinData.cylinders,
    fuelType: vinData.fuelType
  });
  console.log('🔍 VIN Mapper - Mapped data:', {
    bodyClass: mapped.bodyClass,
    doors: mapped.doors,
    cylinders: mapped.cylinders,
    fuelType: mapped.fuelType
  });

  return mapped;
}

export default { decodeVIN, validateVIN, validateVINChecksum, mapVINToFormData };
