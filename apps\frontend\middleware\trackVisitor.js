import { connectToDatabase } from '../lib/dbConnect';
import mongoose from 'mongoose';

/**
 * Visitor Tracking Middleware
 * 
 * This middleware tracks unique visitors to the FazeNAuto platform with:
 * - IP-based visitor identification
 * - Geolocation data from ipapi.co
 * - Visit counting and timestamp tracking
 * - Automatic cleanup after 90 days
 * - Performance optimized with proper indexing
 */

// Visitor Schema with TTL index for auto-cleanup
const visitorSchema = new mongoose.Schema({
  ip: { 
    type: String, 
    required: true, 
    unique: true,
    index: true // Index for fast IP lookups
  },
  userAgent: { 
    type: String, 
    required: true 
  },
  referer: { 
    type: String, 
    default: '' 
  },
  firstVisit: { 
    type: Date, 
    required: true 
  },
  lastVisit: { 
    type: Date, 
    required: true,
    index: true // Index for sorting by recent visits
  },
  visitCount: { 
    type: Number, 
    default: 1 
  },
  city: { 
    type: String, 
    default: 'Unknown' 
  },
  region: { 
    type: String, 
    default: 'Unknown' 
  },
  country: { 
    type: String, 
    default: 'Unknown' 
  },
  countryCode: { 
    type: String, 
    default: 'Unknown' 
  },
  timezone: { 
    type: String, 
    default: 'Unknown' 
  },
  isp: { 
    type: String, 
    default: 'Unknown' 
  },
  createdAt: { 
    type: Date, 
    default: Date.now,
    index: true, // Index for TTL and date-based queries
    expires: 7776000 // TTL: 90 days in seconds (90 * 24 * 60 * 60)
  }
});

// Create compound indexes for better query performance
visitorSchema.index({ ip: 1, lastVisit: -1 });
visitorSchema.index({ country: 1, createdAt: -1 });
visitorSchema.index({ createdAt: -1 }); // For analytics queries

const Visitor = mongoose.models.Visitor || mongoose.model('Visitor', visitorSchema);

/**
 * Extract client IP address from various headers
 * Handles different hosting environments and proxy configurations
 */
const getClientIP = (request) => {
  // Check various headers in priority order
  const forwardedFor = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const cfConnectingIP = request.headers.get('cf-connecting-ip');
  const remoteAddr = request.headers.get('remote-addr');
  
  let ip = 'unknown';
  
  if (forwardedFor) {
    // x-forwarded-for can contain multiple IPs, take the first one
    ip = forwardedFor.split(',')[0].trim();
  } else if (realIP) {
    ip = realIP;
  } else if (cfConnectingIP) {
    ip = cfConnectingIP;
  } else if (remoteAddr) {
    ip = remoteAddr;
  }
  
  return ip;
};

/**
 * Check if IP should be ignored (localhost, internal IPs)
 * In production, we ignore development IPs
 */
const shouldIgnoreIP = (ip) => {
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  // Always ignore these IPs
  const ignoredIPs = ['unknown', '::1', '127.0.0.1'];
  
  // In production, also ignore private network ranges
  if (!isDevelopment) {
    const privateRanges = [
      /^192\.168\./,  // 192.168.x.x
      /^10\./,        // 10.x.x.x
      /^172\.(1[6-9]|2[0-9]|3[0-1])\./  // 172.16.x.x - 172.31.x.x
    ];
    
    if (privateRanges.some(range => range.test(ip))) {
      return true;
    }
  }
  
  return ignoredIPs.includes(ip);
};

/**
 * Get geolocation data from IP address using ipapi.co
 * Free tier: 1000 requests/month
 */
const getLocationFromIP = async (ip) => {
  try {
    console.log(`Fetching geolocation for IP: ${ip}`);
    
    const response = await fetch(`http://ipapi.co/${ip}/json/`, {
      headers: {
        'User-Agent': 'FazeNAuto/1.0'
      },
      signal: AbortSignal.timeout(5000) // 5 second timeout
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    
    // Check if we got an error response
    if (data.error) {
      console.warn('IP geolocation error:', data.reason);
      return getDefaultLocation();
    }
    
    return {
      city: data.city || 'Unknown',
      region: data.region || 'Unknown',
      country: data.country_name || 'Unknown',
      countryCode: data.country_code || 'Unknown',
      timezone: data.timezone || 'Unknown',
      isp: data.org || 'Unknown'
    };
    
  } catch (error) {
    console.error('Error fetching geolocation for IP', ip, ':', error.message);
    return getDefaultLocation();
  }
};

/**
 * Fallback location data for when geolocation fails
 */
const getDefaultLocation = () => {
  return {
    city: 'Unknown',
    region: 'Unknown',
    country: 'Unknown',
    countryCode: 'Unknown',
    timezone: 'Unknown',
    isp: 'Unknown'
  };
};

/**
 * Main visitor tracking function
 * This is the core middleware that can be used across the application
 */
export const trackVisitor = async (request) => {
  try {
    // Connect to database
    await connectToDatabase();
    
    // Extract visitor information
    const ip = getClientIP(request);
    const userAgent = request.headers.get('user-agent') || 'Unknown';
    const referer = request.headers.get('referer') || '';
    
    // Skip tracking for ignored IPs
    if (shouldIgnoreIP(ip)) {
      console.log(`Skipping tracking for ignored IP: ${ip}`);
      return { success: true, message: 'IP ignored', ip };
    }
    
    const now = new Date();
    
    // Check if visitor already exists
    const existingVisitor = await Visitor.findOne({ ip });
    
    if (existingVisitor) {
      // Update existing visitor
      const updateResult = await Visitor.updateOne(
        { ip },
        {
          $set: {
            lastVisit: now,
            userAgent,
            referer
          },
          $inc: {
            visitCount: 1
          }
        }
      );
      
      console.log(`Updated existing visitor ${ip}, visit count: ${existingVisitor.visitCount + 1}`);
      
      return {
        success: true,
        message: 'Existing visitor updated',
        ip,
        visitCount: existingVisitor.visitCount + 1,
        isNewVisitor: false
      };
      
    } else {
      // New visitor - get location data
      const location = await getLocationFromIP(ip);
      
      // Create new visitor record
      const newVisitor = new Visitor({
        ip,
        userAgent,
        referer,
        firstVisit: now,
        lastVisit: now,
        visitCount: 1,
        city: location.city,
        region: location.region,
        country: location.country,
        countryCode: location.countryCode,
        timezone: location.timezone,
        isp: location.isp,
        createdAt: now
      });
      
      await newVisitor.save();
      
      console.log(`New visitor tracked: ${ip} from ${location.city}, ${location.country}`);
      
      return {
        success: true,
        message: 'New visitor tracked',
        ip,
        location: `${location.city}, ${location.region}, ${location.country}`,
        visitCount: 1,
        isNewVisitor: true
      };
    }
    
  } catch (error) {
    console.error('Error in visitor tracking:', error);
    return {
      success: false,
      error: error.message,
      ip: getClientIP(request)
    };
  }
};

/**
 * Express/Next.js middleware wrapper
 * Can be used as middleware in API routes or pages
 */
export const visitorTrackingMiddleware = async (request, response, next) => {
  try {
    const result = await trackVisitor(request);
    
    // Add tracking result to request object for use in handlers
    request.visitorTracking = result;
    
    // Continue to next middleware/handler
    if (next) {
      next();
    }
    
    return result;
    
  } catch (error) {
    console.error('Visitor tracking middleware error:', error);
    
    // Don't fail the request if tracking fails
    request.visitorTracking = {
      success: false,
      error: error.message
    };
    
    if (next) {
      next();
    }
    
    return { success: false, error: error.message };
  }
};

/**
 * Get visitor statistics for analytics
 */
export const getVisitorStats = async (timeRange = '7d') => {
  try {
    await connectToDatabase();
    
    // Calculate date range
    const now = new Date();
    let startDate;
    
    switch (timeRange) {
      case '1d':
        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    }
    
    // Aggregate visitor statistics
    const stats = await Visitor.aggregate([
      {
        $facet: {
          totalVisitors: [
            { $count: "count" }
          ],
          recentVisitors: [
            { $match: { lastVisit: { $gte: startDate } } },
            { $count: "count" }
          ],
          newVisitors: [
            { $match: { firstVisit: { $gte: startDate } } },
            { $count: "count" }
          ],
          topCountries: [
            { $match: { lastVisit: { $gte: startDate } } },
            { $group: { _id: "$country", count: { $sum: 1 } } },
            { $sort: { count: -1 } },
            { $limit: 10 }
          ],
          totalVisits: [
            { $match: { lastVisit: { $gte: startDate } } },
            { $group: { _id: null, total: { $sum: "$visitCount" } } }
          ]
        }
      }
    ]);
    
    const result = stats[0];
    
    return {
      totalVisitors: result.totalVisitors[0]?.count || 0,
      recentVisitors: result.recentVisitors[0]?.count || 0,
      newVisitors: result.newVisitors[0]?.count || 0,
      totalVisits: result.totalVisits[0]?.total || 0,
      topCountries: result.topCountries || [],
      timeRange,
      generatedAt: new Date()
    };
    
  } catch (error) {
    console.error('Error getting visitor stats:', error);
    throw error;
  }
};

export default { trackVisitor, visitorTrackingMiddleware, getVisitorStats };
