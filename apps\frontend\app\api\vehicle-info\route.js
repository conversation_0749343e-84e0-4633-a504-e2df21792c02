import { NextResponse } from 'next/server';

/**
 * Vehicle Information API Route
 * Handles VIN lookup with NHTSA data, recalls, and MarketCheck pricing (dealer-only)
 */
export async function POST(request) {
  try {
    const { vin } = await request.json();

    if (!vin || vin.length !== 17) {
      return NextResponse.json({
        success: false,
        error: 'Valid 17-character VIN required'
      }, { status: 400 });
    }

    // Validate VIN format
    const vinPattern = /^[A-HJ-NPR-Z0-9]{17}$/;
    if (!vinPattern.test(vin.toUpperCase())) {
      return NextResponse.json({
        success: false,
        error: 'Invalid VIN format'
      }, { status: 400 });
    }

    console.log('🔍 Processing vehicle info request for VIN:', vin);

    // Fetch NHTSA data with retry logic
    let nhtsaData;
    let nhtsaError = null;
    try {
      nhtsaData = await fetchNHTSADataWithRetry(vin);
    } catch (error) {
      console.error('❌ NHTSA API error after retries:', error);
      nhtsaError = error;

      // Check if this is a service unavailable error (503)
      if (error.message.includes('503')) {
        console.log('🚨 NHTSA service is temporarily unavailable - attempting fallback options');

        // Try alternative approach: basic VIN parsing
        const basicVinInfo = parseVINBasic(vin);
        if (basicVinInfo) {
          console.log('✅ Using basic VIN parsing as fallback');
          nhtsaData = {
            Results: Object.entries(basicVinInfo).map(([key, value]) => ({
              Variable: key,
              Value: value
            }))
          };
          // Keep error reference to show fallback was used, but allow processing to continue
        }
      }

      // If we still don't have data, return appropriate error
      if (!nhtsaData) {
        let errorMessage = 'NHTSA vehicle database is temporarily unavailable';
        let statusCode = 503;

        if (error.name === 'AbortError' || error.message.includes('timeout')) {
          errorMessage = 'NHTSA API timeout - the service is currently slow. Please try again in a few minutes.';
        } else if (error.message.includes('fetch failed') || error.message.includes('network')) {
          errorMessage = 'Network connection issue - NHTSA API unavailable. Please check your internet connection.';
        } else if (error.message.includes('503')) {
          errorMessage = 'NHTSA vehicle database is temporarily down for maintenance. Please try again later.';
        } else if (error.message.includes('500')) {
          errorMessage = 'NHTSA server error - their service is experiencing issues. Please try again later.';
        }

        return NextResponse.json({
          success: false,
          error: errorMessage,
          details: error.message,
          serviceStatus: 'unavailable',
          retryAfter: 300 // Suggest retry after 5 minutes
        }, { status: statusCode });
      }
    }

    // Parse NHTSA response
    const results = nhtsaData.Results || [];
    const vehicleInfo = {};
    
    results.forEach(item => {
      if (item.Value && item.Value !== 'Not Applicable' && item.Value !== '' && item.Value !== 'N/A') {
        switch (item.Variable) {
          // Basic vehicle information
          case 'Make':
            vehicleInfo.make = item.Value;
            break;
          case 'Model':
            vehicleInfo.model = item.Value;
            break;
          case 'Model Year':
            vehicleInfo.year = parseInt(item.Value);
            break;
          case 'Vehicle Type':
            vehicleInfo.vehicleType = item.Value;
            break;
          case 'Body Class':
            vehicleInfo.bodyClass = item.Value;
            break;
          case 'Manufacturer Name':
            vehicleInfo.manufacturer = item.Value;
            break;
          case 'Plant City':
            vehicleInfo.plantCity = item.Value;
            break;
          case 'Plant Country':
            vehicleInfo.plantCountry = item.Value;
            break;
          case 'Trim':
            vehicleInfo.trim = item.Value;
            break;
          case 'Series':
            vehicleInfo.series = item.Value;
            break;

          // Engine information
          case 'Engine Number of Cylinders':
            vehicleInfo.cylinders = item.Value;
            break;
          case 'Engine Configuration':
            vehicleInfo.engineConfiguration = item.Value;
            break;
          case 'Engine Model':
            vehicleInfo.engineModel = item.Value;
            break;
          case 'Engine Manufacturer':
            vehicleInfo.engineManufacturer = item.Value;
            break;
          case 'Engine Brake (hp) From':
            vehicleInfo.engineBrakeHpFrom = item.Value;
            break;
          case 'Engine Brake (hp) To':
            vehicleInfo.engineBrakeHpTo = item.Value;
            break;
          case 'Engine Power (kW)':
            vehicleInfo.enginePowerKW = item.Value;
            break;
          case 'Engine Stroke Cycles':
            vehicleInfo.engineStrokeCycles = item.Value;
            break;

          // Displacement - try multiple field names
          case 'Displacement (CC)':
            vehicleInfo.engineDisplacementCC = item.Value;
            break;
          case 'Engine Displacement (CC)':
            vehicleInfo.engineDisplacementCC = item.Value;
            break;
          case 'Displacement (CI)':
            vehicleInfo.engineDisplacementCI = item.Value;
            break;
          case 'Engine Displacement (CI)':
            vehicleInfo.engineDisplacementCI = item.Value;
            break;
          case 'Displacement (L)':
            vehicleInfo.engineDisplacementL = item.Value;
            break;
          case 'Engine Displacement (L)':
            vehicleInfo.engineDisplacementL = item.Value;
            break;

          // Fuel information
          case 'Fuel Type - Primary':
            vehicleInfo.fuelType = item.Value;
            break;
          case 'Fuel Type - Secondary':
            vehicleInfo.fuelTypeSecondary = item.Value;
            break;

          // Transmission and drivetrain
          case 'Transmission Style':
            vehicleInfo.transmission = item.Value;
            break;
          case 'Transmission Speeds':
            vehicleInfo.transmissionSpeeds = item.Value;
            break;
          case 'Drive Type':
            vehicleInfo.driveType = item.Value;
            break;

          // Physical characteristics
          case 'Number of Doors':
          case 'Doors':
            vehicleInfo.doors = item.Value;
            break;
          case 'Number of Seats':
            vehicleInfo.seats = item.Value;
            break;
          case 'Number of Seat Rows':
            vehicleInfo.seatRows = item.Value;
            break;
          case 'Curb Weight (pounds)':
            vehicleInfo.curbWeight = item.Value;
            break;
          case 'Gross Vehicle Weight Rating From':
            vehicleInfo.gvwrFrom = item.Value;
            break;
          case 'Gross Vehicle Weight Rating To':
            vehicleInfo.gvwrTo = item.Value;
            break;
        }
      }
    });

    // Fetch recalls data with retry logic
    let recallsData = null;
    try {
      if (vehicleInfo.make && vehicleInfo.model && vehicleInfo.year) {
        recallsData = await fetchRecallsDataWithRetry(vehicleInfo.make, vehicleInfo.model, vehicleInfo.year);
      }
    } catch (error) {
      console.warn('Failed to fetch recalls data after retries:', error);
    }

    // Fetch MarketCheck data (dealer-only) - server-side only
    let marketData = null;
    try {
      const marketCheckResult = await getCompleteMarketDataServerSide(vin, vehicleInfo);
      if (marketCheckResult.success) {
        marketData = marketCheckResult.data;
      } else {
        console.log('MarketCheck data fetch failed:', marketCheckResult.error);
      }
    } catch (error) {
      console.error('MarketCheck error:', error);
    }

    // Map API response to match frontend expectations
    const mappedVehicleInfo = {
      // Basic fields
      make: vehicleInfo.make,
      manufacturer: vehicleInfo.manufacturer,
      model: vehicleInfo.model,
      year: vehicleInfo.year,
      plantCity: vehicleInfo.plantCity,
      vehicleType: vehicleInfo.vehicleType,
      plantCountry: vehicleInfo.plantCountry,
      bodyClass: vehicleInfo.bodyClass,
      doors: vehicleInfo.doors,
      seats: vehicleInfo.seats,
      trim: vehicleInfo.trim,
      series: vehicleInfo.series,

      // Engine fields - comprehensive mapping
      engine: vehicleInfo.engineModel ||
              (vehicleInfo.cylinders && vehicleInfo.engineConfiguration ?
                `${vehicleInfo.cylinders} Cylinder ${vehicleInfo.engineConfiguration}` :
                vehicleInfo.cylinders ? `${vehicleInfo.cylinders} Cylinder` :
                vehicleInfo.engineConfiguration || null),
      cylinders: vehicleInfo.cylinders,
      engineConfiguration: vehicleInfo.engineConfiguration,
      engineModel: vehicleInfo.engineModel,
      engineManufacturer: vehicleInfo.engineManufacturer,

      // Displacement fields - try multiple sources
      displacementCC: vehicleInfo.engineDisplacementCC,
      displacementCI: vehicleInfo.engineDisplacementCI,
      displacementL: vehicleInfo.engineDisplacementL,

      // Power fields
      horsepowerFrom: vehicleInfo.engineBrakeHpFrom,
      horsepowerTo: vehicleInfo.engineBrakeHpTo,
      enginePowerKW: vehicleInfo.enginePowerKW,

      // Fuel information
      fuelType: vehicleInfo.fuelType,
      fuelTypeSecondary: vehicleInfo.fuelTypeSecondary,

      // Transmission and drivetrain
      transmission: vehicleInfo.transmission,
      transmissionSpeeds: vehicleInfo.transmissionSpeeds,
      driveType: vehicleInfo.driveType,
      drivetrain: vehicleInfo.driveType, // Frontend expects both

      // Weight and capacity
      curbWeight: vehicleInfo.curbWeight,
      gvwrFrom: vehicleInfo.gvwrFrom,
      gvwrTo: vehicleInfo.gvwrTo,
      seatRows: vehicleInfo.seatRows
    };

    const response = {
      success: true,
      vin: vin,
      vehicleInfo: mappedVehicleInfo,
      recalls: recallsData,
      marketData: marketData,
      source: nhtsaError ? 'Basic VIN Parsing (NHTSA Unavailable)' : 'NHTSA',
      fetchedAt: new Date().toISOString(),
      // Include warning if using fallback data
      ...(nhtsaError && {
        warning: 'Limited vehicle information available - NHTSA database temporarily unavailable',
        serviceStatus: 'degraded'
      })
    };

    console.log('✅ Vehicle info processed successfully');
    return NextResponse.json(response);

  } catch (error) {
    console.error('❌ Vehicle info processing failed:', error);
    return NextResponse.json({ 
      success: false, 
      error: error.message || 'Failed to process vehicle information' 
    }, { status: 500 });
  }
}

/**
 * Basic VIN parsing fallback when NHTSA API is unavailable
 * This provides minimal vehicle information from VIN structure
 */
function parseVINBasic(vin) {
  if (!vin || vin.length !== 17) return null;

  try {
    const vinUpper = vin.toUpperCase();

    // VIN position meanings (basic decoding)
    const worldManufacturerIdentifier = vinUpper.substring(0, 3);
    const vehicleDescriptorSection = vinUpper.substring(3, 9);
    const vehicleIdentifierSection = vinUpper.substring(9, 17);
    const modelYear = vinUpper.charAt(9);
    const plantCode = vinUpper.charAt(10);

    // Basic manufacturer mapping (limited set)
    const manufacturerMap = {
      '1HG': 'Honda',
      '1HT': 'Honda',
      '2HG': 'Honda',
      '2T1': 'Toyota',
      '2T2': 'Toyota',
      '4T1': 'Toyota',
      '5YJ': 'Tesla',
      'WBA': 'BMW',
      'WBS': 'BMW',
      'WAU': 'Audi',
      'WDD': 'Mercedes-Benz',
      '1FA': 'Ford',
      '1FB': 'Ford',
      '1FC': 'Ford',
      '1FT': 'Ford',
      '1G1': 'Chevrolet',
      '1G6': 'Cadillac',
      '1GC': 'Chevrolet',
      '1GM': 'Chevrolet',
      '2G1': 'Chevrolet',
      '3G1': 'Chevrolet',
      'JHM': 'Honda',
      'JTD': 'Toyota',
      'KMH': 'Hyundai',
      'KNA': 'Kia',
      'VF1': 'Renault',
      'VF3': 'Peugeot',
      'VF7': 'Citroën',
      'WVW': 'Volkswagen',
      'WP0': 'Porsche'
    };

    // Model year mapping (10th position)
    const yearMap = {
      'A': 2010, 'B': 2011, 'C': 2012, 'D': 2013, 'E': 2014,
      'F': 2015, 'G': 2016, 'H': 2017, 'J': 2018, 'K': 2019,
      'L': 2020, 'M': 2021, 'N': 2022, 'P': 2023, 'R': 2024,
      'S': 2025, 'T': 2026, 'V': 2027, 'W': 2028, 'X': 2029,
      'Y': 2030, '1': 2031, '2': 2032, '3': 2033, '4': 2034,
      '5': 2035, '6': 2036, '7': 2037, '8': 2038, '9': 2039
    };

    const manufacturer = manufacturerMap[worldManufacturerIdentifier] || 'Unknown';
    const year = yearMap[modelYear] || null;

    // Return basic information
    const basicInfo = {
      'Make': manufacturer !== 'Unknown' ? manufacturer : null,
      'Model Year': year,
      'Manufacturer Name': manufacturer !== 'Unknown' ? manufacturer : null
    };

    // Filter out null values
    return Object.fromEntries(
      Object.entries(basicInfo).filter(([key, value]) => value !== null)
    );

  } catch (error) {
    console.error('Basic VIN parsing failed:', error);
    return null;
  }
}

/**
 * Fetch NHTSA data with retry logic and exponential backoff
 */
async function fetchNHTSADataWithRetry(vin, maxRetries = 3) {
  let lastError;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`🔍 NHTSA API attempt ${attempt}/${maxRetries} for VIN: ${vin}`);

      const controller = new AbortController();
      // Increase timeout for each retry: 10s, 20s, 30s
      const timeout = 10000 * attempt;
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      const response = await fetch(
        `https://vpic.nhtsa.dot.gov/api/vehicles/DecodeVin/${vin}?format=json`,
        {
          signal: controller.signal,
          headers: {
            'User-Agent': 'FazeNAuto-VehicleLookup/1.0',
            'Accept': 'application/json',
            'Cache-Control': 'no-cache'
          }
        }
      );

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`NHTSA API HTTP error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log(`✅ NHTSA API success on attempt ${attempt}`);

      // Debug: Log available fields for troubleshooting
      if (data.Results) {
        console.log('🔍 Available NHTSA fields:', data.Results.map(r => r.Variable).filter(v => v).sort());
      }

      return data;

    } catch (error) {
      lastError = error;
      console.warn(`⚠️ NHTSA API attempt ${attempt} failed:`, error.message);

      // Don't retry on certain errors
      if (error.message.includes('400') || error.message.includes('401') || error.message.includes('403')) {
        throw error;
      }

      // For 503 errors, use longer delays since service is unavailable
      if (attempt < maxRetries) {
        let delay;
        if (error.message.includes('503')) {
          // Longer delays for service unavailable: 5s, 15s, 30s
          delay = [5000, 15000, 30000][attempt - 1] || 30000;
          console.log(`⏳ NHTSA service unavailable - waiting ${delay}ms before retry...`);
        } else {
          // Standard exponential backoff: 1s, 2s, 4s
          delay = Math.pow(2, attempt - 1) * 1000;
          console.log(`⏳ Waiting ${delay}ms before retry...`);
        }
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  throw lastError;
}

/**
 * Fetch recalls data with retry logic
 */
async function fetchRecallsDataWithRetry(make, model, year, maxRetries = 2) {
  let lastError;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`🔍 Recalls API attempt ${attempt}/${maxRetries} for ${year} ${make} ${model}`);

      const controller = new AbortController();
      const timeout = 10000 * attempt; // 10s, 20s
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      const response = await fetch(
        `https://api.nhtsa.gov/recalls/recallsByVehicle?make=${encodeURIComponent(make)}&model=${encodeURIComponent(model)}&modelYear=${year}&format=json`,
        {
          signal: controller.signal,
          headers: {
            'User-Agent': 'FazeNAuto-VehicleLookup/1.0',
            'Accept': 'application/json'
          }
        }
      );

      clearTimeout(timeoutId);

      if (response.ok) {
        const recalls = await response.json();
        console.log(`✅ Recalls API success on attempt ${attempt}`);
        return recalls.results || [];
      } else {
        throw new Error(`Recalls API HTTP error: ${response.status} ${response.statusText}`);
      }

    } catch (error) {
      lastError = error;
      console.warn(`⚠️ Recalls API attempt ${attempt} failed:`, error.message);

      // Wait before retry
      if (attempt < maxRetries) {
        const delay = 1000 * attempt; // 1s, 2s
        console.log(`⏳ Waiting ${delay}ms before recalls retry...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  console.warn('❌ All recalls API attempts failed, returning empty array');
  return [];
}

/**
 * Server-side MarketCheck API integration
 * This runs on the server where environment variables are available
 */
async function getCompleteMarketDataServerSide(vin, vehicleData = {}) {
  const apiKey = process.env.MARKETCHECK_API_KEY;
  const apiSecret = process.env.MARKETCHECK_API_SECRET;

  if (!apiKey || !apiSecret) {
    return {
      success: false,
      error: 'MarketCheck API not configured',
      data: null
    };
  }

  try {
    console.log('🏪 Fetching complete MarketCheck data for VIN:', vin);

    // Fetch market value data
    const marketValueResult = await getMarketValueServerSide(vin, vehicleData, apiKey, apiSecret);

    // If no market value available (404), return success but with null data
    if (!marketValueResult) {
      return {
        success: true,
        data: null,
        source: 'MarketCheck',
        fetchedAt: new Date().toISOString(),
        message: 'No market data available for this VIN'
      };
    }

    return {
      success: true,
      data: {
        marketValue: marketValueResult,
        comparableListings: { success: false, error: 'Not implemented yet' },
        priceHistory: { success: false, error: 'Not implemented yet' }
      },
      source: 'MarketCheck',
      fetchedAt: new Date().toISOString()
    };

  } catch (error) {
    console.error('❌ Complete MarketCheck data fetch failed:', error);
    return {
      success: false,
      error: error.message || 'Failed to fetch complete market data',
      data: null
    };
  }
}

/**
 * Server-side market value fetch with retry logic
 */
async function getMarketValueServerSide(vin, vehicleData, apiKey, apiSecret) {
  try {
    console.log('🏷️ Fetching MarketCheck market value for VIN:', vin);

    // Try multiple MarketCheck endpoints with retry logic
    return await fetchMarketCheckDataWithRetry(vin, vehicleData, apiKey, apiSecret);

  } catch (error) {
    console.error('❌ MarketCheck market value fetch failed:', error);

    // Handle 404 specifically - VIN not found in MarketCheck database
    if (error.message.includes('404')) {
      console.log(`⚠️ MarketCheck: No market data available for VIN ${vin}`);
      return null; // Return null instead of throwing error
    }

    throw error;
  }
}

/**
 * Fetch MarketCheck data with multiple endpoints and retry logic
 */
async function fetchMarketCheckDataWithRetry(vin, vehicleData, apiKey, apiSecret, maxRetries = 2) {
  const endpoints = [
    // Primary endpoint: VIN-specific search using correct MarketCheck endpoint
    {
      url: `https://mc-api.marketcheck.com/v2/search/car/active`,
      params: {
        api_key: apiKey,
        vin: vin,
        include_relevant_links: 'true'
      }
    },
    // Fallback endpoint: Search by make/model/year if VIN fails
    vehicleData.make && vehicleData.model && vehicleData.year ? {
      url: `https://mc-api.marketcheck.com/v2/search/car/active`,
      params: {
        api_key: apiKey,
        make: vehicleData.make,
        model: vehicleData.model,
        year: vehicleData.year,
        include_relevant_links: 'true'
      }
    } : null
  ].filter(Boolean);

  let lastError;

  for (const endpoint of endpoints) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`🔍 MarketCheck attempt ${attempt}/${maxRetries} with endpoint:`, endpoint.url);

        const params = new URLSearchParams(endpoint.params);

        const controller = new AbortController();
        const timeout = 15000 * attempt; // 15s, 30s
        const timeoutId = setTimeout(() => controller.abort(), timeout);

        const response = await fetch(
          `${endpoint.url}?${params.toString()}`,
          {
            method: 'GET',
            signal: controller.signal,
            headers: {
              'Accept': 'application/json',
              'User-Agent': 'FazeNAuto-MarketCheck/1.0'
            }
          }
        );

        clearTimeout(timeoutId);

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(`MarketCheck API error: ${response.status} - ${errorData.message || response.statusText}`);
        }

        const data = await response.json();

        console.log('✅ MarketCheck market value fetched successfully');

        return {
          success: true,
          data: parseMarketValueResponse(data),
          source: 'MarketCheck',
          fetchedAt: new Date().toISOString()
        };

      } catch (error) {
        lastError = error;
        console.warn(`⚠️ MarketCheck attempt ${attempt} failed:`, error.message);

        // Don't retry on certain errors
        if (error.message.includes('401') || error.message.includes('403')) {
          throw error;
        }

        // Wait before retry
        if (attempt < maxRetries) {
          const delay = 2000 * attempt; // 2s, 4s
          console.log(`⏳ Waiting ${delay}ms before MarketCheck retry...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }
  }

  throw lastError;
}

/**
 * Parse market value response from MarketCheck API
 */
function parseMarketValueResponse(data) {
  if (!data || !data.listings || data.listings.length === 0) {
    return {
      marketValue: null,
      averagePrice: null,
      priceRange: null,
      listingCount: 0,
      message: 'No market data available'
    };
  }

  const listings = data.listings;
  const prices = listings.map(listing => listing.price).filter(price => price && price > 0);
  
  if (prices.length === 0) {
    return {
      marketValue: null,
      averagePrice: null,
      priceRange: null,
      listingCount: listings.length,
      message: 'No pricing data available'
    };
  }

  const averagePrice = Math.round(prices.reduce((sum, price) => sum + price, 0) / prices.length);
  const minPrice = Math.min(...prices);
  const maxPrice = Math.max(...prices);

  return {
    marketValue: averagePrice,
    averagePrice: averagePrice,
    priceRange: {
      min: minPrice,
      max: maxPrice
    },
    listingCount: listings.length,
    dataPoints: prices.length,
    message: `Based on ${prices.length} comparable listings`
  };
}
