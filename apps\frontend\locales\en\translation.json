{"nav.home": "Home", "nav.inventory": "Inventory", "nav.services": "Services", "nav.used_vehicles": "Used Vehicles", "nav.vehicle_info_lookup": "Vehicle Information Lookup", "nav.special_offer": "Special Offer", "nav.sell_or_trade": "Sell or Trade", "nav.dashboard": "Dashboard", "nav.vehicle_scan": "VIN Decode", "nav.upload_new": "Upload New", "nav.admin_listings": "Admin Listings", "nav.compliance_forms": "Compliance Forms", "nav.user_profile": "User Profile", "nav.logout": "Logout", "nav.financing": "Financing", "nav.finance_department": "Finance Department", "nav.financing_calculator": "Financing Calculator", "nav.financing_application": "Financing Application", "nav.pricing": "Pricing", "nav.features": "Features", "nav.about": "About", "nav.contact": "Contact", "nav.login": "Dealer <PERSON>", "nav.signup": "Get Started", "nav.jobs": "Jobs", "nav.quotes": "Quotes", "nav.invoices": "Invoices", "nav.customers": "Customers", "nav.vehicles": "Vehicle Management", "nav.admin": "Admin", "settings.title": "Settings", "settings.subtitle": "Manage your account preferences and settings", "settings.notifications": "Notifications", "settings.privacy": "Privacy", "settings.preferences": "Preferences", "settings.account": "Account", "settings.theme": "Theme", "settings.theme.description": "Choose your preferred theme", "settings.language": "Language", "settings.language.description": "Select your preferred language", "settings.currency": "<PERSON><PERSON><PERSON><PERSON>", "settings.currency.description": "Default currency for pricing", "trial.title": "Start Your Free Trial", "trial.subtitle": "Choose your plan and start your 14-day free trial. No credit card required.", "trial.continue": "Continue with", "trial.selected": "Selected Plan:", "trial.then": "14-day free trial, then", "profile.title": "Profile", "profile.subtitle": "Manage your account information", "profile.subscription": "Subscription", "profile.no_subscription": "No active subscription found.", "profile.start_trial": "Start Free Trial", "admin.title": "Admin Panel", "admin.subtitle": "Manage job types, pricing, and parts inventory", "admin.job_types": "Job Types", "admin.parts_inventory": "Parts & Inventory", "admin.settings": "Settings", "admin.service_types": "Service Types", "admin.base_price": "Base Price:", "admin.est_time": "Est. Time:", "admin.edit": "Edit", "admin.delete": "Delete", "admin.vehicle_inventory_management": "Inventory Management", "admin.show_syndication": "Show Syndication", "admin.hide_syndication": "Hide Syndication", "admin.visitors": "Visitors", "admin.show_image_processing": "Show Image Processing", "admin.hide_image_processing": "Hide Image Processing", "admin.show_ad_generator": "Show Ad Generator", "admin.hide_ad_generator": "Hide Ad Generator", "admin.vehicles_selected": "vehicle(s) selected", "admin.total_vehicles": "Total Vehicles", "admin.active_listings": "Active Listings", "admin.draft_listings": "Draft Listings", "admin.sold_vehicles": "Sold Vehicles", "admin.click_to_view_all": "Click to view all vehicles", "admin.image_processing_pipeline": "Image Processing Pipeline", "admin.batch_process_images": "Batch process vehicle images for multiple platforms", "admin.vin_to_ad_generator": "VIN-to-Ad Generator", "admin.generate_compelling_ads": "Generate compelling ads from VIN using AI", "dashboard.title": "Dashboard", "dashboard.subtitle": "Welcome back! Here's your business overview.", "dashboard.welcome_back_user": "Welcome back, {name}! Here's your business overview.", "dashboard.total_jobs": "Total Jobs", "dashboard.revenue": "Revenue", "dashboard.pending_jobs": "Pending Jobs", "dashboard.customers": "Customers", "dashboard.recent_jobs": "Recent Jobs", "dashboard.no_recent_jobs": "No recent jobs found.", "jobs.title": "Jobs", "jobs.subtitle": "Manage your scheduled and completed jobs", "jobs.new": "+ New Job", "jobs.all": "All Jobs", "jobs.no_jobs": "No jobs", "jobs.get_started": "Get started by creating a new job.", "jobs.status.completed": "Completed", "jobs.status.scheduled": "Scheduled", "jobs.status.in_progress": "In Progress", "customers.title": "Customers", "customers.subtitle": "Manage your customer database", "customers.new": "+ New Customer", "customers.search": "Search customers...", "customers.phone": "Phone", "customers.address": "Address", "customers.total_jobs": "Total Jobs", "customers.total_spent": "Total Spent", "customers.last_service": "Last Service", "customers.view_history": "View History", "customers.edit": "Edit", "customers.schedule_job": "Schedule Job", "customers.send_quote": "Send Quote", "vehicles.title": "Vehicles", "vehicles.subtitle": "Manage customer vehicles and service history", "vehicles.add": "+ Add Vehicle", "quotes.title": "Quotes", "quotes.subtitle": "Create and manage service quotes", "quotes.new": "+ New Quote", "quotes.search": "Search quotes...", "quotes.all_status": "All Status", "invoices.title": "Invoices", "invoices.subtitle": "Track payments and manage billing", "invoices.new": "+ New Invoice", "invoices.search": "Search invoices...", "invoices.all_status": "All Status", "common.loading": "Loading...", "common.loading_vehicle_details": "Loading vehicle details...", "common.loading_vehicles": "Loading our quality vehicles...", "common.decoding": "Decoding...", "inventory.used_vehicles_title": "Used Vehicles", "inventory.used_vehicles_description": "Discover our carefully selected collection of premium pre-owned vehicles. All vehicles come with competitive pricing.", "inventory.available_cars": "Available Cars", "inventory.fair_pricing": "Fair", "inventory.pricing": "Pricing", "inventory.no_vehicles": "No vehicles available at the moment", "inventory.check_back": "Check back soon for new arrivals!", "vehicle.back_to_inventory": "Back to Inventory", "vehicle.not_found": "Vehicle Not Found", "vehicle.not_found_message": "The vehicle you are looking for could not be found.", "vehicle.sold": "SOLD", "vehicle.plus_tax": "+ Tax", "vehicle.estimated_payment": "Est.", "vehicle.per_month": "/mo", "vehicle.details": "Vehicle Details", "vehicle.make": "Make:", "vehicle.model": "Model:", "vehicle.year": "Year:", "vehicle.odometer": "Odometer:", "vehicle.engine": "Engine:", "vehicle.transmission": "Transmission:", "vehicle.drivetrain": "Drivetrain:", "vehicle.fuel_type": "Fuel Type:", "vehicle.body_class": "Body Class:", "vehicle.doors": "Doors:", "vehicle.cylinders": "Cylinders:", "vehicle.plant_city": "Plant City:", "vehicle.plant_country": "Plant Country:", "vehicle.drive_type": "Drive Type:", "vehicle.displacement_cc": "Displacement (CC):", "vehicle.displacement_ci": "Displacement (CI):", "vehicle.horsepower_from": "Engine Brake (hp) From:", "vehicle.exterior_color": "Exterior Color:", "vehicle.interior_color": "Interior Color:", "vehicle.vin": "VIN:", "vehicle.description": "Description", "vehicle.features_options": "Features & Options", "vehicle.view_all_options": "View All Options", "vehicle.contact_about": "Contact Us About This Vehicle", "vehicle.get_financing": "Get Financing Info", "vehicle.disclaimer": "All vehicles are sold as-is. Prices do not include taxes, licensing, and other fees.", "vehicle.note": "Note:", "vehicle.km": "km", "vehicle.view_details": "View Details", "vehicle.contact_us": "Contact Us", "vehicle.driveline": "Driveline:", "vehicle.exterior_features": "Exterior Features", "vehicle.interior_features": "Interior Features", "vehicle.mechanical_features": "Mechanical Features", "vehicle.safety_features": "Safety Features", "vehicle.entertainment_features": "Entertainment Features", "vehicle.comfort_features": "Comfort & Convenience", "vehicle.technology_features": "Technology Features", "vehicle.performance_features": "Performance Features", "vehicle.included": "INCLUDED", "vehicle.not_available": "Not Available", "vehicle_lookup.title": "Vehicle Information Lookup", "vehicle_lookup.subtitle": "Scan or enter a VIN to get detailed vehicle information", "vehicle_lookup.placeholder": "Enter the 17-character VIN to automatically decode vehicle information", "vehicle_lookup.scan_vin": "Scan VIN", "vehicle_lookup.decode": "Decode", "vehicle_lookup.characters_remaining": "Characters remaining:", "vehicle_lookup.vin_decoder_error": "VIN Decoder Error:", "vehicle_lookup.lookup_failed": "Failed to lookup vehicle information. Please try again.", "vehicle_lookup.vehicle_info": "Vehicle Information", "vehicle_lookup.manufacturer": "Manufacturer:", "vehicle_lookup.vehicle_history": "Vehicle History & Recalls", "vehicle_lookup.recalls_found": "recalls found for this vehicle", "vehicle_lookup.no_recalls": "No recalls found for this vehicle", "vehicle_lookup.additional_history": "Additional vehicle history information may be available through third-party services.", "vehicle_lookup.decoded_vehicle_info": "Decoded Vehicle Information:", "vehicle_lookup.basic_info": "Basic Information", "vehicle_lookup.engine_performance": "Engine & Performance", "vehicle_lookup.vehicle_details": "Vehicle Details", "vehicle_lookup.trim": "Trim:", "vehicle_lookup.safety_recalls": "Safety Recalls", "vehicle_lookup.recall_count": "recall(s) found", "vehicle_lookup.more_recalls": "more recall(s)", "vehicle_lookup.recall_date": "Date:", "vehicle_lookup.recall_summary": "Summary:", "vehicle_lookup.clear_search": "Clear Search", "common.save": "Save", "common.cancel": "Cancel", "common.edit": "Edit", "common.delete": "Delete", "common.close": "Close", "common.search": "Search", "footer.language": "Language", "footer.darkMode": "Dark Mode", "sidebar.darkMode": "Dark Mode", "sidebar.language": "Language", "home.hero.title": "Find Your Perfect Vehicle at", "home.hero.subtitle": "Quality pre-owned vehicles with transparent pricing, no hidden fees. Your trusted automotive partner in finding the right car for your needs.", "home.hero.browse_inventory": "Browse Inventory", "home.hero.learn_more": "Learn More", "home.browse_by_make.title": "Browse by", "home.browse_by_make.make": "Make", "home.browse_by_make.view_all": "View All {make} Vehicles", "home.featured.title": "Featured Vehicles", "home.featured.subtitle": "Check out our latest quality pre-owned vehicles", "home.featured.loading": "Loading featured vehicles...", "home.featured.view_details": "View Details", "home.featured.contact_us": "Contact Us", "home.featured.view_all": "View All Vehicles", "home.featured.est_payment": "Est. $167/mo", "home.featured.plus_tax": "+ Tax", "home.featured.km": "km", "home.featured.engine": "Engine:", "home.featured.transmission": "Transmission:", "home.featured.fuel_type": "Fuel Type:", "home.featured.doors": "Doors:", "home.featured.driveline": "Driveline:", "home.featured.na": "N/A", "home.services.title": "Our Services", "home.services.subtitle": "Everything you need for your automotive journey", "home.services.quality.title": "Quality Vehicles", "home.services.quality.description": "Carefully selected pre-owned vehicles with transparent pricing. No hidden fees, just honest deals.", "home.services.pricing.title": "Competitive Pricing", "home.services.pricing.description": "Fair market pricing on all our vehicles. We believe in transparency and providing value to our customers.", "home.services.customer.title": "Customer Service", "home.services.customer.description": "Dedicated support throughout your buying journey. We're here to help you find the perfect vehicle for your needs.", "home.about.title": "About", "home.about.description1": "At FazeNAuto, we're committed to providing quality pre-owned vehicles with complete transparency. Our pricing model means no surprises, no hidden fees, and honest deals for every customer.", "home.about.description2": "We understand that buying a vehicle is a significant decision, and we're here to make that process as smooth and transparent as possible. Every vehicle in our inventory is carefully selected and priced fairly.", "home.about.feature.fair_pricing": "Fair Market Pricing", "home.about.feature.quality_vehicles": "Quality Pre-Owned Vehicles", "home.about.feature.no_fees": "No Hidden Fees", "home.about.feature.customer_service": "Customer-Focused Service", "home.cta.title": "Ready to Find Your Next Vehicle?", "home.cta.subtitle": "Browse our inventory of quality pre-owned vehicles or get in touch with our team", "footer.brand_description": "Quality pre-owned vehicles with transparent pricing. No hidden fees, just honest deals for every customer.", "footer.services.title": "Our Services", "footer.services.quality_vehicles": "Quality Pre-Owned Vehicles", "footer.services.transparent_pricing": "Transparent Pricing", "footer.services.customer_support": "Customer Support", "footer.services.vehicle_info": "Vehicle Information", "footer.contact.title": "Contact Us", "footer.contact.business_hours": "Business Hours", "footer.contact.hours_text": "Mon - Fri: 9:00 AM - 6:00 PM\nSat: 9:00 AM - 5:00 PM\nSun: Closed", "footer.copyright": "All rights reserved.", "footer.legal.privacy": "Privacy Policy", "footer.legal.terms": "Terms of Service", "footer.legal.contact": "Contact"}