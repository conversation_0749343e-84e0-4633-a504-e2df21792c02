/* Dashboard Container */
.dashboardContainer {
  padding: 0;
  background-color: #ffffff;
  min-height: 100vh;
  color: var(--text-primary, #333);
  transition: background-color 0.3s ease;
}

[data-theme="dark"] .dashboardContainer {
  background: var(--bg-primary);
}

/* Loading State */
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: var(--text-secondary);
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Desktop Content - Default Layout */
.desktopContent {
  display: block;
}

.header {
  margin-bottom: 2rem;
  padding: 2rem 1rem 0;
}

.header h1 {
  font-size: 2rem;
  margin: 0 0 0.5rem 0;
  color: var(--text-primary);
}

.header p {
  color: var(--text-secondary);
  margin: 0;
}

/* Stats Grid */
.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
  padding: 0 1rem;
}

.statCard {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.2s ease;
}

.statCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.statCard h3 {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0 0 0.5rem 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.statCard p {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

/* Desktop Chart Section */
.desktopChartSection {
  padding: 0 1rem;
  margin-bottom: 3rem;
}

.desktopChartSection h2 {
  font-size: 1.5rem;
  margin-bottom: 2rem;
  color: var(--text-primary);
  text-align: center;
}

.desktopChartContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 3rem;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 2rem;
}

.chartWrapper {
  flex-shrink: 0;
}

.chartWrapper .donutChart {
  width: 250px;
  height: 250px;
}

.chartWrapper .donutChart circle {
  transition: all 0.3s ease;
}

.desktopChartLegend {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  min-width: 250px;
}

.desktopChartLegend .legendItem {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem 1rem;
  background: var(--bg-primary);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  transition: all 0.2s ease;
}

.desktopChartLegend .legendItem:hover {
  background: var(--bg-tertiary);
  transform: translateY(-1px);
}

.desktopChartLegend .legendColor {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  flex-shrink: 0;
}

.desktopChartLegend .legendLabel {
  flex: 1;
  font-size: 0.875rem;
  color: var(--text-primary);
  text-align: left;
}

.desktopChartLegend .legendValue {
  font-weight: 700;
  color: var(--text-primary);
  background: var(--bg-tertiary);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.875rem;
  min-width: 40px;
  text-align: center;
}

/* Latest Section */
.latestSection {
  padding: 0 1rem 2rem;
}

.latestSection h2 {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
}

.vehicleGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
}

.vehicleCard {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
}

.vehicleCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.vehicleImage {
  height: 200px;
  overflow: hidden;
}

.vehicleImage img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.vehicleInfo {
  padding: 1rem;
}

.vehicleInfo h4 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: var(--text-primary);
}

.vehiclePrice {
  font-size: 1.125rem;
  font-weight: 700;
  color: #10b981;
  margin: 0;
}

/* Mobile Content - Hidden by default */
.mobileContent {
  display: none;
}

.mobileHeader {
  display: none;
}

/* MOBILE ONLY STYLES */
@media screen and (max-width: 768px) {
  .dashboardContainer {
    padding: 0.5rem;
  }

  /* Hide desktop content on mobile */
  .desktopContent {
    display: none;
  }

  /* Show mobile content */
  .mobileContent {
    display: block;
  }

  .mobileHeader {
    display: block;
    padding: 1rem 0.75rem;
    background: var(--bg-secondary);
    border-radius: 12px;
    margin-bottom: 1.5rem;
    border: 1px solid var(--border-color);
  }

  .mobileHeader h1 {
    font-size: clamp(1.5rem, 4vw, 2rem);
    margin: 0;
    color: var(--text-primary);
    text-align: center;
  }

  /* Chart Section */
  .chartSection {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .chartContainer {
    display: flex;
    justify-content: center;
    margin-bottom: 1.5rem;
  }

  .donutChart {
    width: 200px;
    height: 200px;
  }

  .donutChart circle {
    transition: all 0.3s ease;
  }

  .chartLegend {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .legendItem {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem;
    background: var(--bg-primary);
    border-radius: 8px;
    border: 1px solid var(--border-color);
  }

  .legendColor {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    flex-shrink: 0;
  }

  .legendItem span:nth-child(2) {
    flex: 1;
    font-size: 0.875rem;
    color: var(--text-primary);
  }

  .legendValue {
    font-weight: 700;
    color: var(--text-primary);
    background: var(--bg-tertiary);
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.875rem;
  }

  /* Latest Inventory Mobile */
  .latestInventoryMobile {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    padding: 1.5rem;
  }

  .latestInventoryMobile h2 {
    font-size: 1.25rem;
    margin: 0 0 1rem 0;
    color: var(--text-primary);
    text-align: center;
  }

  .mobileVehicleList {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .mobileVehicleCard {
    display: flex;
    gap: 1rem;
    padding: 1rem;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .mobileVehicleCard:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .mobileVehicleCard:active {
    transform: scale(0.98);
  }

  .mobileVehicleImage {
    width: 100px;
    height: 100px;
    border-radius: 12px;
    overflow: hidden;
    flex-shrink: 0;
    border: 1px solid var(--border-color);
  }

  .mobileVehicleImage img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
  }

  .mobileVehicleInfo {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .mobileVehicleTitle {
    font-size: 1rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
    line-height: 1.3;
  }

  .mobileVehicleDetails {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  .mobileInfoRow {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .mobileInfoLabel {
    font-size: 0.875rem;
    color: var(--text-secondary);
  }

  .mobileInfoValue {
    font-size: 0.875rem;
    color: var(--text-primary);
    font-weight: 500;
  }

  .mobileVehiclePrice {
    font-size: 1rem;
    font-weight: 700;
    color: #10b981;
    margin-top: auto;
  }
}

/* Responsive adjustments for larger screens */
@media (min-width: 769px) {
  .header {
    padding: 2rem 3rem 0;
  }

  .statsGrid {
    padding: 0 3rem;
  }

  .desktopChartSection {
    padding: 0 3rem;
  }

  .latestSection {
    padding: 0 3rem 2rem;
  }
}

@media (min-width: 1200px) {
  .header {
    padding: 2rem 4rem 0;
  }

  .statsGrid {
    padding: 0 4rem;
  }

  .desktopChartSection {
    padding: 0 4rem;
  }

  .latestSection {
    padding: 0 4rem 2rem;
  }
}
