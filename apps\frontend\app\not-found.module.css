.container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 2rem;
}

.content {
  text-align: center;
  max-width: 500px;
}

.title {
  font-size: 6rem;
  font-weight: bold;
  color: var(--primary-color);
  margin: 0;
  line-height: 1;
}

.subtitle {
  font-size: 2rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 1rem 0;
}

.description {
  font-size: 1.1rem;
  color: var(--text-secondary);
  margin: 1.5rem 0 2rem;
  line-height: 1.6;
}

.homeLink {
  display: inline-block;
  padding: 0.75rem 2rem;
  background-color: var(--primary-color);
  color: white;
  text-decoration: none;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.homeLink:hover {
  background-color: var(--primary-hover);
}
