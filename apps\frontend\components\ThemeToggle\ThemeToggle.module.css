.container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.toggle {
  position: relative;
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-primary);
  border-radius: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  padding: 0;
}

.toggle:hover {
  background-color: var(--bg-secondary);
}

.toggle:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--accent-primary);
}

.toggle.small {
  width: 2.5rem;
  height: 1.25rem;
}

.toggle.medium {
  width: 3rem;
  height: 1.5rem;
}

.toggle.large {
  width: 3.5rem;
  height: 1.75rem;
}

.toggle.active {
  background-color: var(--accent-primary);
}

.slider {
  position: absolute;
  left: 0.125rem;
  width: 1.25rem;
  height: 1.25rem;
  background-color: var(--bg-primary);
  border-radius: 50%;
  transition: transform 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.toggle.small .slider {
  width: 1rem;
  height: 1rem;
  font-size: 0.625rem;
}

.toggle.large .slider {
  width: 1.5rem;
  height: 1.5rem;
  font-size: 0.875rem;
}

.toggle.active .slider {
  transform: translateX(1.5rem);
}

.toggle.small.active .slider {
  transform: translateX(1.25rem);
}

.toggle.large.active .slider {
  transform: translateX(1.75rem);
}
