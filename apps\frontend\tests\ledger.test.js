/**
 * Test suite for the ledger system
 * Run with: npm test ledger.test.js
 */

import { validateTransactionData, validateFileUploads, formatErrorMessage } from '../lib/utils/errorHandler.js';

// Mock File constructor for testing
class MockFile {
  constructor(name, size, type) {
    this.name = name;
    this.size = size;
    this.type = type;
  }
}

describe('Ledger System Tests', () => {
  
  describe('Transaction Data Validation', () => {
    test('should validate valid transaction data', () => {
      const validData = {
        type: 'Vehicle Purchase',
        description: 'Bought a 2020 Honda Civic',
        amount: '25000.00',
        currency: 'CAD',
        addedBy: '<EMAIL>',
        date: '2025-01-15',
        notes: 'Excellent condition'
      };

      const result = validateTransactionData(validData);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    test('should reject missing required fields', () => {
      const invalidData = {
        type: '',
        description: '',
        amount: '',
        addedBy: ''
      };

      const result = validateTransactionData(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    test('should reject invalid amount', () => {
      const invalidData = {
        type: 'Sale',
        description: 'Test sale',
        amount: '-100',
        addedBy: '<EMAIL>'
      };

      const result = validateTransactionData(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors.some(e => e.field === 'amount')).toBe(true);
    });

    test('should reject description that is too long', () => {
      const invalidData = {
        type: 'Sale',
        description: 'A'.repeat(501), // Too long
        amount: '100',
        addedBy: '<EMAIL>'
      };

      const result = validateTransactionData(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors.some(e => e.field === 'description')).toBe(true);
    });

    test('should reject invalid currency', () => {
      const invalidData = {
        type: 'Sale',
        description: 'Test sale',
        amount: '100',
        currency: 'INVALID',
        addedBy: '<EMAIL>'
      };

      const result = validateTransactionData(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors.some(e => e.field === 'currency')).toBe(true);
    });
  });

  describe('File Upload Validation', () => {
    test('should validate valid files', () => {
      const validFiles = [
        new MockFile('receipt1.pdf', 1024 * 1024, 'application/pdf'),
        new MockFile('receipt2.jpg', 2 * 1024 * 1024, 'image/jpeg'),
        new MockFile('receipt3.png', 3 * 1024 * 1024, 'image/png')
      ];

      const result = validateFileUploads(validFiles);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    test('should reject files that are too large', () => {
      const invalidFiles = [
        new MockFile('large.pdf', 15 * 1024 * 1024, 'application/pdf') // 15MB
      ];

      const result = validateFileUploads(invalidFiles);
      expect(result.isValid).toBe(false);
      expect(result.errors.some(e => e.fileName === 'large.pdf')).toBe(true);
    });

    test('should reject unsupported file types', () => {
      const invalidFiles = [
        new MockFile('document.docx', 1024 * 1024, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')
      ];

      const result = validateFileUploads(invalidFiles);
      expect(result.isValid).toBe(false);
      expect(result.errors.some(e => e.fileName === 'document.docx')).toBe(true);
    });

    test('should reject empty files', () => {
      const invalidFiles = [
        new MockFile('empty.pdf', 0, 'application/pdf')
      ];

      const result = validateFileUploads(invalidFiles);
      expect(result.isValid).toBe(false);
      expect(result.errors.some(e => e.fileName === 'empty.pdf')).toBe(true);
    });

    test('should reject too many files', () => {
      const tooManyFiles = Array.from({ length: 25 }, (_, i) => 
        new MockFile(`file${i}.pdf`, 1024 * 1024, 'application/pdf')
      );

      const result = validateFileUploads(tooManyFiles);
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  describe('Error Message Formatting', () => {
    test('should format string errors', () => {
      const error = 'Simple error message';
      const formatted = formatErrorMessage(error);
      expect(formatted).toBe('Simple error message');
    });

    test('should format Error objects', () => {
      const error = new Error('Test error');
      const formatted = formatErrorMessage(error);
      expect(formatted).toBe('Test error');
    });

    test('should format API error responses', () => {
      const error = { error: 'API error message' };
      const formatted = formatErrorMessage(error);
      expect(formatted).toBe('API error message');
    });

    test('should handle unknown error formats', () => {
      const error = { unknown: 'format' };
      const formatted = formatErrorMessage(error);
      expect(formatted).toBe('An unexpected error occurred');
    });
  });
});

// Integration test helpers
export const testHelpers = {
  /**
   * Create a test transaction
   */
  createTestTransaction: () => ({
    type: 'Vehicle Purchase',
    description: 'Test vehicle purchase',
    amount: 15000.00,
    currency: 'CAD',
    date: new Date().toISOString().split('T')[0],
    addedBy: '<EMAIL>',
    notes: 'Test transaction for integration testing'
  }),

  /**
   * Create test files
   */
  createTestFiles: () => [
    new MockFile('test-receipt.pdf', 1024 * 1024, 'application/pdf'),
    new MockFile('test-invoice.jpg', 2 * 1024 * 1024, 'image/jpeg')
  ],

  /**
   * Mock API response
   */
  mockApiResponse: (success = true, data = null, error = null) => ({
    success,
    data,
    error
  }),

  /**
   * Test API endpoint
   */
  testApiEndpoint: async (url, options = {}) => {
    try {
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers
        },
        ...options
      });

      const data = await response.json();
      return {
        status: response.status,
        success: response.ok,
        data
      };
    } catch (error) {
      return {
        status: 500,
        success: false,
        error: error.message
      };
    }
  }
};

// Manual testing checklist
export const manualTestChecklist = [
  '✅ Create new transaction with all required fields',
  '✅ Create transaction with optional vehicle selection',
  '✅ Upload multiple receipt files (PDF, JPG, PNG)',
  '✅ View transaction list with filters',
  '✅ Search transactions by description',
  '✅ Filter by date range',
  '✅ Filter by transaction type',
  '✅ Filter by income/expense',
  '✅ View individual transaction details',
  '✅ Download receipt files',
  '✅ Export data as CSV',
  '✅ Export data as PDF',
  '✅ Delete transaction (with confirmation)',
  '✅ Handle file upload errors gracefully',
  '✅ Handle network errors gracefully',
  '✅ Validate form inputs properly',
  '✅ Show loading states during operations',
  '✅ Display success/error messages',
  '✅ Responsive design on mobile devices',
  '✅ Navigation between ledger pages works'
];

console.log('Ledger System Test Suite Ready');
console.log('Manual Testing Checklist:', manualTestChecklist);
