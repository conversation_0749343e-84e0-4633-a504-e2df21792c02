// src/app/admin/page.jsx - Redirect to dashboard
'use client';
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function AdminRedirect() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to dashboard by default
    router.replace('/admin/dashboard');
  }, [router]);

  return (
    <div style={{
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      height: '100vh',
      color: 'var(--text-primary)'
    }}>
      <p>Redirecting to dashboard...</p>
    </div>
  );
}
    try {
      setLoading(true);
      const queryParams = new URLSearchParams();

      // Mark this as an admin request
      queryParams.append('admin', 'true');

      if (filters.search) queryParams.append('search', filters.search);
      if (filters.make) queryParams.append('make', filters.make);
      if (filters.year) queryParams.append('year', filters.year);
      if (filters.status !== 'all') queryParams.append('status', filters.status);

      const res = await fetch(`/api/vehicles?${queryParams}`);
      const data = await res.json();

      if (data.success) {
        setVehicles(data.data || []);
      } else {
        throw new Error(data.error || 'Failed to fetch vehicles');
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchVehicles();
  }, [filters]);

  const handleDelete = async (vehicleId) => {
    try {
      // Get user authentication data
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      if (!user.email || !user.role) {
        showMessage('Authentication error. Please log in again.', 'error');
        return;
      }

      const res = await fetch(`/api/vehicles/${vehicleId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userEmail: user.email,
          userRole: user.role
        }),
      });

      const data = await res.json();

      if (res.ok && data.success) {
        // Refresh the vehicles list from the server
        await fetchVehicles();
        showMessage('Vehicle deleted successfully.', 'success');
      } else {
        throw new Error(data.error || 'Failed to delete vehicle');
      }
    } catch (err) {
      showMessage('Error deleting vehicle: ' + err.message, 'error');
    }
  };

  const handleComplianceForm = async (formType) => {
    try {
      // For now, generate a basic form with placeholder data
      const formData = {
        dealerInfo: {
          name: 'FazeNAuto',
          address: '1120 Meighen Way',
          phone: '************',
          email: '<EMAIL>'
        },
        date: new Date().toLocaleDateString()
      };

      const response = await fetch('/api/compliance-forms', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          formType,
          data: formData
        })
      });

      const result = await response.json();

      if (result.success) {
        // Create a download link for the PDF
        const blob = new Blob([new Uint8Array(result.pdfBuffer.data)], { type: 'application/pdf' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = result.filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        showMessage(`${result.message} - PDF downloaded successfully!`, 'success');
      } else {
        showMessage(`Error generating form: ${result.error}`, 'error');
      }
    } catch (error) {
      console.error('Compliance form error:', error);
      showMessage('Error generating compliance form. Please try again.', 'error');
    }
  };

  const showMessage = (message, type = 'success') => {
    setMessage({ text: message, type });
    setTimeout(() => setMessage(null), 5000);
  };

  const handleImageProcessing = async (action) => {
    // Check if vehicles are selected for all actions that require them
    const actionsRequiringSelection = ['process-selected', 'add-watermarks', 'resize-platforms'];

    if (actionsRequiringSelection.includes(action) && selectedVehicles.length === 0) {
      showMessage('Please select vehicles first to process their images.', 'error');
      return;
    }

    try {
      let endpoint = '/api/image-processing';
      let requestData = {
        action,
        vehicleIds: selectedVehicles,
        options: {
          autoResizeFacebook: true,
          autoResizeAutoTrader: true,
          addWatermark: true,
          blurLicensePlates: false
        }
      };

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      });

      const result = await response.json();

      if (result.success) {
        showMessage(result.message || 'Image processing completed successfully!', 'success');
        if (action === 'view-stats') {
          // Display processing stats in a more detailed way
          const stats = result.stats;
          showMessage(`Processing Stats:\n- Total Images: ${stats.totalImages}\n- Processed: ${stats.processed}\n- Watermarked: ${stats.watermarked}\n- Resized: ${stats.resized}`, 'info');
        }
        // Refresh vehicle data to show updated images
        fetchVehicles();
      } else {
        showMessage(result.error || 'Image processing failed', 'error');
      }
    } catch (error) {
      console.error('Image processing error:', error);
      showMessage('Error processing images. Please try again.', 'error');
    }
  };

  const handleAdGeneration = async (action) => {
    if (selectedVehicles.length === 0) {
      showMessage('Please select vehicles first to generate ads.', 'error');
      return;
    }

    try {
      // Get VINs from selected vehicles
      const selectedVehicleData = vehicles.filter(v => selectedVehicles.includes(v._id));

      if (selectedVehicleData.length === 0) {
        showMessage('No vehicle data found for selected vehicles.', 'error');
        return;
      }

      const results = [];

      for (const vehicle of selectedVehicleData) {
        if (!vehicle.vin) {
          results.push({
            vehicle: `${vehicle.year} ${vehicle.make} ${vehicle.model}`,
            error: 'No VIN available'
          });
          continue;
        }

        const platforms = action === 'generate-facebook' ? ['facebook'] :
                         action === 'generate-kijiji' ? ['kijiji'] :
                         action === 'generate-autotrader' ? ['autotrader'] :
                         ['facebook', 'kijiji', 'autotrader'];

        const response = await fetch('/api/ad-generator', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            vin: vehicle.vin,
            platforms,
            includeEmojis: true,
            tone: 'professional'
          })
        });

        const result = await response.json();

        if (result.success) {
          results.push({
            vehicle: `${vehicle.year} ${vehicle.make} ${vehicle.model}`,
            success: true,
            ads: result.ads
          });
        } else {
          results.push({
            vehicle: `${vehicle.year} ${vehicle.make} ${vehicle.model}`,
            error: result.error
          });
        }
      }

      // Display results
      const successCount = results.filter(r => r.success).length;
      const errorCount = results.filter(r => r.error).length;

      showMessage(`Ad Generation Complete: ✅ ${successCount} successful, ❌ ${errorCount} failed. Check console for detailed results.`, successCount > 0 ? 'success' : 'error');
      console.log('Ad Generation Results:', results);

    } catch (error) {
      console.error('Ad generation error:', error);
      showMessage('Error generating ads. Please try again.', 'error');
    }
  };

  const handleCSVExport = async (platform) => {
    if (selectedVehicles.length === 0) {
      showMessage('Please select vehicles first to export CSV.', 'error');
      return;
    }

    try {
      const selectedVehicleData = vehicles.filter(v => selectedVehicles.includes(v._id));

      if (selectedVehicleData.length === 0) {
        showMessage('No vehicle data found for selected vehicles.', 'error');
        return;
      }

      // Generate CSV data based on platform requirements
      const csvData = generateCSVForPlatform(selectedVehicleData, platform);

      // Create and download CSV file
      const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `fazenauto_${platform}_export_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      showMessage(`Successfully exported ${selectedVehicleData.length} vehicles to ${platform} CSV format.`, 'success');

    } catch (error) {
      console.error('CSV export error:', error);
      showMessage('Error exporting CSV. Please try again.', 'error');
    }
  };

  const generateCSVForPlatform = (vehicles, platform) => {
    const headers = getCSVHeaders(platform);
    const rows = vehicles.map(vehicle => formatVehicleForCSV(vehicle, platform));

    return [headers.join(','), ...rows.map(row => row.join(','))].join('\n');
  };

  const getCSVHeaders = (platform) => {
    const commonHeaders = ['Title', 'Description', 'Price', 'Year', 'Make', 'Model', 'VIN', 'Mileage', 'Images'];

    switch (platform) {
      case 'facebook':
        return [...commonHeaders, 'Category', 'Condition', 'Location'];
      case 'autotrader':
        return [...commonHeaders, 'Body Style', 'Transmission', 'Fuel Type', 'Drivetrain'];
      case 'kijiji':
        return [...commonHeaders, 'Category', 'Ad Type', 'Location'];
      case 'craigslist':
        return [...commonHeaders, 'Category', 'Location', 'Contact'];
      default:
        return commonHeaders;
    }
  };

  const formatVehicleForCSV = (vehicle, platform) => {
    const escapeCSV = (str) => {
      if (!str) return '';
      const stringValue = String(str);
      if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
        return `"${stringValue.replace(/"/g, '""')}"`;
      }
      return stringValue;
    };

    const title = `${vehicle.year} ${vehicle.make} ${vehicle.model}`;
    const description = generateAdDescription(vehicle, platform);
    const price = vehicle.price || '';
    const images = vehicle.images ? vehicle.images.slice(0, 10).join(';') : '';

    const baseData = [
      escapeCSV(title),
      escapeCSV(description),
      escapeCSV(price),
      escapeCSV(vehicle.year),
      escapeCSV(vehicle.make),
      escapeCSV(vehicle.model),
      escapeCSV(vehicle.vin),
      escapeCSV(vehicle.mileage),
      escapeCSV(images)
    ];

    switch (platform) {
      case 'facebook':
        return [...baseData, 'Vehicle', 'Used', '1120 Meighen Way'];
      case 'autotrader':
        return [...baseData, vehicle.bodyStyle || '', vehicle.transmission || '', vehicle.fuelType || '', vehicle.drivetrain || ''];
      case 'kijiji':
        return [...baseData, 'Cars & Vehicles', 'For Sale', '1120 Meighen Way'];
      case 'craigslist':
        return [...baseData, 'Cars & Trucks', '1120 Meighen Way', '************'];
      default:
        return baseData;
    }
  };

  const generateAdDescription = (vehicle, platform) => {
    const features = [];
    if (vehicle.engine) features.push(`Engine: ${vehicle.engine}`);
    if (vehicle.transmission) features.push(`Transmission: ${vehicle.transmission}`);
    if (vehicle.fuelType) features.push(`Fuel: ${vehicle.fuelType}`);
    if (vehicle.drivetrain) features.push(`Drivetrain: ${vehicle.drivetrain}`);

    const featuresText = features.length > 0 ? `\n\nKey Features:\n${features.join('\n')}` : '';

    return `${vehicle.year} ${vehicle.make} ${vehicle.model} - Excellent condition vehicle available at FazeNAuto.${featuresText}\n\nContact us at ************ for more information.\n\nSold as-is. Located at 1120 Meighen Way.`;
  };

  const handleTotalVehiclesClick = () => {
    router.push('/inventory/used-cars');
  };

  return (
    <div className={styles.adminDashboard}>
      <div className={styles.header}>
        <div className={styles.headerTop}>
          <div className={styles.titleSection}>
            <h1>{t('admin.vehicle_inventory_management', 'Vehicle Inventory Management')}</h1>
            <button
              onClick={() => router.push('/admin/vehicles/upload')}
              className={styles.addVehicleBtn}
              title="Upload New Vehicle"
            >
              +
            </button>
          </div>
          <div className={styles.headerActions}>
            <div className={styles.actionButtons}>
              <button
                onClick={() => setShowSyndicationPanel(!showSyndicationPanel)}
                className={`enhanced-button ${styles.actionButton} ${showSyndicationPanel ? styles.active : ''}`}
              >
                📤 {showSyndicationPanel ? t('admin.hide_syndication', 'Hide Syndication') : t('admin.show_syndication', 'Show Syndication')}
              </button>
              <button
                onClick={() => setShowVisitorTracker(true)}
                className={`enhanced-button ${styles.actionButton}`}
              >
                👥 {t('admin.visitors', 'Visitors')}
              </button>
              <button
                onClick={() => setShowImageProcessingPanel(!showImageProcessingPanel)}
                className={`enhanced-button ${styles.actionButton} ${showImageProcessingPanel ? styles.active : ''}`}
              >
                🖼️ {showImageProcessingPanel ? t('admin.hide_image_processing', 'Hide Image Processing') : t('admin.show_image_processing', 'Show Image Processing')}
              </button>
              <button
                onClick={() => setShowAdGeneratorPanel(!showAdGeneratorPanel)}
                className={`enhanced-button ${styles.actionButton} ${showAdGeneratorPanel ? styles.active : ''}`}
              >
                ✨ {showAdGeneratorPanel ? t('admin.hide_ad_generator', 'Hide Ad Generator') : t('admin.show_ad_generator', 'Show Ad Generator')}
              </button>
            </div>
            {selectedVehicles.length > 0 && (
              <span className={styles.selectedCount}>
                {selectedVehicles.length} {t('admin.vehicles_selected', 'vehicle(s) selected')}
              </span>
            )}
          </div>
        </div>
        <div className={styles.stats}>
          <div
            className={`${styles.statCard} ${styles.clickable}`}
            onClick={handleTotalVehiclesClick}
            title={t('admin.click_to_view_all', 'Click to view all vehicles')}
          >
            <h3>{t('admin.total_vehicles', 'Total Vehicles')}</h3>
            <p>{vehicles.length}</p>
          </div>
          <div className={styles.statCard}>
            <h3>{t('admin.active_listings', 'Active Listings')}</h3>
            <p>{vehicles.filter(v => v.status === 'active').length}</p>
          </div>
          <div className={styles.statCard}>
            <h3>{t('admin.draft_listings', 'Draft Listings')}</h3>
            <p>{vehicles.filter(v => v.status === 'draft').length}</p>
          </div>
          <div className={styles.statCard}>
            <h3>{t('admin.sold_vehicles', 'Sold Vehicles')}</h3>
            <p>{vehicles.filter(v => v.status === 'sold').length}</p>
          </div>
          <div className={styles.statCard}>
            <h3>{t('admin.archived_vehicles', 'Archived Vehicles')}</h3>
            <p>{vehicles.filter(v => v.status === 'archived').length}</p>
          </div>
        </div>
      </div>

      {/* Message Display */}
      {message && (
        <div className={`${styles.message} ${styles[message.type]}`}>
          <span>{message.text}</span>
          <button
            onClick={() => setMessage(null)}
            className={styles.messageClose}
          >
            ×
          </button>
        </div>
      )}

      {/* Syndication Panel */}
      {showSyndicationPanel && (
        <div className={styles.panelWrapper}>
          <SyndicationPanel
            selectedVehicles={selectedVehicles}
            onRefresh={fetchVehicles}
          />
        </div>
      )}



      {/* Image Processing Panel */}
      {showImageProcessingPanel && (
        <div className={styles.panelWrapper}>
          <div className={styles.panel}>
          <div className={styles.panelHeader}>
            <h3>🖼️ {t('admin.image_processing_pipeline', 'Image Processing Pipeline')}</h3>
            <p>{t('admin.batch_process_images', 'Batch process vehicle images for multiple platforms')}</p>
          </div>
          <div className={styles.panelContent}>
            <div className={styles.imageProcessingActions}>
              <button
                className={styles.processingButton}
                onClick={() => handleImageProcessing('process-selected')}
              >
                📸 Process Selected Vehicle Images
              </button>
              <button
                className={styles.processingButton}
                onClick={() => handleImageProcessing('add-watermarks')}
              >
                🏷️ Add Watermarks
              </button>
              <button
                className={styles.processingButton}
                onClick={() => handleImageProcessing('resize-platforms')}
              >
                📐 Resize for Platforms
              </button>
              <button
                className={styles.processingButton}
                onClick={() => handleImageProcessing('view-stats')}
              >
                📊 View Processing Stats
              </button>
            </div>
            <div className={styles.processingOptions}>
              <label>
                <input type="checkbox" defaultChecked /> Auto-resize for Facebook
              </label>
              <label>
                <input type="checkbox" defaultChecked /> Auto-resize for AutoTrader
              </label>
              <label>
                <input type="checkbox" defaultChecked /> Add FazeNAuto watermark
              </label>
              <label>
                <input type="checkbox" /> Blur license plates
              </label>
            </div>
          </div>
        </div>
        </div>
      )}

      {/* Ad Generator Panel */}
      {showAdGeneratorPanel && (
        <div className={styles.panelWrapper}>
          <div className={styles.panel}>
          <div className={styles.panelHeader}>
            <h3>✨ {t('admin.vin_to_ad_generator', 'VIN-to-Ad Generator')}</h3>
            <p>{t('admin.generate_compelling_ads', 'Generate compelling ads from VIN using AI')}</p>
          </div>
          <div className={styles.panelContent}>
            <div className={styles.adGeneratorActions}>
              <button
                className={styles.adButton}
                onClick={() => handleAdGeneration('generate-all')}
                disabled={selectedVehicles.length === 0}
              >
                🚗 Generate Ads for Selected Vehicles
              </button>
              <button
                className={styles.adButton}
                onClick={() => handleAdGeneration('generate-facebook')}
                disabled={selectedVehicles.length === 0}
              >
                📝 Generate Facebook Marketplace Ad
              </button>
              <button
                className={styles.adButton}
                onClick={() => handleAdGeneration('generate-kijiji')}
                disabled={selectedVehicles.length === 0}
              >
                📰 Generate Kijiji Ad
              </button>
              <button
                className={styles.adButton}
                onClick={() => handleAdGeneration('generate-autotrader')}
                disabled={selectedVehicles.length === 0}
              >
                🏪 Generate AutoTrader Ad
              </button>
            </div>
            <div className={styles.csvExportSection}>
              <h4>📊 CSV Export for Manual Posting</h4>
              <div className={styles.csvExportActions}>
                <button
                  className={styles.csvButton}
                  onClick={() => handleCSVExport('facebook')}
                  disabled={selectedVehicles.length === 0}
                >
                  📝 Export Facebook CSV
                </button>
                <button
                  className={styles.csvButton}
                  onClick={() => handleCSVExport('autotrader')}
                  disabled={selectedVehicles.length === 0}
                >
                  🏪 Export AutoTrader CSV
                </button>
                <button
                  className={styles.csvButton}
                  onClick={() => handleCSVExport('kijiji')}
                  disabled={selectedVehicles.length === 0}
                >
                  📰 Export Kijiji CSV
                </button>
                <button
                  className={styles.csvButton}
                  onClick={() => handleCSVExport('craigslist')}
                  disabled={selectedVehicles.length === 0}
                >
                  📋 Export Craigslist CSV
                </button>
              </div>
            </div>
            <div className={styles.adOptions}>
              <label>
                <input type="checkbox" defaultChecked /> Include emojis
              </label>
              <label>
                <input type="checkbox" /> Generate bilingual (French)
              </label>
              <label>
                Tone:
                <select className={styles.toneSelect}>
                  <option value="professional">Professional</option>
                  <option value="casual">Casual</option>
                  <option value="enthusiastic">Enthusiastic</option>
                </select>
              </label>
            </div>
          </div>
        </div>
        </div>
      )}

      <div className={styles.tableWrapper}>
        <VehicleTable
          vehicles={vehicles}
          loading={loading}
          error={error}
          filters={filters}
          setFilters={setFilters}
          onDelete={handleDelete}
          onRefresh={fetchVehicles}
          selectedVehicles={selectedVehicles}
          setSelectedVehicles={setSelectedVehicles}
          showSelection={showSyndicationPanel || showImageProcessingPanel || showAdGeneratorPanel}
        />
      </div>

      {/* Visitor Tracker Modal */}
      <VisitorTracker
        isOpen={showVisitorTracker}
        onClose={() => setShowVisitorTracker(false)}
      />

      {/* Mobile Floating Action Button */}
      <button
        onClick={() => router.push('/admin/vehicles/upload')}
        className={styles.mobileFloatingActionButton}
        title="Upload New Vehicle"
        aria-label="Upload New Vehicle"
      >
        +
      </button>
    </div>
  );
}
