// src/app/admin/page.jsx - Redirect to dashboard
'use client';
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function AdminRedirect() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to dashboard by default
    router.replace('/admin/dashboard');
  }, [router]);

  return (
    <div style={{
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      height: '100vh',
      color: 'var(--text-primary)'
    }}>
      <p>Redirecting to dashboard...</p>
    </div>
  );
}
