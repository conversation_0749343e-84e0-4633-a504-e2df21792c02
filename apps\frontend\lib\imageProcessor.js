import path from 'path';

// Conditional imports for server-side only
let sharp, AWS, s3, fs;

try {
  // Only import these in server environment
  if (typeof window === 'undefined') {
    sharp = require('sharp');
    fs = require('fs');
    // AWS SDK will be installed separately for production
    // For now, we'll use a mock implementation
  }
} catch (error) {
  console.warn('Image processing dependencies not available:', error.message);
}

// Mock S3 configuration for development
const BUCKET_NAME = process.env.CUSTOM_AWS_S3_BUCKET_NAME || 'fazenauto-dev-bucket';

/**
 * Image Processing Pipeline for Vehicle Photos
 * Handles batch uploads, resizing, watermarking, and optimization
 */
export class ImageProcessor {
  constructor() {
    this.platforms = {
      facebook: { width: 1200, height: 900, quality: 85 },
      autotrader: { width: 1024, height: 768, quality: 90 },
      cargurus: { width: 1200, height: 800, quality: 85 },
      kijiji: { width: 1000, height: 750, quality: 80 },
      standard: { width: 1200, height: 900, quality: 85 }
    };

    // Check if dependencies are available
    this.isAvailable = typeof window === 'undefined' && sharp;

    if (!this.isAvailable) {
      console.warn('ImageProcessor: Dependencies not available, using mock implementation');
    }
  }

  /**
   * Process batch of vehicle images
   * @param {Array} imageFiles - Array of image files or URLs
   * @param {Object} options - Processing options
   * @returns {Promise<Array>} - Array of processed image URLs
   */
  async processBatchImages(imageFiles, options = {}) {
    const {
      vehicleId,
      platforms = ['standard'],
      addWatermark = true,
      enhanceLighting = true,
      blurLicensePlates = true,
      maxImages = 50
    } = options;

    if (!imageFiles || imageFiles.length === 0) {
      throw new Error('No images provided for processing');
    }

    if (imageFiles.length > maxImages) {
      throw new Error(`Maximum ${maxImages} images allowed`);
    }

    // Return mock data if dependencies aren't available
    if (!this.isAvailable) {
      return this.getMockProcessingResult(imageFiles, platforms);
    }

    const processedImages = [];
    const errors = [];

    for (let i = 0; i < imageFiles.length; i++) {
      try {
        const imageFile = imageFiles[i];
        const imageResults = {};

        // Process for each platform
        for (const platform of platforms) {
          const processedUrl = await this.processImage(imageFile, {
            vehicleId,
            platform,
            imageIndex: i,
            addWatermark,
            enhanceLighting,
            blurLicensePlates
          });
          imageResults[platform] = processedUrl;
        }

        processedImages.push({
          originalIndex: i,
          platforms: imageResults,
          status: 'success'
        });

      } catch (error) {
        console.error(`Error processing image ${i}:`, error);
        errors.push({
          imageIndex: i,
          error: error.message
        });
      }
    }

    return {
      success: true,
      processedImages,
      errors,
      totalProcessed: processedImages.length,
      totalErrors: errors.length
    };
  }

  /**
   * Process single image for specific platform
   * @param {File|Buffer|String} imageInput - Image file, buffer, or URL
   * @param {Object} options - Processing options
   * @returns {Promise<String>} - S3 URL of processed image
   */
  async processImage(imageInput, options = {}) {
    const {
      vehicleId,
      platform = 'standard',
      imageIndex = 0,
      addWatermark = true,
      enhanceLighting = true,
      blurLicensePlates = true
    } = options;

    // Get platform specifications
    const specs = this.platforms[platform] || this.platforms.standard;

    // Get image buffer
    let imageBuffer;
    if (typeof imageInput === 'string') {
      // URL - download the image
      const response = await fetch(imageInput);
      imageBuffer = Buffer.from(await response.arrayBuffer());
    } else if (imageInput instanceof File) {
      // File object
      imageBuffer = Buffer.from(await imageInput.arrayBuffer());
    } else {
      // Assume it's already a buffer
      imageBuffer = imageInput;
    }

    // Start Sharp processing pipeline
    let sharpImage = sharp(imageBuffer);

    // Get image metadata
    const metadata = await sharpImage.metadata();
    console.log(`Processing ${platform} image: ${metadata.width}x${metadata.height}`);

    // 1. Auto-rotate based on EXIF
    sharpImage = sharpImage.rotate();

    // 2. Resize to platform specifications
    sharpImage = sharpImage.resize(specs.width, specs.height, {
      fit: 'inside',
      withoutEnlargement: true
    });

    // 3. Enhance lighting if requested
    if (enhanceLighting) {
      sharpImage = sharpImage
        .modulate({
          brightness: 1.1,  // Slightly brighter
          saturation: 1.05  // Slightly more saturated
        })
        .sharpen(1, 1, 0.5); // Subtle sharpening
    }

    // 4. Add watermark if requested
    if (addWatermark) {
      sharpImage = await this.addWatermark(sharpImage, platform);
    }

    // 5. Blur license plates if requested
    if (blurLicensePlates) {
      // Note: This is a placeholder - real license plate detection would require ML
      // For now, we'll skip this step and implement it later with proper ML models
      console.log('License plate blurring: Placeholder (requires ML implementation)');
    }

    // 6. Optimize and compress
    const processedBuffer = await sharpImage
      .jpeg({ 
        quality: specs.quality,
        progressive: true,
        mozjpeg: true
      })
      .toBuffer();

    // 7. Upload to S3
    const s3Key = `vehicles/${vehicleId}/processed/${platform}_${imageIndex}_${Date.now()}.jpg`;
    const s3Url = await this.uploadToS3(processedBuffer, s3Key);

    return s3Url;
  }

  /**
   * Add FazeNAuto watermark to image
   * @param {Sharp} sharpImage - Sharp image instance
   * @param {String} platform - Target platform
   * @returns {Promise<Sharp>} - Sharp image with watermark
   */
  async addWatermark(sharpImage, platform) {
    try {
      // Use the watermark image file
      const watermarkPath = path.join(process.cwd(), 'apps/frontend/public/assets/images/watermark.png');

      // Check if watermark file exists
      if (!fs.existsSync(watermarkPath)) {
        console.warn('⚠️ Watermark file not found, using text watermark');
        // Fallback to text watermark
        const watermarkSvg = `
          <svg width="200" height="40">
            <defs>
              <style>
                .watermark-text {
                  font-family: Arial, sans-serif;
                  font-size: 24px;
                  font-weight: bold;
                  fill: white;
                  stroke: rgba(0,0,0,0.5);
                  stroke-width: 1;
                }
              </style>
            </defs>
            <text x="10" y="30" class="watermark-text">FazeNAuto</text>
          </svg>
        `;

        const watermarkBuffer = Buffer.from(watermarkSvg);

        // Add watermark to bottom-right corner
        return sharpImage.composite([{
          input: watermarkBuffer,
          gravity: 'southeast',
          blend: 'over'
        }]);
      }

      // Use PNG watermark file
      const watermarkBuffer = fs.readFileSync(watermarkPath);

      // Resize watermark to appropriate size (max 200px wide)
      const resizedWatermark = await sharp(watermarkBuffer)
        .resize(200, null, {
          withoutEnlargement: true,
          fit: 'inside'
        })
        .png()
        .toBuffer();

      // Add watermark to bottom-right corner with some padding
      return sharpImage.composite([{
        input: resizedWatermark,
        gravity: 'southeast',
        blend: 'over'
      }]);

    } catch (error) {
      console.warn('Watermark failed, continuing without:', error.message);
      return sharpImage;
    }
  }

  /**
   * Upload processed image to S3
   * @param {Buffer} imageBuffer - Processed image buffer
   * @param {String} s3Key - S3 object key
   * @returns {Promise<String>} - S3 URL
   */
  async uploadToS3(imageBuffer, s3Key) {
    // Return mock URL if S3 isn't configured
    if (!s3) {
      const mockUrl = `https://${BUCKET_NAME}.s3.amazonaws.com/${s3Key}`;
      console.log('Mock S3 upload:', mockUrl);
      return mockUrl;
    }

    try {
      const uploadParams = {
        Bucket: BUCKET_NAME,
        Key: s3Key,
        Body: imageBuffer,
        ContentType: 'image/jpeg',
        ACL: 'public-read',
        CacheControl: 'max-age=31536000' // 1 year cache
      };

      const result = await s3.upload(uploadParams).promise();
      return result.Location;

    } catch (error) {
      console.error('S3 upload failed:', error);
      throw new Error(`Failed to upload image to S3: ${error.message}`);
    }
  }

  /**
   * Get image processing statistics
   * @param {String} vehicleId - Vehicle ID
   * @returns {Promise<Object>} - Processing statistics
   */
  async getProcessingStats(vehicleId) {
    try {
      // List all processed images for this vehicle
      const listParams = {
        Bucket: BUCKET_NAME,
        Prefix: `vehicles/${vehicleId}/processed/`
      };

      const result = await s3.listObjectsV2(listParams).promise();
      
      const stats = {
        totalImages: result.Contents.length,
        platforms: {},
        totalSize: 0,
        lastProcessed: null
      };

      result.Contents.forEach(obj => {
        stats.totalSize += obj.Size;
        if (!stats.lastProcessed || obj.LastModified > stats.lastProcessed) {
          stats.lastProcessed = obj.LastModified;
        }

        // Extract platform from key
        const platform = obj.Key.split('/').pop().split('_')[0];
        if (!stats.platforms[platform]) {
          stats.platforms[platform] = 0;
        }
        stats.platforms[platform]++;
      });

      return stats;

    } catch (error) {
      console.error('Failed to get processing stats:', error);
      return {
        totalImages: 0,
        platforms: {},
        totalSize: 0,
        lastProcessed: null,
        error: error.message
      };
    }
  }

  /**
   * Get mock processing result for development
   * @param {Array} imageFiles - Image files
   * @param {Array} platforms - Target platforms
   * @returns {Object} - Mock processing result
   */
  getMockProcessingResult(imageFiles, platforms) {
    const processedImages = imageFiles.map((file, index) => {
      const platformResults = {};
      platforms.forEach(platform => {
        platformResults[platform] = `https://mock-bucket.s3.amazonaws.com/vehicles/mock/processed/${platform}_${index}_${Date.now()}.jpg`;
      });

      return {
        originalIndex: index,
        platforms: platformResults,
        status: 'success'
      };
    });

    return {
      success: true,
      processedImages,
      errors: [],
      totalProcessed: processedImages.length,
      totalErrors: 0
    };
  }
}

export default ImageProcessor;
