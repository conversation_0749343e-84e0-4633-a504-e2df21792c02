import { NextResponse } from 'next/server';
import { connectToDatabase } from '../../../../lib/dbConnect';
import Vehicle from '../../../../lib/models/Vehicle';

// GET /api/ledger/vehicles - Search vehicles for ledger form
export async function GET(request) {
  try {
    await connectToDatabase();

    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search') || '';
    const limit = parseInt(searchParams.get('limit')) || 20;

    let query = {};

    if (search) {
      const searchRegex = new RegExp(search, 'i');
      query = {
        $or: [
          { make: searchRegex },
          { model: searchRegex },
          { vin: searchRegex },
          { year: isNaN(parseInt(search)) ? undefined : parseInt(search) }
        ].filter(Boolean)
      };
    }

    const vehicles = await Vehicle.find(query)
      .select('make model year vin color price status displayName')
      .sort({ year: -1, make: 1, model: 1 })
      .limit(limit)
      .lean();

    // Format vehicles for dropdown/search
    const formattedVehicles = vehicles.map(vehicle => ({
      _id: vehicle._id,
      label: `${vehicle.year} ${vehicle.make} ${vehicle.model} - ${vehicle.vin}`,
      value: vehicle._id,
      make: vehicle.make,
      model: vehicle.model,
      year: vehicle.year,
      vin: vehicle.vin,
      color: vehicle.color,
      price: vehicle.price,
      status: vehicle.status,
      displayName: vehicle.displayName || `${vehicle.year} ${vehicle.make} ${vehicle.model}`
    }));

    return NextResponse.json({
      success: true,
      data: formattedVehicles
    });

  } catch (error) {
    console.error('Error searching vehicles:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to search vehicles' },
      { status: 500 }
    );
  }
}
