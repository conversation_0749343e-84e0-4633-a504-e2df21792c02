/* Shared Market Component Styles */
/* Used by VehicleLookup and VehicleInfo components for market data display */

.marketCard {
  background: var(--card-background, #ffffff);
  border: 1px solid var(--border-color, #e2e8f0);
  border-radius: 8px;
  padding: 1.5rem;
}

[data-theme="dark"] .marketCard {
  background: #161b22;
  border-color: #30363d;
}

.marketCard h3 {
  color: var(--text-primary, #2d3748);
  margin-bottom: 1rem;
  font-size: 1.2rem;
  font-weight: 600;
}

.marketPrice {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--primary-color, #007bff);
  margin-bottom: 0.5rem;
}

.marketRange {
  color: var(--text-secondary, #4a5568);
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.marketInfo {
  color: var(--text-secondary, #4a5568);
  font-size: 0.85rem;
  font-style: italic;
}

.comparableList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1rem;
}

.comparableItem {
  background: var(--background-secondary, #f8f9fa);
  padding: 1rem;
  border-radius: 6px;
  border-left: 3px solid var(--primary-color, #007bff);
}

[data-theme="dark"] .comparableItem {
  background: #0d1117;
}

.comparableItem p {
  margin: 0.25rem 0;
  font-size: 0.9rem;
}

.comparableItem strong {
  color: var(--text-primary, #2d3748);
}

.priceTrend {
  font-size: 1rem;
  margin-bottom: 1rem;
}

.trend {
  font-weight: 600;
  text-transform: capitalize;
}

.trend.increasing {
  color: #28a745;
}

.trend.decreasing {
  color: #dc3545;
}

.trend.stable {
  color: var(--text-secondary, #4a5568);
}

.historyList {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.historyItem {
  background: var(--background-secondary, #f8f9fa);
  padding: 0.75rem;
  border-radius: 4px;
  border-left: 2px solid var(--primary-color, #007bff);
}

[data-theme="dark"] .historyItem {
  background: #0d1117;
}

.historyItem p {
  margin: 0.25rem 0;
  font-size: 0.85rem;
}

.historyDate {
  font-weight: 600;
  color: var(--text-primary, #2d3748);
}

.historyPrice {
  font-weight: 600;
  color: var(--primary-color, #007bff);
}
