import React from 'react';
import styles from './ValidationModal.module.css';

const ValidationModal = ({ isOpen, onClose, onContinue, errors, title = "Required Fields Missing" }) => {
  if (!isOpen) return null;

  return (
    <div className={styles.modalOverlay} onClick={onClose}>
      <div className={styles.modalContent} onClick={(e) => e.stopPropagation()}>
        <div className={styles.modalHeader}>
          <h3>{title}</h3>
          <button className={styles.closeButton} onClick={onClose}>
            ×
          </button>
        </div>
        
        <div className={styles.modalBody}>
          <div className={styles.errorIcon}>
            ⚠️
          </div>
          <p className={styles.errorMessage}>
            Please fill in the following required fields:
          </p>
          <ul className={styles.errorList}>
            {errors.map((error, index) => (
              <li key={index} className={styles.errorItem}>
                {error}
              </li>
            ))}
          </ul>
          <p className={styles.continueMessage}>
            Would you like to continue anyway? Some fields may be filled manually after printing.
          </p>
        </div>
        
        <div className={styles.modalFooter}>
          <button 
            className={styles.cancelButton} 
            onClick={onClose}
          >
            Cancel
          </button>
          <button 
            className={styles.continueButton} 
            onClick={onContinue}
          >
            Continue Anyway
          </button>
        </div>
      </div>
    </div>
  );
};

export default ValidationModal;
