.container {
  padding: 2rem;
  max-width: 1000px;
  margin: 0 auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  gap: 2rem;
}

.titleSection h1 {
  margin: 0 0 0.5rem 0;
  color: #1a1a1a;
  font-size: 2rem;
}

.titleSection p {
  margin: 0;
  color: #666;
  font-size: 1rem;
}

.actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.backButton {
  background: #6b7280;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 500;
  transition: background-color 0.2s;
  white-space: nowrap;
}

.backButton:hover {
  background: #4b5563;
}

.deleteButton {
  background: #ef4444;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
  white-space: nowrap;
}

.deleteButton:hover:not(:disabled) {
  background: #dc2626;
}

.deleteButton:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

/* Loading and Error States */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  color: #6b7280;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error,
.notFound {
  text-align: center;
  padding: 4rem 2rem;
}

.error h2,
.notFound h2 {
  margin: 0 0 1rem 0;
  color: #dc2626;
}

.error p,
.notFound p {
  margin: 0 0 2rem 0;
  color: #6b7280;
}

/* Content Layout */
.content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Card Styles */
.card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.cardHeader {
  background: #f9fafb;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.cardHeader h2 {
  margin: 0;
  color: #374151;
  font-size: 1.25rem;
  font-weight: 600;
}

.cardContent {
  padding: 1.5rem;
}

/* Type Tag */
.typeTag {
  display: inline-block;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.typeTag.sale {
  background: #dcfce7;
  color: #166534;
}

.typeTag.vehiclePurchase {
  background: #fef3c7;
  color: #92400e;
}

.typeTag.vehicleRepair {
  background: #fee2e2;
  color: #991b1b;
}

.typeTag.generalCost {
  background: #e0e7ff;
  color: #3730a3;
}

.typeTag.other {
  background: #f3f4f6;
  color: #374151;
}

/* Info Grid */
.infoGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.infoItem {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.infoItem label {
  font-weight: 600;
  color: #6b7280;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.infoItem span {
  color: #374151;
  font-size: 1rem;
}

.incomeAmount {
  color: #10b981 !important;
  font-weight: 600;
}

.expenseAmount {
  color: #ef4444 !important;
  font-weight: 600;
}

.category {
  text-transform: capitalize;
}

/* Description and Notes */
.description,
.notes {
  margin-bottom: 1.5rem;
}

.description:last-child,
.notes:last-child {
  margin-bottom: 0;
}

.description label,
.notes label {
  display: block;
  font-weight: 600;
  color: #6b7280;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  margin-bottom: 0.5rem;
}

.description p,
.notes p {
  margin: 0;
  color: #374151;
  line-height: 1.6;
}

/* Vehicle Info */
.vehicleInfo {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.vehicleDetails h3 {
  margin: 0 0 0.5rem 0;
  color: #374151;
  font-size: 1.125rem;
}

.vehicleDetails p {
  margin: 0;
  color: #6b7280;
  font-size: 0.875rem;
}

/* Receipts */
.receiptsList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.receiptItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.receiptInfo {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.receiptName {
  color: #374151;
  font-weight: 500;
}

.receiptError {
  color: #dc2626;
  font-size: 0.875rem;
}

.downloadButton {
  background: #3b82f6;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  border: none;
  cursor: pointer;
  font-weight: 500;
  font-size: 0.875rem;
  transition: background-color 0.2s;
}

.downloadButton:hover:not(:disabled) {
  background: #2563eb;
}

.downloadButton:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

/* Metadata */
.metadataGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.metadataItem {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.metadataItem label {
  font-weight: 600;
  color: #6b7280;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.metadataItem span {
  color: #374151;
  font-size: 0.875rem;
}

.transactionId {
  font-family: 'Courier New', monospace;
  background: #f3f4f6;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem !important;
}

.s3Path {
  font-family: 'Courier New', monospace;
  background: #f3f4f6;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem !important;
  word-break: break-all;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }

  .header {
    flex-direction: column;
    align-items: stretch;
  }

  .actions {
    justify-content: stretch;
  }

  .actions > * {
    flex: 1;
    text-align: center;
  }

  .cardContent {
    padding: 1rem;
  }

  .infoGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .metadataGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .receiptItem {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .downloadButton {
    align-self: stretch;
    text-align: center;
  }
}
