# Database Script Generator Prompt

## Description
This prompt helps generate MongoDB database scripts, migrations, and data operations for the FazeNAuto platform.

## Prompt Template

```
Create a MongoDB database script for [SCRIPT_PURPOSE] with the following requirements:

**Script Details:**
- Purpose: [DESCRIBE_PURPOSE]
- Type: [MIGRATION/SEED/CLEANUP/BACKUP/QUERY]
- Database: [DATABASE_NAME]
- Collections: [LIST_COLLECTIONS]

**Operations:**
- Actions: [CREATE/READ/UPDATE/DELETE/INDEX]
- Data transformation: [DESCRIBE_TRANSFORMATIONS]
- Validation: [DESCRIBE_VALIDATION_RULES]
- Rollback: [YES/NO - describe rollback strategy]

**Data Requirements:**
- Input format: [DESCRIBE_INPUT_DATA]
- Output format: [DESCRIBE_OUTPUT_DATA]
- Sample data: [PROVIDE_SAMPLE_DATA]
- Data volume: [ESTIMATED_RECORD_COUNT]

**Performance Considerations:**
- Indexing: [DESCRIBE_INDEX_REQUIREMENTS]
- Batch processing: [YES/NO]
- Memory usage: [CONSIDERATIONS]
- Execution time: [ESTIMATED_TIME]

**Safety Measures:**
- Backup required: [YES/NO]
- Dry run mode: [YES/NO]
- Transaction support: [YES/NO]
- Error handling: [DESCRIBE_ERROR_HANDLING]

**Follow these FazeNAuto conventions:**
1. Use Mongoose ODM with proper schemas
2. Include comprehensive error handling
3. Add logging for tracking progress
4. Use transactions for multi-collection operations
5. Include data validation before operations
6. Add progress indicators for long operations
7. Include rollback procedures
8. Use environment-specific configurations
9. Add proper indexing for performance
10. Include data integrity checks

**Example structure:**
```javascript
const mongoose = require('mongoose');
const { connectToDatabase } = require('../lib/dbConnect');

async function runScript() {
  try {
    await connectToDatabase();
    console.log('Starting script: [SCRIPT_NAME]');
    
    // Script implementation
    
    console.log('Script completed successfully');
  } catch (error) {
    console.error('Script failed:', error);
    process.exit(1);
  } finally {
    await mongoose.connection.close();
  }
}

if (require.main === module) {
  runScript();
}

module.exports = { runScript };
```

Generate the complete database script implementation.
```

## Example Usage

### Creating a Vehicle Data Migration Script
```
Create a MongoDB database script for migrating vehicle data to new schema with the following requirements:

**Script Details:**
- Purpose: Migrate vehicle records to include new features structure and image hashes
- Type: MIGRATION
- Database: fazenauto
- Collections: vehicles

**Operations:**
- Actions: UPDATE
- Data transformation: Convert features array to object with status, add imageHashes field
- Validation: Ensure all vehicles have valid features structure
- Rollback: YES - backup original data and provide rollback script

**Data Requirements:**
- Input format: Existing vehicle documents with features as array
- Output format: Updated vehicle documents with features as object and imageHashes
- Sample data: Vehicle with features: ["AC", "Bluetooth"] -> features: {comfort: {ac: "included", bluetooth: "included"}}
- Data volume: ~500 vehicle records

**Performance Considerations:**
- Indexing: Ensure indexes on make, model, year remain intact
- Batch processing: YES - process 50 records at a time
- Memory usage: Minimal - process in batches
- Execution time: ~2-3 minutes

**Safety Measures:**
- Backup required: YES
- Dry run mode: YES
- Transaction support: YES
- Error handling: Log failed records, continue processing others
```

## Expected Output Structure

### 1. Main Migration Script
```javascript
const mongoose = require('mongoose');
const { connectToDatabase } = require('../lib/dbConnect');
const Vehicle = require('../models/Vehicle');

// Configuration
const BATCH_SIZE = 50;
const DRY_RUN = process.env.DRY_RUN === 'true';

async function migrateVehicleData() {
  try {
    await connectToDatabase();
    console.log(`Starting vehicle data migration (DRY_RUN: ${DRY_RUN})`);
    
    // Get total count
    const totalCount = await Vehicle.countDocuments({});
    console.log(`Total vehicles to process: ${totalCount}`);
    
    let processed = 0;
    let errors = 0;
    
    // Process in batches
    for (let skip = 0; skip < totalCount; skip += BATCH_SIZE) {
      const vehicles = await Vehicle.find({})
        .skip(skip)
        .limit(BATCH_SIZE)
        .lean();
      
      console.log(`Processing batch ${Math.floor(skip / BATCH_SIZE) + 1}/${Math.ceil(totalCount / BATCH_SIZE)}`);
      
      for (const vehicle of vehicles) {
        try {
          const updatedData = transformVehicleData(vehicle);
          
          if (!DRY_RUN) {
            await Vehicle.updateOne(
              { _id: vehicle._id },
              { $set: updatedData }
            );
          }
          
          processed++;
        } catch (error) {
          console.error(`Error processing vehicle ${vehicle._id}:`, error);
          errors++;
        }
      }
    }
    
    console.log(`Migration completed. Processed: ${processed}, Errors: ${errors}`);
    
  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  }
}

function transformVehicleData(vehicle) {
  // Transform features array to object structure
  const features = {
    exterior: {},
    interior: {},
    mechanical: {},
    safety: {},
    entertainment: {}
  };
  
  // Map old features to new structure
  if (vehicle.features && Array.isArray(vehicle.features)) {
    vehicle.features.forEach(feature => {
      const mappedFeature = mapFeatureToCategory(feature);
      if (mappedFeature) {
        features[mappedFeature.category][mappedFeature.key] = 'included';
      }
    });
  }
  
  // Generate image hashes if missing
  const imageHashes = vehicle.imageHashes || [];
  
  return {
    features,
    imageHashes,
    updatedAt: new Date()
  };
}

function mapFeatureToCategory(feature) {
  const featureMap = {
    'AC': { category: 'interior', key: 'airConditioning' },
    'Bluetooth': { category: 'entertainment', key: 'bluetooth' },
    'GPS': { category: 'entertainment', key: 'navigation' },
    // Add more mappings as needed
  };
  
  return featureMap[feature] || null;
}

module.exports = { migrateVehicleData };

if (require.main === module) {
  migrateVehicleData()
    .then(() => process.exit(0))
    .catch(() => process.exit(1));
}
```

### 2. Rollback Script
```javascript
const mongoose = require('mongoose');
const { connectToDatabase } = require('../lib/dbConnect');

async function rollbackMigration() {
  try {
    await connectToDatabase();
    console.log('Starting rollback...');
    
    // Restore from backup collection
    const backupData = await mongoose.connection.db
      .collection('vehicles_backup_' + Date.now())
      .find({})
      .toArray();
    
    for (const vehicle of backupData) {
      await mongoose.connection.db
        .collection('vehicles')
        .replaceOne({ _id: vehicle._id }, vehicle);
    }
    
    console.log('Rollback completed');
  } catch (error) {
    console.error('Rollback failed:', error);
    throw error;
  }
}

module.exports = { rollbackMigration };
```

## Common Script Types

### 1. Data Seeding Script
```javascript
async function seedData() {
  const sampleData = [
    {
      make: 'Toyota',
      model: 'Camry',
      year: 2023,
      // ... other fields
    }
  ];
  
  for (const item of sampleData) {
    const exists = await Model.findOne({ uniqueField: item.uniqueField });
    if (!exists) {
      await new Model(item).save();
      console.log(`Created: ${item.name}`);
    }
  }
}
```

### 2. Data Cleanup Script
```javascript
async function cleanupData() {
  // Remove orphaned records
  const orphanedRecords = await Model.find({
    parentId: { $nin: await ParentModel.distinct('_id') }
  });
  
  console.log(`Found ${orphanedRecords.length} orphaned records`);
  
  if (!DRY_RUN) {
    await Model.deleteMany({
      _id: { $in: orphanedRecords.map(r => r._id) }
    });
  }
}
```

### 3. Index Creation Script
```javascript
async function createIndexes() {
  const indexes = [
    { collection: 'vehicles', index: { make: 1, model: 1, year: 1 } },
    { collection: 'vehicles', index: { status: 1, createdAt: -1 } },
    { collection: 'users', index: { email: 1 }, options: { unique: true } }
  ];
  
  for (const { collection, index, options = {} } of indexes) {
    try {
      await mongoose.connection.db
        .collection(collection)
        .createIndex(index, options);
      console.log(`Created index on ${collection}:`, index);
    } catch (error) {
      console.error(`Failed to create index on ${collection}:`, error);
    }
  }
}
```

### 4. Data Export Script
```javascript
async function exportData() {
  const fs = require('fs');
  const path = require('path');
  
  const data = await Model.find({}).lean();
  const exportPath = path.join(__dirname, `export_${Date.now()}.json`);
  
  fs.writeFileSync(exportPath, JSON.stringify(data, null, 2));
  console.log(`Data exported to: ${exportPath}`);
}
```

## Safety Patterns

### Transaction Wrapper
```javascript
async function withTransaction(operations) {
  const session = await mongoose.startSession();
  
  try {
    await session.withTransaction(async () => {
      for (const operation of operations) {
        await operation(session);
      }
    });
    
    console.log('Transaction completed successfully');
  } catch (error) {
    console.error('Transaction failed:', error);
    throw error;
  } finally {
    await session.endSession();
  }
}
```

### Backup Creation
```javascript
async function createBackup(collectionName) {
  const backupName = `${collectionName}_backup_${Date.now()}`;
  
  await mongoose.connection.db
    .collection(collectionName)
    .aggregate([
      { $out: backupName }
    ])
    .toArray();
  
  console.log(`Backup created: ${backupName}`);
  return backupName;
}
```

---

*Use this prompt to generate safe, efficient database scripts for the FazeNAuto platform! 🗄️*
