/* src/app/admin/AdminPage.module.css */
.adminDashboard {
  padding: 0;
  background-color: #ffffff;
  min-height: 100vh;
  color: var(--text-primary, #333);
  transition: background-color 0.3s ease;
}

/* Dark mode: Apply consistent gradient background */
[data-theme="dark"] .adminDashboard {
  background: var(--bg-primary);
}

.header {
  margin-bottom: 2rem;
  padding: 2rem 1rem 0;
}

/* Responsive padding adjustments for header */
@media (min-width: 769px) {
  .header {
    padding: 2rem 3rem 0;
  }
}

@media (min-width: 1200px) {
  .header {
    padding: 2rem 4rem 0;
  }
}

.tableWrapper {
  padding: 0 1rem 2rem;
}

/* Responsive padding adjustments for table wrapper */
@media (min-width: 769px) {
  .tableWrapper {
    padding: 0 3rem 2rem;
  }
}

@media (min-width: 1200px) {
  .tableWrapper {
    padding: 0 4rem 2rem;
  }
}

.panelWrapper {
  padding: 0 1rem 2rem;
}

/* Responsive padding adjustments for panel wrapper */
@media (min-width: 769px) {
  .panelWrapper {
    padding: 0 3rem 2rem;
  }
}

@media (min-width: 1200px) {
  .panelWrapper {
    padding: 0 4rem 2rem;
  }
}

.headerTop {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.titleSection {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header h1 {
  font-size: 2rem;
  margin: 0;
  color: var(--text-primary, #333);
}

.addVehicleBtn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #007bff;
  color: white;
  border: none;
  font-size: 1.5rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

.addVehicleBtn:hover {
  background: #0056b3;
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.4);
}

.headerActions {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.actionButtons {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.actionButton {
  background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.75rem;
  box-shadow: 0 2px 6px rgba(229, 62, 62, 0.2);
  white-space: nowrap;
}

.actionButton:hover {
  background: linear-gradient(135deg, #c53030 0%, #9b2c2c 100%);
  transform: translateY(-1px);
  box-shadow: 0 3px 8px rgba(229, 62, 62, 0.3);
}

.actionButton.active {
  background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
  box-shadow: 0 2px 6px rgba(74, 85, 104, 0.3);
}

.syndicationToggle {
  background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.875rem;
  box-shadow: 0 2px 8px rgba(229, 62, 62, 0.3);
}

.syndicationToggle:hover {
  background: linear-gradient(135deg, #c53030 0%, #9b2c2c 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(229, 62, 62, 0.4);
}

.syndicationToggle.active {
  background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
  box-shadow: 0 2px 8px rgba(74, 85, 104, 0.3);
}

.selectedCount {
  background: #e53e3e;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  white-space: nowrap;
}

.uploadBtn {
  background: #48bb78;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-size: 1rem;
}

.uploadBtn:hover {
  background: #38a169;
}

.stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.statCard {
  background: var(--bg-secondary, white);
  padding: 0.75rem;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  text-align: center;
  border: 1px solid var(--border-color, #e2e8f0);
}

.statCard.clickable {
  cursor: pointer;
}

.statCard h3 {
  font-size: 0.7rem;
  color: var(--text-secondary, #666);
  margin-bottom: 0.2rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.statCard p {
  font-size: 1.25rem;
  font-weight: bold;
  color: var(--text-primary, #333);
  margin: 0;
}

/* Dark mode styling for stat cards - consistent border */
[data-theme="dark"] .statCard {
  border: 1px solid rgba(255, 255, 255, 0.15);
  background: var(--bg-secondary);
}

/* Panel Styles */
.panel {
  background: var(--bg-secondary, white);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  overflow: hidden;
  border: 1px solid var(--border-color, #e2e8f0);
}

.panelHeader {
  background: var(--bg-tertiary, linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%));
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color, #e2e8f0);
}

.panelHeader h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.25rem;
  color: var(--text-primary, #2d3748);
  font-weight: 600;
}

.panelHeader p {
  margin: 0;
  color: var(--text-secondary, #718096);
  font-size: 0.875rem;
}

.panelContent {
  padding: 1.5rem;
}

/* Compliance Forms Panel */
.complianceActions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.complianceButton {
  background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
  color: white;
  border: none;
  padding: 1rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.875rem;
  text-align: center;
  box-shadow: 0 2px 6px rgba(66, 153, 225, 0.2);
}

.complianceButton:hover {
  background: linear-gradient(135deg, #3182ce 0%, #2c5282 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 10px rgba(66, 153, 225, 0.3);
}

.complianceInfo {
  background: var(--bg-tertiary, #f7fafc);
  padding: 1rem;
  border-radius: 6px;
  border-left: 4px solid var(--accent-primary, #4299e1);
}

.complianceInfo p {
  margin: 0;
  color: var(--text-secondary, #4a5568);
  font-size: 0.875rem;
}

/* Image Processing Panel */
.imageProcessingActions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.processingButton {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
  color: white;
  border: none;
  padding: 1rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.875rem;
  text-align: center;
  box-shadow: 0 2px 6px rgba(72, 187, 120, 0.2);
}

.processingButton:hover {
  background: linear-gradient(135deg, #38a169 0%, #2f855a 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 10px rgba(72, 187, 120, 0.3);
}

.processingOptions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.75rem;
  background: var(--bg-tertiary, #f7fafc);
  padding: 1rem;
  border-radius: 6px;
}

.processingOptions label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary, #4a5568);
  font-size: 0.875rem;
  cursor: pointer;
}

.processingOptions input[type="checkbox"] {
  margin: 0;
}

/* Ad Generator Panel */
.adGeneratorActions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.adButton {
  background: linear-gradient(135deg, #9f7aea 0%, #805ad5 100%);
  color: white;
  border: none;
  padding: 1rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.875rem;
  text-align: center;
  box-shadow: 0 2px 6px rgba(159, 122, 234, 0.2);
}

.adButton:hover {
  background: linear-gradient(135deg, #805ad5 0%, #6b46c1 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 10px rgba(159, 122, 234, 0.3);
}

.adButton:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* CSV Export Section */
.csvExportSection {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e2e8f0;
}

.csvExportSection h4 {
  margin: 0 0 1rem 0;
  color: #4a5568;
  font-size: 1rem;
  font-weight: 600;
}

.csvExportActions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 1rem;
}

.csvButton {
  background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
  color: white;
  border: none;
  padding: 1rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.875rem;
  text-align: center;
  box-shadow: 0 2px 6px rgba(237, 137, 54, 0.2);
}

.csvButton:hover {
  background: linear-gradient(135deg, #dd6b20 0%, #c05621 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 10px rgba(237, 137, 54, 0.3);
}

.csvButton:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.adOptions {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: center;
  background: var(--bg-tertiary, #f7fafc);
  padding: 1rem;
  border-radius: 6px;
}

.adOptions label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary, #4a5568);
  font-size: 0.875rem;
  cursor: pointer;
}

.adOptions input[type="checkbox"] {
  margin: 0;
}

.toneSelect {
  margin-left: 0.5rem;
  padding: 0.25rem 0.5rem;
  border: 1px solid var(--border-secondary, #cbd5e0);
  border-radius: 4px;
  background: var(--bg-primary, white);
  color: var(--text-secondary, #4a5568);
  font-size: 0.875rem;
}

/* Message Display */
.message {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  margin-bottom: 1rem;
  border-radius: 8px;
  font-weight: 500;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.message.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.message.info {
  background: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

.messageClose {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: inherit;
  opacity: 0.7;
  transition: opacity 0.2s;
  padding: 0;
  margin-left: 1rem;
}

.messageClose:hover {
  opacity: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
  .adminDashboard {
    padding: 1rem;
  }

  .headerTop {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .titleSection {
    justify-content: center;
    gap: 0.75rem;
  }

  .addVehicleBtn {
    width: 36px;
    height: 36px;
    font-size: 1.25rem;
  }

  .headerActions {
    flex-direction: column;
    gap: 1rem;
    width: 100%;
  }

  .actionButtons {
    flex-direction: column;
    width: 100%;
    gap: 0.75rem;
  }

  .stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }

  .actionButton {
    width: 100%;
    justify-content: center;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    min-height: 48px;
  }

  .syndicationToggle {
    width: 100%;
    justify-content: center;
    min-height: 48px;
  }

  .complianceActions,
  .imageProcessingActions,
  .adGeneratorActions,
  .csvExportActions {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .complianceButton,
  .processingButton,
  .adButton,
  .csvButton {
    min-height: 48px;
    font-size: 0.875rem;
  }

  .processingOptions {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .adOptions {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .statCard {
    padding: 0.5rem;
  }

  .statCard h3 {
    font-size: 0.65rem;
  }

  .statCard p {
    font-size: 1rem;
  }
}