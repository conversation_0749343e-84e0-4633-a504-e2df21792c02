.imageProcessingPanel {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 24px;
  margin: 20px 0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.header {
  margin-bottom: 24px;
  text-align: center;
}

.header h3 {
  color: #333;
  margin: 0 0 8px 0;
  font-size: 1.5rem;
}

.header p {
  color: #666;
  margin: 0;
  font-size: 0.95rem;
}

.section {
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.section h4 {
  color: #333;
  margin: 0 0 16px 0;
  font-size: 1.1rem;
  font-weight: 600;
}

/* Platform Selection */
.platformGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 12px;
}

.platformOption {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.platformOption:hover {
  border-color: #dc2626;
  background: rgba(220, 38, 38, 0.05);
}

.platformOption input[type="checkbox"] {
  margin: 0;
  transform: scale(1.2);
}

.platformInfo {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.platformName {
  font-weight: 600;
  color: #333;
  font-size: 0.95rem;
}

.platformDesc {
  font-size: 0.85rem;
  color: #666;
}

/* Processing Options */
.optionsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.option:hover {
  background: rgba(220, 38, 38, 0.05);
  border-color: #dc2626;
}

.option input[type="checkbox"] {
  transform: scale(1.1);
}

.option span {
  font-size: 0.95rem;
  color: #333;
}

/* File Upload */
.uploadArea {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 24px;
  border: 2px dashed #dc2626;
  border-radius: 12px;
  background: rgba(220, 38, 38, 0.02);
}

.fileInput {
  display: none;
}

.uploadButton {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 200px;
}

.uploadButton:hover:not(:disabled) {
  background: linear-gradient(135deg, #b91c1c, #991b1b);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
}

.uploadButton:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.uploadHint {
  font-size: 0.85rem;
  color: #666;
  text-align: center;
  margin: 8px 0 0 0;
}

/* Results */
.results {
  background: rgba(248, 250, 252, 0.8);
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e2e8f0;
}

.results h4 {
  margin: 0 0 16px 0;
  color: #333;
}

.summary {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 20px;
}

.success {
  color: #059669;
  font-weight: 600;
}

.warning {
  color: #d97706;
  font-weight: 600;
}

.error {
  color: #dc2626;
  font-weight: 600;
}

.processedImages {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.imageResult {
  background: white;
  border-radius: 6px;
  padding: 16px;
  border: 1px solid #e2e8f0;
}

.imageIndex {
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8px;
}

.platformResults {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 8px;
}

.platformResult {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
}

.platformResult .platformName {
  font-weight: 600;
  color: #666;
  min-width: 80px;
}

.imageLink {
  color: #dc2626;
  text-decoration: none;
  font-weight: 500;
}

.imageLink:hover {
  text-decoration: underline;
}

.errors {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e2e8f0;
}

.errors h5 {
  color: #dc2626;
  margin: 0 0 12px 0;
}

.errors .error {
  background: rgba(220, 38, 38, 0.1);
  padding: 8px 12px;
  border-radius: 4px;
  margin-bottom: 8px;
  font-size: 0.9rem;
}

/* Mobile Note */
.mobileNote {
  background: rgba(59, 130, 246, 0.1);
  border-radius: 8px;
  padding: 16px;
  border-left: 4px solid #3b82f6;
}

.mobileNote h4 {
  color: #1e40af;
  margin: 0 0 8px 0;
  font-size: 1rem;
}

.mobileNote p {
  color: #1e40af;
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.5;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .imageProcessingPanel {
    padding: 16px;
    margin: 16px 0;
  }

  .platformGrid {
    grid-template-columns: 1fr;
  }

  .optionsGrid {
    grid-template-columns: 1fr;
  }

  .uploadArea {
    padding: 20px 16px;
  }

  .uploadButton {
    width: 100%;
    min-width: auto;
  }

  .platformResults {
    grid-template-columns: 1fr;
  }

  .summary {
    flex-direction: column;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .header h3 {
    font-size: 1.3rem;
  }

  .section h4 {
    font-size: 1rem;
  }

  .platformOption,
  .option {
    padding: 10px;
  }

  .uploadButton {
    padding: 10px 16px;
    font-size: 0.95rem;
  }
}
