#!/usr/bin/env node

/**
 * Test Invoice Generation Script
 * Tests the invoice number generation for both test and prod modes
 * 
 * Usage:
 *   node scripts/test-invoice-generation.js
 */

import dotenv from 'dotenv';
import { generateInvoiceNumber, getCounterStatus } from '../apps/frontend/src/lib/utils/invoiceGenerator.js';

// Load environment variables from frontend directory
dotenv.config({ path: '../apps/frontend/.env.local' });

async function testInvoiceGeneration() {
  try {
    console.log('🧪 Testing invoice generation...\n');
    
    // Test Test Mode
    console.log('📝 Testing Test Mode:');
    for (let i = 1; i <= 3; i++) {
      const invoiceNumber = await generateInvoiceNumber('test');
      console.log(`   ${i}. Generated: ${invoiceNumber}`);
    }
    
    console.log('\n📝 Testing Production Mode:');
    for (let i = 1; i <= 3; i++) {
      const invoiceNumber = await generateInvoiceNumber('prod');
      console.log(`   ${i}. Generated: ${invoiceNumber}`);
    }
    
    // Check final counter status
    console.log('\n📊 Final Counter Status:');
    const testStatus = await getCounterStatus('test');
    const prodStatus = await getCounterStatus('prod');
    
    console.log(`   Test Mode: Current sequence ${testStatus.currentSequence}, Next: ${testStatus.nextInvoiceNumber}`);
    console.log(`   Prod Mode: Current sequence ${prodStatus.currentSequence}, Next: ${prodStatus.nextInvoiceNumber}`);
    
    console.log('\n✅ Invoice generation test completed successfully!');
    
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Error testing invoice generation:', error);
    process.exit(1);
  }
}

// Run the test
testInvoiceGeneration();
