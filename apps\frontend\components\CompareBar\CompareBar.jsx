'use client';

import { useCompare } from '../../contexts/CompareContext';
import styles from './CompareBar.module.css';

export default function CompareBar() {
  const { compareCount, canCompare, goToCompare, clearCompare } = useCompare();

  // Don't show the bar if there are no vehicles to compare
  if (compareCount === 0) {
    return null;
  }

  return (
    <div className={styles.compareBar}>
      <div className={styles.compareContent}>
        <div className={styles.compareInfo}>
          <span className={styles.compareIcon}>⚖️</span>
          <span className={styles.compareText}>
            Compare {compareCount} Vehicle{compareCount > 1 ? 's' : ''}
          </span>
          {compareCount === 1 && (
            <span className={styles.helpText}>Add one more to compare</span>
          )}
        </div>
        
        <div className={styles.compareActions}>
          <button
            className={styles.clearBtn}
            onClick={clearCompare}
            title="Clear all comparisons"
          >
            Clear
          </button>
          <button
            className={`${styles.compareBtn} ${canCompare ? styles.compareBtnEnabled : styles.compareBtnDisabled}`}
            onClick={goToCompare}
            disabled={!canCompare}
          >
            {canCompare ? 'Compare Now' : 'Add More Vehicles'}
          </button>
        </div>
      </div>
    </div>
  );
}
