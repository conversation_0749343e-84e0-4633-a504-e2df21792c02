import { NextResponse } from 'next/server';

/**
 * GET /api/rins - Fetch all active RIN records
 * Proxy to the backend API
 */
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const includeInactive = searchParams.get('includeInactive') || 'false';
    
    // In a real implementation, this would connect to your backend API
    // For now, we'll return mock data that matches your RIN schema
    const mockRins = [
      {
        _id: '507f1f77bcf86cd799439011',
        businessName: 'FazeNAuto Inc.',
        address: '1120 Meighen Way, Toronto, ON M1B 2X7',
        phone: '************',
        email: '<EMAIL>',
        rin: '**********',
        omvicNumber: '0021972',
        licenseNumber: 'DL-2024-001',
        contactPerson: {
          name: '<PERSON><PERSON><PERSON> <PERSON>',
          title: 'Owner',
          phone: '************',
          email: '<EMAIL>'
        },
        businessType: 'dealer',
        isActive: true,
        displayName: 'FazeNAuto Inc. (RIN: **********)'
      },
      {
        _id: '507f1f77bcf86cd799439012',
        businessName: 'Toronto Auto Sales',
        address: '456 Queen Street, Toronto, ON M5V 2A8',
        phone: '************',
        email: '<EMAIL>',
        rin: '**********',
        omvicNumber: '0012345',
        licenseNumber: 'DL-2024-002',
        contactPerson: {
          name: 'John Smith',
          title: 'Sales Manager',
          phone: '************',
          email: '<EMAIL>'
        },
        businessType: 'dealer',
        isActive: true,
        displayName: 'Toronto Auto Sales (RIN: **********)'
      }
    ];
    
    // Filter active RINs if requested
    const filteredRins = includeInactive === 'true' 
      ? mockRins 
      : mockRins.filter(rin => rin.isActive);
    
    return NextResponse.json({
      success: true,
      data: filteredRins,
      count: filteredRins.length
    });
    
  } catch (error) {
    console.error('❌ Error fetching RINs:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch RIN records',
      details: error.message
    }, { status: 500 });
  }
}

/**
 * POST /api/rins - Create a new RIN record
 */
export async function POST(request) {
  try {
    const body = await request.json();
    const {
      businessName,
      address,
      phone,
      email,
      rin,
      omvicNumber,
      licenseNumber,
      contactPerson,
      businessType,
      notes
    } = body;
    
    // Validate required fields
    if (!businessName || !address || !phone || !email || !rin) {
      return NextResponse.json({
        success: false,
        error: 'Missing required fields: businessName, address, phone, email, rin'
      }, { status: 400 });
    }
    
    // In a real implementation, this would save to your database
    // For now, return a mock success response
    const newRin = {
      _id: Date.now().toString(),
      businessName,
      address,
      phone,
      email,
      rin,
      omvicNumber,
      licenseNumber,
      contactPerson,
      businessType: businessType || 'dealer',
      notes,
      isActive: true,
      displayName: `${businessName} (RIN: ${rin})`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    return NextResponse.json({
      success: true,
      data: newRin,
      message: 'RIN record created successfully'
    }, { status: 201 });
    
  } catch (error) {
    console.error('❌ Error creating RIN:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to create RIN record',
      details: error.message
    }, { status: 500 });
  }
}
