.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  transition: background 0.3s ease;
}

/* Dark mode: Apply consistent gradient background */
[data-theme="dark"] .container {
  background: linear-gradient(to bottom right, #0d1117, #161b22);
}

.card {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 500px;
}

.header {
  text-align: center;
  margin-bottom: 2rem;
}

.header h1 {
  color: #333;
  margin-bottom: 0.5rem;
  font-size: 1.75rem;
}

.header p {
  color: #666;
  font-size: 0.875rem;
}

.warning {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  color: #856404;
  padding: 1rem;
  border-radius: 6px;
  margin-bottom: 1.5rem;
  font-size: 0.875rem;
}

.envInfo {
  background: #e7f3ff;
  border: 1px solid #b8daff;
  color: #004085;
  padding: 1rem;
  border-radius: 6px;
  margin-bottom: 1.5rem;
  font-size: 0.875rem;
}

.envInfo h4 {
  margin-bottom: 0.5rem;
}

.envInfo code {
  background: #f8f9fa;
  padding: 0.25rem 0.5rem;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
}

.formGroup label {
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #333;
  font-size: 0.875rem;
}

.input,
.select {
  padding: 0.75rem;
  border: 2px solid #e1e5e9;
  border-radius: 6px;
  font-size: 0.875rem;
  transition: border-color 0.2s;
}

.input:focus,
.select:focus {
  outline: none;
  border-color: #667eea;
}

.errorInput {
  border-color: #dc3545 !important;
  background-color: #fff5f5;
}

.successInput {
  border-color: #28a745 !important;
  background-color: #f8fff8;
}

.errorText {
  color: #dc3545;
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

.submitBtn {
  padding: 0.875rem;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.submitBtn:hover:not(:disabled) {
  background: #5a67d8;
}

.submitBtn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.result {
  margin-top: 1.5rem;
  padding: 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
}

.result pre {
  margin: 0;
  white-space: pre-wrap;
  font-family: inherit;
}

.result.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.result.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* Responsive */
@media (max-width: 768px) {
  .container {
    padding: 0.5rem;
  }
  
  .card {
    padding: 1.5rem;
  }
  
  .header h1 {
    font-size: 1.5rem;
  }
}
