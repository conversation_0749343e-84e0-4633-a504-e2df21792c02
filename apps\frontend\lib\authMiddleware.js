/**
 * Authentication Middleware for API Routes
 * 
 * This middleware provides authentication and authorization checks for API routes.
 * It can be used to protect routes that should only be accessible to authenticated users
 * with specific roles.
 */

import { NextResponse } from 'next/server';

/**
 * Middleware to check if a request is authenticated
 * @param {Request} request - The incoming request
 * @param {Array<string>} allowedRoles - Array of roles allowed to access this endpoint
 * @returns {Object} - Either an error response or null if authenticated
 */
export async function checkAuth(request, allowedRoles = ['admin', 'dealer', 'salesman']) {
  try {
    console.log('🔐 Auth check started for:', request.method, request.url);

    // 1. Check for authorization header (for API clients)
    const authHeader = request.headers.get('authorization');
    console.log('🔑 Auth header:', authHeader ? 'Present' : 'Not present');

    // 2. Check if request is from a browser (our frontend)
    const userAgent = request.headers.get('user-agent') || '';
    const isBrowser = userAgent.includes('Mozilla') || userAgent.includes('Chrome') || userAgent.includes('Safari');
    console.log('🌐 Is browser request:', isBrowser);
    
    // 3. For form data requests, extract user info from form
    let userEmail, userRole;

    // Check content type to determine how to extract user data
    const contentType = request.headers.get('content-type') || '';
    console.log('📄 Content type:', contentType);

    if (contentType.includes('multipart/form-data')) {
      // Clone the request to avoid consuming the body
      const clonedRequest = request.clone();
      try {
        const formData = await clonedRequest.formData();
        userEmail = formData.get('userEmail');
        userRole = formData.get('userRole');
        console.log('📝 Form data auth - Email:', userEmail, 'Role:', userRole);
      } catch (error) {
        console.error('Error parsing form data:', error);
      }
    } else if (contentType.includes('application/json')) {
      // Clone the request to avoid consuming the body
      const clonedRequest = request.clone();
      try {
        const jsonData = await clonedRequest.json();
        userEmail = jsonData.userEmail;
        userRole = jsonData.userRole;
        console.log('📝 JSON data auth - Email:', userEmail, 'Role:', userRole);
      } catch (error) {
        console.error('Error parsing JSON data:', error);
      }
    }
    
    // 4. Check if we have valid authentication
    const isAuthenticated =
      // API token auth
      (authHeader && authHeader.startsWith('Bearer ')) ||
      // Form data auth
      (userEmail && userRole && allowedRoles.includes(userRole));

    console.log('🔒 Authentication check:', {
      hasAuthHeader: !!authHeader,
      hasUserEmail: !!userEmail,
      hasUserRole: !!userRole,
      roleAllowed: userRole ? allowedRoles.includes(userRole) : false,
      isAuthenticated
    });

    if (!isAuthenticated) {
      // Allow browser requests to GET endpoints (read-only)
      if (isBrowser && request.method === 'GET') {
        console.log('✅ Allowing browser GET request without auth');
        return null; // Allow browser GET requests without auth
      }

      console.log('❌ Authentication failed');
      return NextResponse.json({
        success: false,
        error: 'Authentication required. Please log in.'
      }, { status: 401 });
    }
    
    // 5. Role-based authorization check
    if (userRole && !allowedRoles.includes(userRole)) {
      return NextResponse.json({
        success: false,
        error: `Insufficient permissions. Required roles: ${allowedRoles.join(', ')}`
      }, { status: 403 });
    }
    
    // Authentication passed
    return null;
    
  } catch (error) {
    console.error('Auth middleware error:', error);
    return NextResponse.json({
      success: false,
      error: 'Authentication error'
    }, { status: 500 });
  }
}

/**
 * Middleware to protect API routes that modify data
 * @param {Function} handler - The route handler function
 * @param {Array<string>} allowedRoles - Array of roles allowed to access this endpoint
 * @returns {Function} - Wrapped handler with authentication
 */
export function withAuth(handler, allowedRoles = ['admin', 'dealer', 'salesman']) {
  return async function(request, context) {
    // Skip auth for GET requests (read-only)
    if (request.method === 'GET') {
      return handler(request, context);
    }
    
    // Check authentication
    const authError = await checkAuth(request, allowedRoles);
    if (authError) {
      return authError;
    }
    
    // If authenticated, proceed to handler
    return handler(request, context);
  };
}

/**
 * Middleware to protect API routes that require admin access
 * @param {Function} handler - The route handler function
 * @returns {Function} - Wrapped handler with admin authentication
 */
export function withAdminAuth(handler) {
  return withAuth(handler, ['admin']);
}

/**
 * Middleware to protect API routes that require dealer access
 * @param {Function} handler - The route handler function
 * @returns {Function} - Wrapped handler with dealer authentication
 */
export function withDealerAuth(handler) {
  return withAuth(handler, ['admin', 'dealer', 'salesman']);
}
