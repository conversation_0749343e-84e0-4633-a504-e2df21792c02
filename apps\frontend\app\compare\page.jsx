'use client';

import { useEffect, useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useCompare } from '../../contexts/CompareContext';
import CompareCard from '../../components/CompareCard/CompareCard';
import styles from './ComparePage.module.css';

// Force dynamic rendering to avoid build-time issues
export const dynamic = 'force-dynamic';

function ComparePageContent() {
  const [vehicles, setVehicles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const router = useRouter();
  const searchParams = useSearchParams();
  const { selectedVehicles, clearCompare } = useCompare();

  useEffect(() => {
    const fetchVehicles = async () => {
      try {
        // Get vehicle IDs from URL params or context
        const urlIds = searchParams.get('ids');
        const vehicleIds = urlIds ? urlIds.split(',').filter(id => id.trim()) : selectedVehicles;

        if (vehicleIds.length === 0) {
          setLoading(false);
          return;
        }

        // Fetch vehicle details for comparison
        const vehiclePromises = vehicleIds.map(async (id) => {
          const response = await fetch(`/api/vehicles/${id}`);
          const data = await response.json();
          if (data.success) {
            return data.data;
          }
          throw new Error(`Failed to fetch vehicle ${id}`);
        });

        const vehicleData = await Promise.all(vehiclePromises);
        setVehicles(vehicleData.filter(vehicle => vehicle !== null));
      } catch (err) {
        console.error('Error fetching vehicles for comparison:', err);
        setError('Failed to load vehicles for comparison');
      } finally {
        setLoading(false);
      }
    };

    fetchVehicles();
  }, [searchParams, selectedVehicles]);

  const handleShareComparison = async () => {
    if (vehicles.length === 0) return;
    
    const shareUrl = `${window.location.origin}/compare?ids=${vehicles.map(v => v._id).join(',')}`;
    
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'Vehicle Comparison - FazeNAuto',
          text: `Compare ${vehicles.length} vehicles`,
          url: shareUrl,
        });
      } catch (err) {
        // Fallback to clipboard
        navigator.clipboard.writeText(shareUrl);
        alert('Comparison link copied to clipboard!');
      }
    } else {
      // Fallback to clipboard
      navigator.clipboard.writeText(shareUrl);
      alert('Comparison link copied to clipboard!');
    }
  };

  if (loading) {
    return (
      <div className={styles.container}>
        <div className={styles.loadingContainer}>
          <div className={styles.spinner}></div>
          <p className={styles.loadingText}>Loading vehicles for comparison...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.container}>
        <div className={styles.errorContainer}>
          <div className={styles.errorIcon}>⚠️</div>
          <h2 className={styles.errorTitle}>Error Loading Comparison</h2>
          <p className={styles.errorText}>{error}</p>
          <button
            className={styles.retryBtn}
            onClick={() => window.location.reload()}
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (vehicles.length === 0) {
    return (
      <div className={styles.container}>
        <div className={styles.emptyContainer}>
          <div className={styles.emptyIcon}>⚖️</div>
          <h2 className={styles.emptyTitle}>No Vehicles to Compare</h2>
          <p className={styles.emptyText}>
            You haven't selected any vehicles for comparison yet.
          </p>
          <p className={styles.emptySubtext}>
            Browse our inventory and add vehicles to compare their features side by side.
          </p>
          <button
            className={styles.browseBtn}
            onClick={() => router.push('/inventory/used-cars')}
          >
            Browse Inventory
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.headerContent}>
          <button
            className={styles.backBtn}
            onClick={() => router.back()}
          >
            ← Back
          </button>
          <div className={styles.titleSection}>
            <h1 className={styles.title}>Vehicle Comparison</h1>
            <p className={styles.subtitle}>
              Comparing {vehicles.length} vehicle{vehicles.length > 1 ? 's' : ''}
            </p>
          </div>
          <div className={styles.headerActions}>
            <button
              className={styles.shareBtn}
              onClick={handleShareComparison}
            >
              Share
            </button>
            <button
              className={styles.clearBtn}
              onClick={() => {
                clearCompare();
                router.push('/inventory/used-cars');
              }}
            >
              Clear All
            </button>
          </div>
        </div>
      </div>

      <div className={styles.compareGrid}>
        {vehicles.map((vehicle) => (
          <CompareCard
            key={vehicle._id}
            vehicle={vehicle}
          />
        ))}
      </div>
    </div>
  );
}

export default function ComparePage() {
  return (
    <Suspense fallback={
      <div className={styles.container}>
        <div className={styles.loadingContainer}>
          <div className={styles.spinner}></div>
          <p className={styles.loadingText}>Loading comparison...</p>
        </div>
      </div>
    }>
      <ComparePageContent />
    </Suspense>
  );
}
