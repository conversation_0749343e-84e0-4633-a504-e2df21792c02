#!/usr/bin/env node

/**
 * RIN Data Seeding Script
 * Seeds the database with sample RIN (Registration Identification Number) records
 * 
 * Usage:
 *   node scripts/seed-rins.js
 */

import dotenv from 'dotenv';
import { connectToDatabase } from '../apps/api/lib/dbConnect.js';
import Rin from '../apps/api/lib/models/Rin.js';

// Load environment variables
dotenv.config();

const sampleRins = [
  {
    businessName: 'FazeNAuto Inc.',
    address: '1120 Meighen Way, Toronto, ON M1B 2X7',
    phone: '************',
    email: '<EMAIL>',
    rin: '**********',
    omvicNumber: '0021972',
    licenseNumber: 'DL-2024-001',
    contactPerson: {
      name: '<PERSON><PERSON><PERSON>',
      title: 'Owner',
      phone: '************',
      email: '<EMAIL>'
    },
    businessType: 'dealer',
    notes: 'Primary dealership for FazeNAuto operations',
    isActive: true
  },
  {
    businessName: 'Toronto Auto Sales Ltd.',
    address: '456 Queen Street West, Toronto, ON M5V 2A8',
    phone: '************',
    email: '<EMAIL>',
    rin: '**********',
    omvicNumber: '0012345',
    licenseNumber: 'DL-2024-002',
    contactPerson: {
      name: 'John Smith',
      title: 'Sales Manager',
      phone: '************',
      email: '<EMAIL>'
    },
    businessType: 'dealer',
    notes: 'Partner dealership for overflow inventory',
    isActive: true
  },
  {
    businessName: 'Mississauga Motors',
    address: '789 Dundas Street, Mississauga, ON L5A 1W7',
    phone: '************',
    email: '<EMAIL>',
    rin: '**********',
    omvicNumber: '0067890',
    licenseNumber: 'DL-2024-003',
    contactPerson: {
      name: 'Sarah Johnson',
      title: 'General Manager',
      phone: '************',
      email: '<EMAIL>'
    },
    businessType: 'dealer',
    notes: 'Specialized in luxury vehicles',
    isActive: true
  },
  {
    businessName: 'Budget Auto Broker',
    address: '321 Industrial Road, Brampton, ON L6T 4K9',
    phone: '************',
    email: '<EMAIL>',
    rin: '**********',
    omvicNumber: '', // No OMVIC number for brokers
    licenseNumber: 'BR-2024-001',
    contactPerson: {
      name: 'Mike Chen',
      title: 'Licensed Broker',
      phone: '************',
      email: '<EMAIL>'
    },
    businessType: 'broker',
    notes: 'Vehicle brokerage services',
    isActive: true
  },
  {
    businessName: 'Classic Cars Ontario',
    address: '555 Heritage Lane, Hamilton, ON L8E 2X3',
    phone: '************',
    email: '<EMAIL>',
    rin: '**********',
    omvicNumber: '0098765',
    licenseNumber: 'DL-2024-004',
    contactPerson: {
      name: 'Robert Wilson',
      title: 'Owner/Operator',
      phone: '************',
      email: '<EMAIL>'
    },
    businessType: 'dealer',
    notes: 'Specializes in classic and vintage vehicles',
    isActive: false // Inactive for testing
  }
];

async function seedRins() {
  try {
    console.log('🔗 Connecting to database...');
    await connectToDatabase();
    
    console.log('🗑️ Clearing existing RIN records...');
    await Rin.deleteMany({});
    
    console.log('🌱 Seeding RIN records...');
    const createdRins = await Rin.insertMany(sampleRins);
    
    console.log(`✅ Successfully seeded ${createdRins.length} RIN records:`);
    createdRins.forEach(rin => {
      console.log(`   - ${rin.businessName} (RIN: ${rin.rin}) - ${rin.isActive ? 'Active' : 'Inactive'}`);
    });
    
    console.log('\n📊 Summary:');
    console.log(`   Total RINs: ${createdRins.length}`);
    console.log(`   Active: ${createdRins.filter(r => r.isActive).length}`);
    console.log(`   Inactive: ${createdRins.filter(r => !r.isActive).length}`);
    console.log(`   Dealers: ${createdRins.filter(r => r.businessType === 'dealer').length}`);
    console.log(`   Brokers: ${createdRins.filter(r => r.businessType === 'broker').length}`);
    
    console.log('\n🎉 RIN seeding completed successfully!');
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Error seeding RINs:', error);
    process.exit(1);
  }
}

// Run the seeding function
seedRins();
