'use client';

import { useEffect } from 'react';

export const useVisitorTracking = () => {
  useEffect(() => {
    // Only run on client side
    if (typeof window === 'undefined') return;

    // Track visitor on component mount
    const trackVisitor = async () => {
      try {
        await fetch('/api/visitors', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            page: window.location.pathname,
            timestamp: new Date().toISOString()
          })
        });
      } catch (error) {
        // Silently fail - visitor tracking shouldn't break the app
        console.debug('Visitor tracking failed:', error);
      }
    };

    // Track immediately
    trackVisitor();

    // Track on page visibility change (when user returns to tab)
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        trackVisitor();
      }
    };

    if (typeof document !== 'undefined') {
      document.addEventListener('visibilitychange', handleVisibilityChange);

      return () => {
        document.removeEventListener('visibilitychange', handleVisibilityChange);
      };
    }
  }, []);
};

export default useVisitorTracking;
