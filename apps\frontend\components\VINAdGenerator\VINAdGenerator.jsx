'use client';

import { useState, useRef } from 'react';
import styles from './VINAdGenerator.module.css';

export default function VINAdGenerator({ onAdGenerated }) {
  const [vin, setVin] = useState('');
  const [generating, setGenerating] = useState(false);
  const [results, setResults] = useState(null);
  const [options, setOptions] = useState({
    platforms: ['facebook', 'kijiji'],
    includeEmojis: true,
    bilingual: false,
    tone: 'professional',
    additionalFeatures: []
  });
  const [newFeature, setNewFeature] = useState('');

  const platforms = [
    { id: 'facebook', name: 'Facebook Marketplace', icon: '📘' },
    { id: 'kijiji', name: '<PERSON><PERSON><PERSON>', icon: '🏷️' },
    { id: 'craigslist', name: 'Craigslist', icon: '📋' },
    { id: 'autotrader', name: 'AutoTrader', icon: '🚗' }
  ];

  const tones = [
    { id: 'professional', name: 'Professional', description: 'Formal, business-like' },
    { id: 'casual', name: 'Casual', description: 'Friendly, conversational' },
    { id: 'enthusiastic', name: 'Enthusiastic', description: 'Exciting, energetic' }
  ];

  const handleVinChange = (e) => {
    const value = e.target.value.toUpperCase().replace(/[^A-HJ-NPR-Z0-9]/g, '');
    if (value.length <= 17) {
      setVin(value);
    }
  };

  const handlePlatformToggle = (platformId) => {
    setOptions(prev => ({
      ...prev,
      platforms: prev.platforms.includes(platformId)
        ? prev.platforms.filter(id => id !== platformId)
        : [...prev.platforms, platformId]
    }));
  };

  const handleAddFeature = () => {
    if (newFeature.trim() && !options.additionalFeatures.includes(newFeature.trim())) {
      setOptions(prev => ({
        ...prev,
        additionalFeatures: [...prev.additionalFeatures, newFeature.trim()]
      }));
      setNewFeature('');
    }
  };

  const handleRemoveFeature = (feature) => {
    setOptions(prev => ({
      ...prev,
      additionalFeatures: prev.additionalFeatures.filter(f => f !== feature)
    }));
  };

  const generateAds = async () => {
    if (vin.length !== 17) {
      alert('Please enter a valid 17-character VIN');
      return;
    }

    if (options.platforms.length === 0) {
      alert('Please select at least one platform');
      return;
    }

    setGenerating(true);
    setResults(null);

    try {
      const response = await fetch('/api/ad-generator', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          vin,
          ...options
        })
      });

      const data = await response.json();
      setResults(data);

      if (data.success && onAdGenerated) {
        onAdGenerated(data);
      }

    } catch (error) {
      console.error('Ad generation failed:', error);
      setResults({
        success: false,
        error: error.message
      });
    } finally {
      setGenerating(false);
    }
  };

  const copyToClipboard = async (text, type) => {
    try {
      await navigator.clipboard.writeText(text);
      // Show temporary success message
      const button = document.activeElement;
      const originalText = button.textContent;
      button.textContent = '✅ Copied!';
      setTimeout(() => {
        button.textContent = originalText;
      }, 2000);
    } catch (error) {
      console.error('Failed to copy:', error);
      alert('Failed to copy to clipboard');
    }
  };

  return (
    <div className={styles.vinAdGenerator}>
      <div className={styles.header}>
        <h3>🎯 VIN-to-Ad Generator</h3>
        <p>Generate market-ready listing copy from VIN</p>
      </div>

      {/* VIN Input */}
      <div className={styles.section}>
        <h4>Vehicle Identification Number</h4>
        <div className={styles.vinInput}>
          <input
            type="text"
            value={vin}
            onChange={handleVinChange}
            placeholder="Enter 17-character VIN"
            className="formInput"
            disabled={generating}
          />
          <div className={styles.vinStatus}>
            {vin.length > 0 && (
              <span className={vin.length === 17 ? styles.valid : styles.invalid}>
                {vin.length}/17 characters
              </span>
            )}
          </div>
        </div>
      </div>

      {/* Platform Selection */}
      <div className={styles.section}>
        <h4>Target Platforms</h4>
        <div className={styles.platformGrid}>
          {platforms.map(platform => (
            <label key={platform.id} className={styles.platformOption}>
              <input
                type="checkbox"
                checked={options.platforms.includes(platform.id)}
                onChange={() => handlePlatformToggle(platform.id)}
                disabled={generating}
              />
              <span className={styles.platformIcon}>{platform.icon}</span>
              <span className={styles.platformName}>{platform.name}</span>
            </label>
          ))}
        </div>
      </div>

      {/* Generation Options */}
      <div className={styles.section}>
        <h4>Generation Options</h4>
        <div className={styles.optionsGrid}>
          <div className={styles.optionGroup}>
            <label>Tone</label>
            <select
              value={options.tone}
              onChange={(e) => setOptions(prev => ({ ...prev, tone: e.target.value }))}
              disabled={generating}
              className="formInput"
            >
              {tones.map(tone => (
                <option key={tone.id} value={tone.id}>
                  {tone.name} - {tone.description}
                </option>
              ))}
            </select>
          </div>

          <div className={styles.checkboxGroup}>
            <label className={styles.checkbox}>
              <input
                type="checkbox"
                checked={options.includeEmojis}
                onChange={(e) => setOptions(prev => ({ ...prev, includeEmojis: e.target.checked }))}
                disabled={generating}
              />
              <span>Include Emojis 😊</span>
            </label>

            <label className={styles.checkbox}>
              <input
                type="checkbox"
                checked={options.bilingual}
                onChange={(e) => setOptions(prev => ({ ...prev, bilingual: e.target.checked }))}
                disabled={generating}
              />
              <span>Generate French Versions 🇫🇷</span>
            </label>
          </div>
        </div>
      </div>

      {/* Additional Features */}
      <div className={styles.section}>
        <h4>Additional Features</h4>
        <div className={styles.featuresInput}>
          <input
            type="text"
            value={newFeature}
            onChange={(e) => setNewFeature(e.target.value)}
            placeholder="Add custom feature (e.g., Leather seats, Sunroof)"
            className="formInput"
            disabled={generating}
            onKeyPress={(e) => e.key === 'Enter' && handleAddFeature()}
          />
          <button
            onClick={handleAddFeature}
            disabled={generating || !newFeature.trim()}
            className={`enhanced-button ${styles.addFeatureBtn}`}
          >
            Add
          </button>
        </div>
        
        {options.additionalFeatures.length > 0 && (
          <div className={styles.featuresList}>
            {options.additionalFeatures.map((feature, index) => (
              <span key={index} className={styles.featureTag}>
                {feature}
                <button
                  onClick={() => handleRemoveFeature(feature)}
                  disabled={generating}
                  className={`enhanced-button ${styles.removeFeature}`}
                >
                  ×
                </button>
              </span>
            ))}
          </div>
        )}
      </div>

      {/* Generate Button */}
      <div className={styles.generateSection}>
        <button
          onClick={generateAds}
          disabled={generating || vin.length !== 17 || options.platforms.length === 0}
          className={`enhanced-button ${styles.generateButton}`}
        >
          {generating ? '🔄 Generating Ads...' : '🎯 Generate Ads'}
        </button>
      </div>

      {/* Results */}
      {results && (
        <div className={styles.results}>
          <h4>Generated Ads</h4>
          {results.success ? (
            <div className={styles.successResults}>
              {/* Vehicle Info */}
              <div className={styles.vehicleInfo}>
                <h5>Vehicle Information</h5>
                <p>
                  {results.vehicleSpecs.year} {results.vehicleSpecs.make} {results.vehicleSpecs.model}
                  {results.vehicleSpecs.engine && ` • ${results.vehicleSpecs.engine}`}
                </p>
              </div>

              {/* Platform Ads */}
              <div className={styles.platformAds}>
                {Object.entries(results.platformAds).map(([platform, ad]) => (
                  <div key={platform} className={styles.platformAd}>
                    <div className={styles.platformHeader}>
                      <h5>
                        {platforms.find(p => p.id === platform)?.icon} {' '}
                        {platforms.find(p => p.id === platform)?.name || platform}
                      </h5>
                    </div>
                    
                    <div className={styles.adContent}>
                      <div className={styles.adSection}>
                        <label>Title:</label>
                        <div className={styles.copyableContent}>
                          <p>{ad.title}</p>
                          <button
                            onClick={() => copyToClipboard(ad.title, 'title')}
                            className={styles.copyBtn}
                          >
                            📋 Copy Title
                          </button>
                        </div>
                      </div>

                      <div className={styles.adSection}>
                        <label>Description:</label>
                        <div className={styles.copyableContent}>
                          <pre className={styles.description}>{ad.description}</pre>
                          <button
                            onClick={() => copyToClipboard(ad.description, 'description')}
                            className={styles.copyBtn}
                          >
                            📋 Copy Description
                          </button>
                        </div>
                      </div>

                      <div className={styles.adSection}>
                        <label>Full Ad:</label>
                        <button
                          onClick={() => copyToClipboard(`${ad.title}\n\n${ad.description}`, 'full')}
                          className={styles.copyBtn}
                        >
                          📋 Copy Full Ad
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className={styles.errorResults}>
              <span className={styles.error}>❌ {results.error}</span>
            </div>
          )}
        </div>
      )}

      {/* Mobile Tip */}
      <div className={styles.mobileTip}>
        <h4>📱 Mobile Tip</h4>
        <p>
          Use the copy buttons to quickly paste generated ads into Facebook, Kijiji, or other platforms. 
          The ads are optimized for each platform's requirements and character limits.
        </p>
      </div>
    </div>
  );
}
