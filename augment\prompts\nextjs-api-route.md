# Next.js API Route Generator Prompt

## Description
This prompt helps generate Next.js API routes following FazeNAuto's patterns and conventions.

## Prompt Template

```
Create a Next.js API route for [FEATURE_NAME] with the following requirements:

**Route Details:**
- Path: /api/[ROUTE_PATH]
- Methods: [GET/POST/PUT/DELETE]
- Purpose: [DESCRIBE_PURPOSE]

**Database Integration:**
- Model: [MODEL_NAME] (if applicable)
- Operations: [CREATE/READ/UPDATE/DELETE]
- Validation: [DESCRIBE_VALIDATION_RULES]

**Authentication:**
- Required: [YES/NO]
- Role-based: [ADMIN/DEALER/USER/ANY]

**Request/Response Format:**
- Input: [DESCRIBE_INPUT_FORMAT]
- Output: [DESCRIBE_OUTPUT_FORMAT]
- Error handling: [DESCRIBE_ERROR_CASES]

**Additional Requirements:**
- [LIST_ANY_SPECIAL_REQUIREMENTS]

**Follow these FazeNAuto conventions:**
1. Use MongoDB with Mongoose ODM
2. Import connectToDatabase from '@/lib/dbConnect'
3. Use NextResponse for responses
4. Include proper error handling with try/catch
5. Add input validation
6. Use consistent response format: { success: boolean, data?: any, error?: string }
7. Add appropriate HTTP status codes
8. Include logging for debugging
9. Follow RESTful principles
10. Add rate limiting if needed

**Example structure:**
```javascript
import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbConnect';
import ModelName from '@/domains/[domain]/models/ModelName';

export async function GET(request) {
  try {
    await connectToDatabase();
    
    // Implementation here
    
    return NextResponse.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json(
      { success: false, error: 'Error message' },
      { status: 500 }
    );
  }
}
```

Generate the complete API route implementation.
```

## Example Usage

### Creating a Vehicle Search API
```
Create a Next.js API route for vehicle search with the following requirements:

**Route Details:**
- Path: /api/vehicles/search
- Methods: GET
- Purpose: Search vehicles by make, model, year, price range, and features

**Database Integration:**
- Model: Vehicle
- Operations: READ with filtering and pagination
- Validation: Validate search parameters and ranges

**Authentication:**
- Required: NO
- Role-based: ANY (public endpoint)

**Request/Response Format:**
- Input: Query parameters (make, model, year, minPrice, maxPrice, page, limit)
- Output: Array of vehicles with pagination metadata
- Error handling: Invalid parameters, database errors

**Additional Requirements:**
- Support fuzzy search for make/model
- Include pagination (default 20 per page)
- Sort by price, year, or date added
- Filter by vehicle status (active only for public)
- Include total count for pagination
```

## Expected Output Structure

The generated API route should include:

1. **Imports**: All necessary imports
2. **Database Connection**: Connect to MongoDB
3. **Input Validation**: Validate request parameters
4. **Business Logic**: Core functionality implementation
5. **Error Handling**: Comprehensive error handling
6. **Response Format**: Consistent JSON responses
7. **Status Codes**: Appropriate HTTP status codes
8. **Logging**: Debug and error logging
9. **Comments**: Clear code documentation

## Common Patterns

### GET Route Pattern
```javascript
export async function GET(request) {
  try {
    await connectToDatabase();
    
    const { searchParams } = new URL(request.url);
    const param = searchParams.get('param');
    
    // Validation
    if (!param) {
      return NextResponse.json(
        { success: false, error: 'Parameter required' },
        { status: 400 }
      );
    }
    
    // Database operation
    const result = await Model.find({ field: param });
    
    return NextResponse.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('GET Error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
```

### POST Route Pattern
```javascript
export async function POST(request) {
  try {
    await connectToDatabase();
    
    const body = await request.json();
    
    // Validation
    const { field1, field2 } = body;
    if (!field1 || !field2) {
      return NextResponse.json(
        { success: false, error: 'Required fields missing' },
        { status: 400 }
      );
    }
    
    // Database operation
    const newRecord = new Model({ field1, field2 });
    await newRecord.save();
    
    return NextResponse.json({
      success: true,
      data: newRecord
    }, { status: 201 });
  } catch (error) {
    console.error('POST Error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create record' },
      { status: 500 }
    );
  }
}
```

## Validation Patterns

### Input Validation
```javascript
// String validation
if (!name || typeof name !== 'string' || name.trim().length === 0) {
  return NextResponse.json(
    { success: false, error: 'Valid name required' },
    { status: 400 }
  );
}

// Number validation
const price = parseFloat(priceStr);
if (isNaN(price) || price < 0) {
  return NextResponse.json(
    { success: false, error: 'Valid price required' },
    { status: 400 }
  );
}

// Email validation
const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
if (!emailRegex.test(email)) {
  return NextResponse.json(
    { success: false, error: 'Valid email required' },
    { status: 400 }
  );
}
```

## Error Handling Patterns

### Database Errors
```javascript
try {
  // Database operation
} catch (error) {
  if (error.code === 11000) {
    // Duplicate key error
    return NextResponse.json(
      { success: false, error: 'Record already exists' },
      { status: 409 }
    );
  }
  
  if (error.name === 'ValidationError') {
    return NextResponse.json(
      { success: false, error: error.message },
      { status: 400 }
    );
  }
  
  // Generic error
  console.error('Database error:', error);
  return NextResponse.json(
    { success: false, error: 'Database operation failed' },
    { status: 500 }
  );
}
```

---

*Use this prompt to generate consistent, well-structured API routes for the FazeNAuto platform! 🚀*
