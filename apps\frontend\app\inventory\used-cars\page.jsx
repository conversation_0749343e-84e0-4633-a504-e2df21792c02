
'use client';
import { useEffect, useState, useCallback, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import styles from './QualityUsedCars.module.css';
import { useLanguage } from '../../../contexts/LanguageContext';
import { useCompare } from '../../../contexts/CompareContext';

// Force dynamic rendering to avoid build-time issues
export const dynamic = 'force-dynamic';

function QualityUsedCarsContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { t } = useLanguage();
  const { addToCompare, removeFromCompare, isInCompare, canAddMore } = useCompare();
  const [vehicles, setVehicles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedMake, setSelectedMake] = useState('');

  useEffect(() => {
    // Get make filter from URL parameters
    const makeParam = searchParams.get('make');
    if (makeParam) {
      setSelectedMake(makeParam);
    }
  }, [searchParams]);

  const fetchVehicles = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Build API URL with make filter if selected
      let apiUrl = '/api/vehicles';
      if (selectedMake) {
        apiUrl += `?make=${encodeURIComponent(selectedMake)}`;
      }

      const res = await fetch(apiUrl);
      const json = await res.json();

      if (json.success && Array.isArray(json.data)) {
        setVehicles(json.data);
      } else {
        throw new Error('Invalid response format');
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [selectedMake]);

  useEffect(() => {
    fetchVehicles();
  }, [fetchVehicles]);

  const handleRetry = () => {
    fetchVehicles();
  };

  if (loading) {
    return (
      <div className={styles.container}>
        <div className={styles.loadingContainer}>
          <div className={styles.spinner}></div>
          <p className={styles.loadingText}>{t('common.loading_vehicles')}</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.container}>
        <div className={styles.errorContainer}>
          <div className={styles.errorIcon}>⚠️</div>
          <p className={styles.errorText}>Oops! Something went wrong</p>
          <p>Error: {error}</p>
          <button onClick={handleRetry} className={styles.retryBtn}>
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.contentWrapper}>
        {/* Keep only the filter info if a make is selected */}
        {selectedMake && (
          <div className={styles.filterInfo}>
            <span className={styles.filterLabel}>Filtered by:</span>
            <span className={styles.filterValue}>{selectedMake}</span>
            <button
              className={styles.clearFilter}
              onClick={() => {
                setSelectedMake('');
                router.push('/');
              }}
            >
              Clear Filter
            </button>
          </div>
        )}

        {/* Vehicle Grid */}
        {vehicles.length === 0 ? (
          <div className={styles.emptyContainer}>
            <div className={styles.emptyIcon}>🚗</div>
            <p className={styles.emptyText}>{t('inventory.no_vehicles')}</p>
            <p className={styles.emptySubtext}>{t('inventory.check_back')}</p>
          </div>
        ) : (
          <div className={styles.vehicleGrid}>
            {vehicles.map((vehicle) => {
              // Get the first image from images array or fall back to imageUrl
              const primaryImage = (vehicle.images && vehicle.images.length > 0)
                ? vehicle.images[0]
                : vehicle.imageUrl;

              const vehicleId = vehicle._id;
              const isInComparison = isInCompare(vehicleId);

              const handleCompareClick = () => {
                if (isInComparison) {
                  removeFromCompare(vehicleId);
                } else if (canAddMore) {
                  addToCompare(vehicleId);
                }
              };

              return (
                <div key={vehicle._id} className={styles.vehicleCard}>
                  <div className={styles.imageWrapper}>
                    <img
                      src={primaryImage}
                      alt={`${vehicle.year} ${vehicle.make} ${vehicle.model}`}
                      className={styles.vehicleImage}
                      onClick={() => router.push(`/vehicles/${vehicle._id}`)}
                    />

                    {/* Status Banners - Smaller size */}
                    {vehicle.status === 'sold' && (
                      <div className={`${styles.statusBanner} ${styles.sold}`}>
                        SOLD
                      </div>
                    )}
                    {vehicle.status === 'on_hold' && (
                      <div className={`${styles.statusBanner} ${styles.onHold}`}>
                        ON HOLD
                      </div>
                    )}

                    {/* Compare Button */}
                    <button
                      className={`${styles.compareBtn} ${isInComparison ? styles.compareBtnActive : ''} ${!canAddMore && !isInComparison ? styles.compareBtnDisabled : ''}`}
                      onClick={handleCompareClick}
                      disabled={!canAddMore && !isInComparison}
                      title={isInComparison ? 'Remove from comparison' : canAddMore ? 'Add to comparison' : 'Maximum 4 vehicles can be compared'}
                    >
                      {isInComparison ? '✓' : '+'}
                    </button>
                  </div>

                <div className={styles.vehicleInfo}>
                  <h2 className={styles.vehicleTitle}>
                    {vehicle.year} {vehicle.make} {vehicle.model}
                  </h2>

                  <div className={styles.vehicleDetails}>
                    <span className={styles.mileage}>
                      {vehicle.mileage ? `${vehicle.mileage.toLocaleString()} ${t('vehicle.km')}` : 'N/A'}
                    </span>
                  </div>

                  <div className={styles.vehicleSpecs}>
                    <div className={styles.specItem}>
                      <span className={styles.specLabel}>{t('vehicle.engine')}</span>
                      <span className={styles.specValue}>{vehicle.engine || 'N/A'}</span>
                    </div>
                    <div className={styles.specItem}>
                      <span className={styles.specLabel}>{t('vehicle.transmission')}</span>
                      <span className={styles.specValue}>{vehicle.transmission || 'N/A'}</span>
                    </div>
                    <div className={styles.specItem}>
                      <span className={styles.specLabel}>{t('vehicle.fuel_type')}</span>
                      <span className={styles.specValue}>{vehicle.fuelType || 'N/A'}</span>
                    </div>
                    <div className={styles.specItem}>
                      <span className={styles.specLabel}>{t('vehicle.doors')}</span>
                      <span className={styles.specValue}>{vehicle.doors || 'N/A'}</span>
                    </div>
                    <div className={styles.specItem}>
                      <span className={styles.specLabel}>{t('vehicle.driveline')}</span>
                      <span className={styles.specValue}>{vehicle.drivetrain || vehicle.driveline || 'N/A'}</span>
                    </div>
                  </div>



                  <div className={styles.vehiclePrice}>
                    ${vehicle.price?.toLocaleString() || 'N/A'} <span className={styles.taxText}>{t('vehicle.plus_tax')}</span>
                  </div>

                  <div className={styles.estimatedPayment}>
                    {t('vehicle.estimated_payment')} ${Math.round((vehicle.price || 0) / 60)}{t('vehicle.per_month')}
                  </div>

                  <div className={styles.buttonGroup}>
                    <button
                      className={`glossy-button view-btn ${styles.viewDetailsBtn}`}
                      onClick={() => router.push(`/vehicles/${vehicle._id}`)}
                    >
                      {t('vehicle.view_details')}
                    </button>
                    <button className={`glossy-button contact-btn ${styles.contactBtn}`}>
                      {t('vehicle.contact_us')}
                    </button>
                  </div>
                </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
}

export default function QualityUsedCars() {
  return (
    <Suspense fallback={
      <div className={styles.container}>
        <div className={styles.loadingContainer}>
          <div className={styles.spinner}></div>
          <p>Loading vehicles...</p>
        </div>
      </div>
    }>
      <QualityUsedCarsContent />
    </Suspense>
  );
}
