'use client';

import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import styles from './ThemeToggle.module.css';

export default function ThemeToggle({
  showLabel = true,
  size = 'medium',
  className = ''
}) {
  const { currentTheme, changeTheme } = useTheme();
  const { t } = useLanguage();

  const handleToggle = () => {
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
    changeTheme(newTheme);
  };

  return (
    <div className={`${styles.container} ${className}`}>
      {showLabel && (
        <label className={styles.label}>
          {currentTheme === 'dark' ? `🌙 ${t('theme.dark', 'Dark')}` : `☀️ ${t('theme.light', 'Light')}`}
        </label>
      )}
      <button
        onClick={handleToggle}
        className={`${styles.toggle} ${styles[size]} ${currentTheme === 'dark' ? styles.active : ''}`}
        aria-label={t('theme.toggle', `Switch to ${currentTheme === 'dark' ? 'light' : 'dark'} mode`)}
        title={t('theme.toggle', `Switch to ${currentTheme === 'dark' ? 'light' : 'dark'} mode`)}
      >
        <span className={styles.slider}>
          {currentTheme === 'dark' ? '🌙' : '☀️'}
        </span>
      </button>
    </div>
  );
}
