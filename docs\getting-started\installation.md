# Installation Guide

This guide will help you set up the FazeNAuto project on your local development machine. Even a kid can follow these steps! 🚀

## 📋 Prerequisites

Before you start, make sure you have these installed on your computer:

### Required Software
1. **Node.js** (version 18 or higher)
   - Download from: https://nodejs.org/
   - Check if installed: `node --version`

2. **npm** (comes with Node.js)
   - Check if installed: `npm --version`

3. **Git**
   - Download from: https://git-scm.com/
   - Check if installed: `git --version`

4. **Code Editor** (choose one):
   - VS Code (recommended): https://code.visualstudio.com/
   - Cursor: https://cursor.sh/

### Optional but Recommended
- **MongoDB Compass** (for database visualization): https://www.mongodb.com/products/compass

## 🚀 Step-by-Step Installation

### Step 1: Clone the Repository
```bash
# Clone the project
git clone <your-repository-url>
cd app

# Or if you already have the folder
cd path/to/your/app/folder
```

### Step 2: Install Dependencies
```bash
# Install all dependencies for both frontend and backend
npm install

# If you're in the root directory, install for both apps
cd apps/frontend
npm install

cd ../api
npm install

# Go back to root
cd ../..
```

### Step 3: Set Up Environment Variables
```bash
# Copy the example environment file
cp .env.example .env.local

# Edit the file with your actual values
# See docs/configuration/env-guide.md for detailed explanations
```

### Step 4: Set Up MongoDB
You have two options:

#### Option A: MongoDB Atlas (Cloud - Recommended for beginners)
1. Go to https://www.mongodb.com/atlas
2. Create a free account
3. Create a new cluster
4. Get your connection string
5. Add it to your `.env.local` file

#### Option B: Local MongoDB
1. Download MongoDB Community Server
2. Install and start MongoDB service
3. Use connection string: `mongodb://localhost:27017/fazenauto`

### Step 5: Set Up AWS S3 (for image uploads)
1. Create an AWS account
2. Create an S3 bucket
3. Create IAM user with S3 permissions
4. Add credentials to `.env.local`
5. See `docs/configuration/aws-setup.md` for detailed steps

### Step 6: Set Up OAuth (Google & Apple)
1. **Google OAuth**:
   - Go to Google Cloud Console
   - Create a new project
   - Enable Google+ API
   - Create OAuth 2.0 credentials
   - Add to `.env.local`

2. **Apple OAuth**:
   - Go to Apple Developer Console
   - Create App ID and Service ID
   - Add to `.env.local`

## 🏃‍♂️ Running the Application

### Development Mode
```bash
# Start the frontend (from root directory)
cd apps/frontend
npm run dev

# The app will be available at http://localhost:3000
```

### Production Mode
```bash
# Build the application
npm run build

# Start production server
npm start
```

## 🔧 IDE Setup

### VS Code Setup
1. Install recommended extensions:
   - ES7+ React/Redux/React-Native snippets
   - Prettier - Code formatter
   - ESLint
   - Auto Rename Tag
   - Bracket Pair Colorizer

2. Open the project:
   ```bash
   code .
   ```

### Cursor Setup
1. Download Cursor from https://cursor.sh/
2. Open the project folder
3. Cursor will automatically detect the project type

## ✅ Verify Installation

### Check if everything works:
1. **Frontend**: Visit http://localhost:3000
2. **Database**: Check if you can see the connection in logs
3. **Image Upload**: Try uploading a test image
4. **Authentication**: Try logging in with Google

### Common Commands
```bash
# Install new package
npm install package-name

# Run tests
npm test

# Check for errors
npm run lint

# Format code
npm run format
```

## 🆘 Troubleshooting

### Common Issues:

1. **Port 3000 already in use**:
   ```bash
   # Kill the process using port 3000
   npx kill-port 3000
   ```

2. **MongoDB connection error**:
   - Check your connection string in `.env.local`
   - Make sure MongoDB is running

3. **AWS S3 upload fails**:
   - Verify your AWS credentials
   - Check bucket permissions

4. **OAuth not working**:
   - Verify redirect URLs match your local setup
   - Check client IDs and secrets

### Getting Help
- Check `docs/development/troubleshooting.md`
- Look at the console for error messages
- Make sure all environment variables are set correctly

## 🎉 Next Steps

Once everything is installed:
1. Read `docs/architecture/overview.md` to understand the system
2. Check `docs/development/coding-standards.md` for coding guidelines
3. Explore `docs/api/routes.md` to understand the API
4. Start building! 🚀

---

*Need help? Check the troubleshooting guide or contact the development team.*
