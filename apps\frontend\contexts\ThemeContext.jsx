'use client';

import { createContext, useContext, useState, useEffect } from 'react';

const ThemeContext = createContext();

export function ThemeProvider({ children }) {
  const [currentTheme, setCurrentTheme] = useState('light');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    console.log('🎨 ThemeContext useEffect running...');
    // Only access localStorage on client side
    if (typeof window !== 'undefined') {
      console.log('🎨 Client side detected, loading theme...');
      const savedSettings = localStorage.getItem('userSettings');
      console.log('🎨 Saved settings:', savedSettings);
      if (savedSettings) {
        try {
          const parsedSettings = JSON.parse(savedSettings);
          const theme = parsedSettings.preferences?.theme || 'light';
          console.log('🎨 Loaded theme from localStorage:', theme);
          setCurrentTheme(theme);
          applyTheme(theme);
        } catch (error) {
          console.error('Error parsing saved settings:', error);
          // Fallback to system preference
          detectSystemTheme();
        }
      } else {
        // No saved settings, default to light mode
        console.log('🎨 No saved settings, defaulting to light mode');
        setCurrentTheme('light');
        applyTheme('light');
      }
    } else {
      console.log('🎨 Server side detected, skipping theme loading');
    }
    setIsLoading(false);
  }, []);

  const detectSystemTheme = () => {
    if (typeof window !== 'undefined') {
      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
      setCurrentTheme(systemTheme);
      applyTheme(systemTheme);
    }
  };

  const applyTheme = (theme) => {
    if (typeof document !== 'undefined') {
      console.log('🎨 Applying theme:', theme);
      document.documentElement.setAttribute('data-theme', theme);
      console.log('🎨 Theme applied, data-theme is now:', document.documentElement.getAttribute('data-theme'));
    }
  };

  const changeTheme = (newTheme) => {
    console.log('🎨 ThemeContext.changeTheme called:', { currentTheme, newTheme });
    setCurrentTheme(newTheme);
    applyTheme(newTheme);

    // Only access localStorage on client side
    if (typeof window !== 'undefined') {
      const savedSettings = localStorage.getItem('userSettings');
      let settings = {};

      if (savedSettings) {
        try {
          settings = JSON.parse(savedSettings);
        } catch (error) {
          console.error('Error parsing saved settings:', error);
        }
      }

      // Ensure preferences object exists
      if (!settings.preferences) {
        settings.preferences = {};
      }

      settings.preferences.theme = newTheme;
      localStorage.setItem('userSettings', JSON.stringify(settings));
    }
  };

  const value = {
    currentTheme,
    changeTheme,
    isLoading
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}
