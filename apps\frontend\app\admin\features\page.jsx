'use client';

import { useState, useEffect, Suspense } from 'react';
import { useRouter } from 'next/navigation';
import styles from './Features.module.css';

// Force dynamic rendering to avoid build-time issues
export const dynamic = 'force-dynamic';

export default function FeaturesPage() {
  const router = useRouter();
  const [features, setFeatures] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingFeature, setEditingFeature] = useState(null);
  const [formData, setFormData] = useState({
    category: 'exterior',
    value: ''
  });

  const categories = [
    { value: 'exterior', label: 'Exterior Features' },
    { value: 'interior', label: 'Interior Features' },
    { value: 'mechanical', label: 'Mechanical Features' },
    { value: 'safety', label: 'Safety Features' },
    { value: 'entertainment', label: 'Entertainment Features' }
  ];

  useEffect(() => {
    // Check authentication
    const user = localStorage.getItem('user');
    if (!user) {
      router.push('/login');
      return;
    }

    const userData = JSON.parse(user);
    if (!userData.role || !['admin', 'sales-manager'].includes(userData.role)) {
      router.push('/unauthorized');
      return;
    }

    fetchFeatures();
  }, [router]);

  const fetchFeatures = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/features?grouped=true');
      const data = await response.json();

      if (data.success) {
        setFeatures(data.data);
      } else {
        setError(data.error || 'Failed to fetch features');
      }
    } catch (err) {
      setError('Network error while fetching features');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      const url = editingFeature 
        ? `/api/features/${editingFeature._id}`
        : '/api/features';
      
      const method = editingFeature ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });

      const data = await response.json();

      if (data.success) {
        await fetchFeatures();
        setShowAddForm(false);
        setEditingFeature(null);
        setFormData({ category: 'exterior', value: '' });
        setError('');
      } else {
        setError(data.error || 'Failed to save feature');
      }
    } catch (err) {
      setError('Network error while saving feature');
    }
  };

  const handleEdit = (feature) => {
    setEditingFeature(feature);
    setFormData({
      category: feature.category || 'exterior',
      value: feature.value || ''
    });
    setShowAddForm(true);
  };

  const handleDelete = async (featureId) => {
    if (!confirm('Are you sure you want to delete this feature?')) {
      return;
    }

    try {
      const response = await fetch(`/api/features/${featureId}`, {
        method: 'DELETE'
      });

      const data = await response.json();

      if (data.success) {
        await fetchFeatures();
        setError('');
      } else {
        setError(data.error || 'Failed to delete feature');
      }
    } catch (err) {
      setError('Network error while deleting feature');
    }
  };

  const cancelForm = () => {
    setShowAddForm(false);
    setEditingFeature(null);
    setFormData({ category: 'exterior', value: '' });
    setError('');
  };

  if (loading) {
    return (
      <div className={styles.container}>
        <div className={styles.loading}>Loading features...</div>
      </div>
    );
  }

  return (
    <Suspense fallback={<div>Loading...</div>}>
      <div className={styles.container}>
      <div className={styles.header}>
        <h1>Vehicle Features Management</h1>
        <button 
          onClick={() => setShowAddForm(true)}
          className={styles.addButton}
        >
          Add New Feature
        </button>
      </div>

      {error && (
        <div className={styles.error}>
          {error}
        </div>
      )}

      {showAddForm && (
        <div className={styles.modal}>
          <div className={styles.modalContent}>
            <h2>{editingFeature ? 'Edit Feature' : 'Add New Feature'}</h2>
            <form onSubmit={handleSubmit} className={styles.form}>
              <div className={styles.formGroup}>
                <label htmlFor="category">Category:</label>
                <select
                  id="category"
                  value={formData.category}
                  onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                  required
                >
                  {categories.map(cat => (
                    <option key={cat.value} value={cat.value}>
                      {cat.label}
                    </option>
                  ))}
                </select>
              </div>

              <div className={styles.formGroup}>
                <label htmlFor="value">Feature Name:</label>
                <input
                  type="text"
                  id="value"
                  value={formData.value}
                  onChange={(e) => setFormData({ ...formData, value: e.target.value })}
                  placeholder="Enter feature name"
                  required
                />
              </div>

              <div className={styles.formActions}>
                <button type="submit" className={styles.saveButton}>
                  {editingFeature ? 'Update' : 'Add'} Feature
                </button>
                <button type="button" onClick={cancelForm} className={styles.cancelButton}>
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      <div className={styles.featuresGrid}>
        {categories.map(category => (
          <div key={category.value} className={styles.categorySection}>
            <h2 className={styles.categoryTitle}>{category.label}</h2>
            <div className={styles.featuresList}>
              {features[category.value] && features[category.value].length > 0 ? (
                features[category.value].map(feature => (
                  <div key={feature._id} className={styles.featureItem}>
                    <span className={styles.featureName}>{feature.value}</span>
                    <div className={styles.featureActions}>
                      <button 
                        onClick={() => handleEdit({ ...feature, category: category.value })}
                        className={styles.editButton}
                      >
                        Edit
                      </button>
                      <button 
                        onClick={() => handleDelete(feature._id)}
                        className={styles.deleteButton}
                      >
                        Delete
                      </button>
                    </div>
                  </div>
                ))
              ) : (
                <div className={styles.noFeatures}>
                  No features in this category
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
    </Suspense>
  );
}
