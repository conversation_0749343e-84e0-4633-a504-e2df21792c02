import { NextResponse } from 'next/server';
import { connectToDatabase } from '../../../lib/dbConnect';
import { withDealerAuth } from '../../../lib/authMiddleware';

// Import the actual syndication handlers
class FacebookMarketplaceHandler {
  constructor() {
    this.apiVersion = 'v18.0';
    this.baseUrl = `https://graph.facebook.com/${this.apiVersion}`;
  }

  getName() {
    return 'Facebook Marketplace';
  }

  isEnabled() {
    return !!(
      process.env.FACEBOOK_APP_ID &&
      process.env.FACEBOOK_APP_SECRET &&
      process.env.FACEBOOK_PAGE_ID
    );
  }

  async postVehicle(vehicleId, settings = {}) {
    try {
      await connectToDatabase();

      // Import Vehicle model dynamically to avoid build issues
      const { default: Vehicle } = await import('@/lib/models/Vehicle');

      const vehicle = await Vehicle.findById(vehicleId);
      if (!vehicle) {
        throw new Error('Vehicle not found');
      }

      // Get access token from settings or environment
      const accessToken = settings.accessToken || process.env.FACEBOOK_PAGE_TOKEN;
      const pageId = settings.pageId || process.env.FACEBOOK_PAGE_ID;

      if (!accessToken) {
        throw new Error('Facebook access token required. Please set up Facebook integration first.');
      }

      if (!pageId) {
        throw new Error('Facebook page ID required. Set FACEBOOK_PAGE_ID in environment variables.');
      }

      // Format vehicle data for Facebook
      const listingData = this.formatVehicleForFacebook(vehicle, settings);

      // Create the listing (simplified for now - images would be uploaded separately)
      const listing = await this.createListing(listingData, [], { accessToken, pageId });

      return {
        success: true,
        externalId: listing.id,
        externalUrl: `https://www.facebook.com/marketplace/item/${listing.id}`,
        platformData: {
          listingId: listing.id,
          pageId: pageId,
          createdAt: new Date().toISOString(),
          facebookResponse: listing
        }
      };

    } catch (error) {
      console.error('❌ Facebook posting error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  formatVehicleForFacebook(vehicle, settings = {}) {
    const title = `${vehicle.year} ${vehicle.make} ${vehicle.model}`;
    const description = this.generateDescription(vehicle);

    return {
      name: title,
      description: description,
      price: Math.round(vehicle.price * 100), // Facebook expects price in cents
      currency: settings.currency || 'CAD',
      condition: 'used',
      availability: 'in stock',

      // Vehicle-specific fields
      vehicle_year: vehicle.year,
      vehicle_make: vehicle.make,
      vehicle_model: vehicle.model,
      vehicle_mileage: vehicle.mileage,
      vehicle_transmission: vehicle.transmission,
      vehicle_exterior_color: vehicle.color,
      vehicle_vin: vehicle.vin,

      // Marketplace category
      marketplace_listing_type: 'vehicle',
    };
  }

  generateDescription(vehicle) {
    const parts = [
      `${vehicle.year} ${vehicle.make} ${vehicle.model}`,
      `Odometer: ${vehicle.mileage?.toLocaleString() || 'N/A'} km`,
      vehicle.engine && `Engine: ${vehicle.engine}`,
      vehicle.transmission && `Transmission: ${vehicle.transmission}`,
      vehicle.color && `Color: ${vehicle.color}`,
      vehicle.description && vehicle.description
    ].filter(Boolean);

    return parts.join('\n');
  }

  async createListing(listingData, imageIds, settings) {
    try {
      const payload = {
        ...listingData,
        access_token: settings.accessToken
      };

      const response = await fetch(`${this.baseUrl}/${settings.pageId}/marketplace_listings`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error?.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();

    } catch (error) {
      console.error('❌ Listing creation error:', error);
      throw error;
    }
  }
}

// Updated syndication service with actual Facebook integration
class SyndicationService {
  constructor() {
    this.facebookHandler = new FacebookMarketplaceHandler();
  }

  getAvailablePlatforms() {
    return [
      {
        id: 'facebook_marketplace',
        name: 'Facebook Marketplace',
        description: 'Post vehicles to Facebook Marketplace for maximum visibility',
        enabled: this.facebookHandler.isEnabled(),
        requiresAuth: true,
        features: ['auto_posting', 'image_upload', 'price_updates']
      },
      {
        id: 'autotrader',
        name: 'AutoTrader',
        description: 'Post vehicles to AutoTrader.ca (requires dealer account)',
        enabled: process.env.AUTOTRADER_ENABLED === 'true',
        requiresAuth: true,
        features: ['dealer_listings', 'professional_photos']
      },
      {
        id: 'custom_export',
        name: 'CSV Export',
        description: 'Export vehicle inventory to CSV format',
        enabled: true,
        requiresAuth: false,
        features: ['bulk_export', 'custom_fields']
      }
    ];
  }

  async syndicateVehicle(vehicleId, platforms, options = {}) {
    const results = [];

    for (const platform of platforms) {
      try {
        let result;

        switch (platform) {
          case 'facebook_marketplace':
            result = await this.facebookHandler.postVehicle(vehicleId, options.platformSettings?.facebook || {});
            break;

          case 'custom_export':
            // CSV export would be handled differently
            result = {
              success: true,
              message: 'CSV export available via /api/export/csv'
            };
            break;

          default:
            result = {
              success: false,
              error: `Platform ${platform} not yet implemented`
            };
        }

        results.push({
          platform,
          vehicleId,
          ...result
        });

      } catch (error) {
        results.push({
          platform,
          vehicleId,
          success: false,
          error: error.message
        });
      }
    }

    return results;
  }

  async getVehicleStatus(vehicleId) {
    return {
      success: true,
      vehicleId,
      platforms: {}
    };
  }

  async getPlatformStats(platform, days = 30) {
    return {
      success: true,
      platform,
      days,
      stats: []
    };
  }

  async removeVehicle(vehicleId, platforms = []) {
    return {
      success: false,
      error: 'Remove vehicle service temporarily unavailable during build optimization'
    };
  }
}

/**
 * GET /api/syndication - Get available platforms and stats
 */
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const vehicleId = searchParams.get('vehicleId');
    const platform = searchParams.get('platform');

    switch (action) {
      case 'platforms':
        const syndicationService = new SyndicationService();
        const platforms = syndicationService.getAvailablePlatforms();
        return NextResponse.json({
          success: true,
          platforms
        });

      case 'status':
        if (!vehicleId) {
          return NextResponse.json({
            success: false,
            error: 'Vehicle ID required for status check'
          }, { status: 400 });
        }

        const statusService = new SyndicationService();
        const status = await statusService.getVehicleStatus(vehicleId);
        return NextResponse.json(status);

      case 'stats':
        if (!platform) {
          return NextResponse.json({
            success: false,
            error: 'Platform required for stats'
          }, { status: 400 });
        }

        const days = parseInt(searchParams.get('days')) || 30;
        const statsService = new SyndicationService();
        const stats = await statsService.getPlatformStats(platform, days);
        return NextResponse.json(stats);

      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid action. Use: platforms, status, or stats'
        }, { status: 400 });
    }

  } catch (error) {
    console.error('❌ Syndication GET error:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

/**
 * POST /api/syndication - Syndicate vehicles to platforms
 */
export const POST = withDealerAuth(async function(request) {
  try {
    
    const body = await request.json();
    const { vehicleIds, platforms, settings = {} } = body;

    // Validation
    if (!vehicleIds || !Array.isArray(vehicleIds) || vehicleIds.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'Vehicle IDs array required'
      }, { status: 400 });
    }

    if (!platforms || !Array.isArray(platforms) || platforms.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'Platforms array required'
      }, { status: 400 });
    }

    // Validate platforms
    const validationService = new SyndicationService();
    const availablePlatforms = validationService.getAvailablePlatforms();
    const validPlatforms = availablePlatforms.map(p => p.id);
    const invalidPlatforms = platforms.filter(p => !validPlatforms.includes(p));

    if (invalidPlatforms.length > 0) {
      return NextResponse.json({
        success: false,
        error: `Invalid platforms: ${invalidPlatforms.join(', ')}`,
        availablePlatforms: validPlatforms
      }, { status: 400 });
    }

    // Process syndication for each vehicle
    const allResults = [];

    const postService = new SyndicationService();

    for (const vehicleId of vehicleIds) {
      try {
        const vehicleResults = await postService.syndicateVehicle(
          vehicleId,
          platforms,
          {
            userId: settings.userId,
            platformSettings: settings.platformSettings || {}
          }
        );

        // vehicleResults is an array of results for each platform
        allResults.push(...vehicleResults);

      } catch (error) {
        // Add error result for each platform for this vehicle
        platforms.forEach(platform => {
          allResults.push({
            success: false,
            vehicleId,
            platform,
            error: error.message
          });
        });
      }
    }

    // Calculate summary
    const successful = allResults.filter(r => r.success).length;
    const failed = allResults.length - successful;

    return NextResponse.json({
      success: true,
      summary: {
        total: allResults.length,
        successful,
        failed,
        platforms: platforms.length,
        vehicles: vehicleIds.length
      },
      results: allResults
    });

  } catch (error) {
    console.error('❌ Syndication POST error:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
});

/**
 * DELETE /api/syndication - Remove vehicles from platforms
 */
export async function DELETE(request) {
  try {
    
    const { searchParams } = new URL(request.url);
    const vehicleIds = searchParams.get('vehicleIds')?.split(',');
    const platforms = searchParams.get('platforms')?.split(',');

    if (!vehicleIds || vehicleIds.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'Vehicle IDs required'
      }, { status: 400 });
    }

    const results = [];
    
    const removeService = new SyndicationService();

    for (const vehicleId of vehicleIds) {
      try {
        const result = await removeService.removeVehicle(vehicleId, platforms);
        results.push(result);
      } catch (error) {
        results.push({
          success: false,
          vehicleId,
          error: error.message
        });
      }
    }

    const successful = results.filter(r => r.success).length;
    const failed = results.length - successful;

    return NextResponse.json({
      success: true,
      summary: {
        total: results.length,
        successful,
        failed
      },
      results
    });

  } catch (error) {
    console.error('❌ Syndication DELETE error:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}
