'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import MobilePostingUI from '@/components/MobilePostingUI';
import styles from './mobile-posting.module.css';

export default function MobilePostingPage() {
  const router = useRouter();
  const [user, setUser] = useState(null);
  const [isInstallable, setIsInstallable] = useState(false);
  const [deferredPrompt, setDeferredPrompt] = useState(null);

  useEffect(() => {
    checkAuth();
    setupPWA();
  }, []);

  const checkAuth = () => {
    const storedUser = localStorage.getItem('user');
    if (!storedUser) {
      router.push('/admin/auth/login');
      return;
    }
    setUser(JSON.parse(storedUser));
  };

  const setupPWA = () => {
    // Listen for the beforeinstallprompt event
    window.addEventListener('beforeinstallprompt', (e) => {
      e.preventDefault();
      setDeferredPrompt(e);
      setIsInstallable(true);
    });

    // Check if already installed
    if (window.matchMedia('(display-mode: standalone)').matches) {
      console.log('App is running in standalone mode');
    }
  };

  const handleInstallApp = async () => {
    if (!deferredPrompt) return;

    deferredPrompt.prompt();
    const { outcome } = await deferredPrompt.userChoice;
    
    if (outcome === 'accepted') {
      console.log('User accepted the install prompt');
    }
    
    setDeferredPrompt(null);
    setIsInstallable(false);
  };

  const handlePostingComplete = (result) => {
    console.log('Posting completed:', result);
    // You could track analytics, show success messages, etc.
  };

  if (!user) {
    return (
      <div className={styles.loading}>
        <div className={styles.spinner}></div>
        <p>Loading...</p>
      </div>
    );
  }

  return (
    <div className={styles.mobilePostingPage}>
      {/* Header */}
      <div className={styles.header}>
        <div className={styles.headerContent}>
          <button 
            onClick={() => router.back()}
            className={styles.backBtn}
          >
            ← Back
          </button>
          <h1>📱 Mobile Posting</h1>
          <div className={styles.headerActions}>
            {isInstallable && (
              <button 
                onClick={handleInstallApp}
                className={styles.installBtn}
              >
                📲 Install
              </button>
            )}
          </div>
        </div>
      </div>

      {/* PWA Install Banner */}
      {isInstallable && (
        <div className={styles.installBanner}>
          <div className={styles.bannerContent}>
            <div className={styles.bannerIcon}>📲</div>
            <div className={styles.bannerText}>
              <h4>Install FazeNAuto Mobile</h4>
              <p>Add to your home screen for quick access</p>
            </div>
            <button 
              onClick={handleInstallApp}
              className={styles.bannerInstallBtn}
            >
              Install
            </button>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className={styles.content}>
        <MobilePostingUI onPostingComplete={handlePostingComplete} />
      </div>

      {/* Footer */}
      <div className={styles.footer}>
        <div className={styles.footerContent}>
          <p>FazeNAuto Mobile Posting • {new Date().getFullYear()}</p>
          <div className={styles.footerLinks}>
            <button onClick={() => router.push('/admin')}>
              🏠 Dashboard
            </button>
            <button onClick={() => router.push('/admin/upload')}>
              ➕ Upload
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
