import { NextResponse } from 'next/server';
import { connectToDatabase } from '../../../lib/dbConnect';
import Ledger from '../../../lib/models/Ledger';
import Vehicle from '../../../lib/models/Vehicle';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import { v4 as uuidv4 } from 'uuid';
import { withDealerAuth } from '../../../lib/authMiddleware';
// Temporarily comment out error handler imports to debug
// import {
//   validateTransactionData,
//   validateFileUploads,
//   handleApiError,
//   logError,
//   ValidationError,
//   FileUploadError,
//   S3Error,
//   DatabaseError
// } from '../../../lib/utils/errorHandler';

const s3 = new S3Client({
  region: process.env.CUSTOM_AWS_REGION,
  credentials: {
    accessKeyId: process.env.CUSTOM_AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.CUSTOM_AWS_SECRET_ACCESS_KEY,
  },
});

// GET /api/ledger - List ledger entries with filters
export async function GET(request) {
  try {
    await connectToDatabase();

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page')) || 1;
    const limit = parseInt(searchParams.get('limit')) || 50;
    const skip = (page - 1) * limit;

    // Build filter object
    const filters = {};
    
    if (searchParams.get('type')) {
      filters.type = searchParams.get('type');
    }
    
    if (searchParams.get('vehicleId')) {
      filters.vehicleId = searchParams.get('vehicleId');
    }
    
    if (searchParams.get('addedBy')) {
      filters.addedBy = { $regex: searchParams.get('addedBy'), $options: 'i' };
    }
    
    if (searchParams.get('category')) {
      filters.category = searchParams.get('category');
    }
    
    if (searchParams.get('isIncome')) {
      filters.isIncome = searchParams.get('isIncome') === 'true';
    }

    // Date range filter
    if (searchParams.get('startDate') || searchParams.get('endDate')) {
      filters.date = {};
      if (searchParams.get('startDate')) {
        filters.date.$gte = new Date(searchParams.get('startDate'));
      }
      if (searchParams.get('endDate')) {
        filters.date.$lte = new Date(searchParams.get('endDate'));
      }
    }

    // Amount range filter
    if (searchParams.get('minAmount') || searchParams.get('maxAmount')) {
      filters.amount = {};
      if (searchParams.get('minAmount')) {
        filters.amount.$gte = parseFloat(searchParams.get('minAmount'));
      }
      if (searchParams.get('maxAmount')) {
        filters.amount.$lte = parseFloat(searchParams.get('maxAmount'));
      }
    }

    // Search in description
    if (searchParams.get('search')) {
      filters.description = { $regex: searchParams.get('search'), $options: 'i' };
    }

    // Get total count for pagination
    const total = await Ledger.countDocuments(filters);

    // Get ledger entries
    const ledgerEntries = await Ledger.find(filters)
      .populate('vehicleId', 'make model year vin displayName')
      .sort({ date: -1, createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();

    // Calculate summary statistics
    const summary = await Ledger.aggregate([
      { $match: filters },
      {
        $group: {
          _id: '$isIncome',
          total: { $sum: '$amount' },
          count: { $sum: 1 }
        }
      }
    ]);

    const summaryData = {
      totalIncome: 0,
      totalExpenses: 0,
      incomeCount: 0,
      expenseCount: 0,
      netAmount: 0
    };

    summary.forEach(item => {
      if (item._id === true) {
        summaryData.totalIncome = item.total;
        summaryData.incomeCount = item.count;
      } else {
        summaryData.totalExpenses = item.total;
        summaryData.expenseCount = item.count;
      }
    });

    summaryData.netAmount = summaryData.totalIncome - summaryData.totalExpenses;

    return NextResponse.json({
      success: true,
      data: {
        entries: ledgerEntries,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        },
        summary: summaryData
      }
    });

  } catch (error) {
    console.error('Error fetching ledger entries:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch ledger entries: ' + error.message },
      { status: 500 }
    );
  }
}

// POST /api/ledger - Create new ledger entry
export const POST = withDealerAuth(async function(request) {
  try {
    await connectToDatabase();

    const formData = await request.formData();
    
    // Extract form fields
    const type = formData.get('type');
    const description = formData.get('description');
    const amount = parseFloat(formData.get('amount'));
    const currency = formData.get('currency') || 'CAD';
    const date = formData.get('date') ? new Date(formData.get('date')) : new Date();
    const vehicleId = formData.get('vehicleId') || null;
    const addedBy = formData.get('addedBy');
    const notes = formData.get('notes') || '';

    // Basic validation
    if (!type || !description || !amount || !addedBy) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    if (parseFloat(amount) <= 0) {
      return NextResponse.json(
        { success: false, error: 'Amount must be greater than 0' },
        { status: 400 }
      );
    }

    // Validate vehicle exists if vehicleId provided
    if (vehicleId) {
      const vehicle = await Vehicle.findById(vehicleId);
      if (!vehicle) {
        return NextResponse.json(
          { success: false, error: 'Vehicle not found' },
          { status: 400 }
        );
      }
    }

    // Create ledger entry first to get ID for S3 path
    const transactionDate = new Date(date);
    const fiscalYear = transactionDate.getFullYear();
    const quarter = Math.ceil((transactionDate.getMonth() + 1) / 3);

    // Set category based on type
    const categoryMap = {
      'Vehicle Purchase': 'vehicle-purchase',
      'Repair Cost': 'vehicle-repair',
      'General Cost': 'general-cost',
      'Sale': 'sale',
      'Insurance': 'insurance',
      'Registration': 'registration',
      'Inspection': 'inspection',
      'Marketing': 'marketing',
      'Office Supplies': 'office',
      'Utilities': 'utilities',
      'Other Income': 'other',
      'Other Expense': 'other'
    };

    const category = categoryMap[type] || 'other';
    const isIncome = ['Sale', 'Other Income'].includes(type);

    const ledgerEntry = new Ledger({
      type,
      description,
      amount,
      currency,
      date,
      vehicleId,
      addedBy,
      notes,
      category,
      isIncome,
      fiscalYear,
      quarter
    });

    await ledgerEntry.save();

    // Handle file uploads if any
    const receiptUrls = [];
    const files = formData.getAll('receipts').filter(file => file.size > 0);

    if (files && files.length > 0) {
      // Basic file validation
      for (const file of files) {
        const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'];
        const maxSize = 10 * 1024 * 1024; // 10MB

        if (!allowedTypes.includes(file.type)) {
          return NextResponse.json(
            { success: false, error: `Unsupported file type: ${file.name}` },
            { status: 400 }
          );
        }

        if (file.size > maxSize) {
          return NextResponse.json(
            { success: false, error: `File too large: ${file.name}` },
            { status: 400 }
          );
        }
      }
      const s3Path = ledgerEntry.generateS3Path();
      
      for (const file of files) {
        if (file.size > 0) {
          const fileExtension = file.name.split('.').pop().toLowerCase();
          const allowedExtensions = ['pdf', 'jpg', 'jpeg', 'png'];
          
          if (!allowedExtensions.includes(fileExtension)) {
            continue; // Skip invalid files
          }

          const fileName = `${uuidv4()}.${fileExtension}`;
          const s3Key = `${s3Path}${fileName}`;
          
          const buffer = Buffer.from(await file.arrayBuffer());
          
          const uploadParams = {
            Bucket: 'fazenauto-ledger-documents',
            Key: s3Key,
            Body: buffer,
            ContentType: file.type,
            ServerSideEncryption: 'AES256',
            Metadata: {
              originalName: file.name,
              uploadedBy: addedBy,
              transactionId: ledgerEntry._id.toString(),
              uploadDate: new Date().toISOString()
            }
          };

          await s3.send(new PutObjectCommand(uploadParams));
          receiptUrls.push(`https://fazenauto-ledger-documents.s3.${process.env.CUSTOM_AWS_REGION}.amazonaws.com/${s3Key}`);
        }
      }

      // Create metadata.json file
      if (receiptUrls.length > 0) {
        const metadata = {
          vehicleId: vehicleId || null,
          date: date.toISOString().split('T')[0],
          type,
          description,
          amount,
          currency,
          receiptFiles: receiptUrls.map(url => url.split('/').pop()),
          addedBy,
          notes,
          createdAt: new Date().toISOString()
        };

        const metadataBuffer = Buffer.from(JSON.stringify(metadata, null, 2));
        const metadataParams = {
          Bucket: 'fazenauto-ledger-documents',
          Key: `${s3Path}metadata.json`,
          Body: metadataBuffer,
          ContentType: 'application/json',
          ServerSideEncryption: 'AES256'
        };

        await s3.send(new PutObjectCommand(metadataParams));

        // Update ledger entry with receipt URLs and S3 path
        ledgerEntry.receiptUrls = receiptUrls;
        ledgerEntry.s3Path = s3Path;
        await ledgerEntry.save();
      }
    }

    // Populate vehicle data for response
    await ledgerEntry.populate('vehicleId', 'make model year vin displayName');

    return NextResponse.json({
      success: true,
      data: ledgerEntry,
      message: 'Ledger entry created successfully'
    });

  } catch (error) {
    console.error('Error creating ledger entry:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create ledger entry: ' + error.message },
      { status: 500 }
    );
  }
});
