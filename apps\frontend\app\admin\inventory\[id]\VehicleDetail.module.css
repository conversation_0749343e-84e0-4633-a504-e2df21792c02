/* Vehicle Detail Container */
.detailContainer {
  padding: 0;
  background-color: #ffffff;
  min-height: 100vh;
  color: var(--text-primary, #333);
  transition: background-color 0.3s ease;
}

[data-theme="dark"] .detailContainer {
  background: var(--bg-primary);
}

/* Loading and Error States */
.loadingContainer,
.errorContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: var(--text-secondary);
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.backButton {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  background: var(--bg-secondary);
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 1rem;
}

.backButton:hover {
  background: var(--bg-tertiary);
}

/* Desktop Content - Default Layout */
.desktopContent {
  display: block;
}

.header {
  padding: 2rem 1rem 1rem;
}

.header h1 {
  font-size: 2rem;
  margin: 1rem 0 0 0;
  color: var(--text-primary);
}

.desktopLayout {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  padding: 0 1rem 2rem;
}

.imageSection {
  display: flex;
  justify-content: center;
}

.mainImage {
  width: 100%;
  max-width: 500px;
  height: 300px;
  object-fit: cover;
  border-radius: 12px;
  border: 1px solid var(--border-color);
}

.detailsSection {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.vehicleInfo h2 {
  font-size: 1.5rem;
  margin: 0 0 0.5rem 0;
  color: var(--text-primary);
}

.vin {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0 0 1rem 0;
}

.price {
  font-size: 1.5rem;
  font-weight: 700;
  color: #10b981;
  margin: 0;
}

.editButton {
  padding: 1rem 2rem;
  border-radius: 12px;
  border: none;
  background: #10b981;
  color: white;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.editButton:hover {
  background: #059669;
  transform: translateY(-1px);
}

/* Mobile Content - Hidden by default */
.mobileContent {
  display: none;
}

/* MOBILE ONLY STYLES */
@media screen and (max-width: 768px) {
  .detailContainer {
    padding: 0;
  }

  /* Hide desktop content on mobile */
  .desktopContent {
    display: none;
  }

  /* Show mobile content */
  .mobileContent {
    display: block;
  }

  /* Hero Image Section */
  .heroImageSection {
    position: relative;
    width: 100%;
    height: 250px;
    overflow: hidden;
  }

  .imageContainer {
    position: relative;
    width: 100%;
    height: 100%;
  }

  .heroImage {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
  }

  .imageNav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 44px;
    height: 44px;
    border-radius: 50%;
    border: none;
    background: rgba(0, 0, 0, 0.5);
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .prevButton {
    left: 1rem;
  }

  .nextButton {
    right: 1rem;
  }

  .imageNav:hover {
    background: rgba(0, 0, 0, 0.7);
    transform: translateY(-50%) scale(1.1);
  }

  .imageIndicators {
    position: absolute;
    bottom: 1rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 0.5rem;
  }

  .indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .indicator.active {
    background: white;
    transform: scale(1.2);
  }

  /* Vehicle Details Section */
  .vehicleDetailsSection {
    padding: 1.5rem;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
  }

  .vehicleHeader {
    text-align: center;
    margin-bottom: 1.5rem;
  }

  .vehicleTitle {
    font-size: clamp(1.25rem, 4vw, 1.5rem);
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 0.5rem 0;
    line-height: 1.3;
  }

  .vehicleVin {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: 1rem;
  }

  .vehiclePrice {
    font-size: clamp(1.5rem, 5vw, 2rem);
    font-weight: 700;
    color: #10b981;
  }

  .mobileEditButton {
    width: 100%;
    padding: 1rem;
    border-radius: 12px;
    border: none;
    background: #10b981;
    color: white;
    font-size: clamp(1rem, 2.5vw, 1.125rem);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 48px;
  }

  .mobileEditButton:hover {
    background: #059669;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
  }

  .mobileEditButton:active {
    transform: scale(0.97);
  }

  /* Action Grid Section */
  .actionGridSection {
    padding: 1.5rem;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
  }

  .actionGrid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .actionGridTile {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 1.5rem 1rem;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 80px;
    gap: 0.5rem;
  }

  .actionGridTile:hover {
    background: var(--bg-tertiary);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .actionGridTile:active {
    transform: scale(0.97);
  }

  .actionIcon {
    font-size: 1.5rem;
  }

  .actionLabel {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
    text-align: center;
    line-height: 1.2;
  }

  /* BOS Buttons Section */
  .bosButtonsSection {
    padding: 1.5rem;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    gap: 1rem;
  }

  .bosButton {
    flex: 1;
    padding: 1rem;
    border-radius: 12px;
    border: 1px solid var(--border-color);
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: clamp(0.875rem, 2.5vw, 1rem);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 48px;
  }

  .bosButton:hover {
    background: var(--bg-tertiary);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  .bosButton:active {
    transform: scale(0.97);
  }

  /* Accordion Section */
  .accordionSection {
    padding: 1.5rem;
    background: var(--bg-secondary);
  }

  .accordionItem {
    border: 1px solid var(--border-color);
    border-radius: 12px;
    overflow: hidden;
  }

  .accordionHeader {
    width: 100%;
    padding: 1rem 1.25rem;
    background: var(--bg-primary);
    border: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    transition: all 0.2s ease;
  }

  .accordionHeader:hover {
    background: var(--bg-tertiary);
  }

  .accordionIcon {
    transition: transform 0.2s ease;
  }

  .accordionContent {
    padding: 1rem 1.25rem;
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
  }

  .detailRow {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
  }

  .detailRow:last-child {
    border-bottom: none;
  }

  .detailLabel {
    font-size: 0.875rem;
    color: var(--text-secondary);
  }

  .detailValue {
    font-size: 0.875rem;
    color: var(--text-primary);
    font-weight: 500;
  }

  .statusValue {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    background: #d4edda;
    color: #155724;
    font-size: 0.75rem;
    text-transform: uppercase;
  }
}

/* Responsive adjustments for larger screens */
@media (min-width: 769px) {
  .header {
    padding: 2rem 3rem 1rem;
  }

  .desktopLayout {
    padding: 0 3rem 2rem;
  }
}

@media (min-width: 1200px) {
  .header {
    padding: 2rem 4rem 1rem;
  }

  .desktopLayout {
    padding: 0 4rem 2rem;
  }
}
