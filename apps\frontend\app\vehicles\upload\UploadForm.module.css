/* Vehicle Upload Form Styling */

.container {
  min-height: 100vh;
  background: #ffffff;
  padding: 0;
  transition: background 0.3s ease;
}

/* Dark mode: Apply consistent gradient background */
[data-theme="dark"] .container {
  background: var(--bg-primary);
}

.formWrapper {
  background: transparent;
  padding: 2rem 1rem;
  width: 100%;
  max-width: none;
  margin: 0;
}

/* Responsive padding adjustments for larger screens */
@media (min-width: 769px) {
  .formWrapper {
    padding: 2rem 3rem;
  }
}

@media (min-width: 1200px) {
  .formWrapper {
    padding: 2rem 4rem;
  }
}

.title {
  font-size: 2rem;
  font-weight: 700;
  color: #2d3748;
  text-align: center;
  margin-bottom: 2rem;
  border-bottom: 2px solid #e2e8f0;
  padding-bottom: 1rem;
  transition: color 0.3s ease, border-color 0.3s ease;
}

[data-theme="dark"] .title {
  color: #ffffff;
  border-bottom-color: rgba(255, 255, 255, 0.2);
}

/* Dark mode styling for form elements */
[data-theme="dark"] .label {
  color: #ffffff;
}

[data-theme="dark"] .fileInput {
  background: linear-gradient(135deg, #0d1117 0%, #1a1f2b 100%);
  border: 1.5px dashed #4a4a4a;
  color: #e2e8f0;
}

[data-theme="dark"] .fileInput:hover {
  border-color: #5a5a5a;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.inputGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.label {
  font-weight: 600;
  color: #4a5568;
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
}



.fileInput {
  padding: 0.75rem 1rem;
  border: 1.5px dashed var(--border-secondary, #cbd5e0);
  border-radius: 8px;
  background: var(--bg-tertiary, #f7fafc);
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  color: var(--text-primary, #2d3748);
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
}

.fileInput:hover {
  border-color: var(--accent-primary, #4299e1);
  background: var(--bg-secondary, #ebf8ff);
}

.fileInput:focus {
  outline: none;
  border-color: var(--accent-primary, #4299e1);
  box-shadow: 0 0 0 2px rgba(66, 153, 225, 0.1);
}

.helpText {
  color: var(--text-secondary, #718096);
  font-size: 0.875rem;
  margin-top: 0.25rem;
  font-style: italic;
}

.submitButton {
  background: #4299e1;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 1rem;
}

.submitButton:hover {
  background: #3182ce;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(66, 153, 225, 0.3);
}

.submitButton:active {
  transform: translateY(0);
}

.message {
  padding: 1rem;
  border-radius: 8px;
  margin-top: 1rem;
  font-weight: 500;
  text-align: center;
}

.messageSuccess {
  background: #c6f6d5;
  color: #22543d;
  border: 1px solid #9ae6b4;
}

.messageError {
  background: #fed7d7;
  color: #742a2a;
  border: 1px solid #fc8181;
}

.twoColumnGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.fullWidth {
  grid-column: 1 / -1;
}

/* Loading state for submit button */
.submitButton:disabled {
  background: var(--text-tertiary, #a0aec0);
  cursor: not-allowed;
  transform: none;
}

.submitButton:disabled:hover {
  background: var(--text-tertiary, #a0aec0);
  transform: none;
  box-shadow: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }
  
  .formWrapper {
    padding: 2rem 1.5rem;
  }
  
  .title {
    font-size: 1.75rem;
  }
  
  .twoColumnGrid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .formWrapper {
    padding: 1.5rem 1rem;
  }

  .title {
    font-size: 1.5rem;
  }

  .fileInput {
    padding: 0.75rem;
  }

  .submitButton {
    padding: 0.875rem 1.5rem;
    font-size: 1rem;
  }
}

/* VIN Decoder Styles */
.vinInputGroup {
  display: flex;
  gap: 0.75rem;
  align-items: flex-end;
}



.scanButton {
  background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
  color: white;
  border: none;
  padding: 1rem 1.25rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
  white-space: nowrap;
  min-width: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  line-height: 1;
  /* 3D Glossy Effect */
  position: relative;
  overflow: hidden;
  box-shadow:
    inset 0 1px rgba(255, 255, 255, 0.15),
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -2px rgba(0, 0, 0, 0.1);
}

.scanButton::before {
  content: "";
  position: absolute;
  inset: 0;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.2), transparent);
  pointer-events: none;
  z-index: 1;
}

.scanButton:active {
  box-shadow:
    inset 0 2px 4px rgba(0, 0, 0, 0.3),
    0 0 #0000;
  transform: scale(0.98);
}

.scanButton span {
  display: flex;
  align-items: center;
  position: relative;
  z-index: 2;
}

.scanButton:hover {
  background: linear-gradient(135deg, #c53030 0%, #9b2c2c 100%);
  transform: translateY(-1px);
  box-shadow:
    inset 0 1px rgba(255, 255, 255, 0.15),
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -2px rgba(0, 0, 0, 0.1);
}

/* Camera icon flash animation */
.scanButton span:first-child {
  transition: filter 0.2s ease;
  display: flex;
  align-items: center;
  transform: translateY(-1px);
  font-size: 1.1em;
  line-height: 1;
}

.scanButton:hover span:first-child {
  animation: flashIcon 2s ease-in-out;
}

@keyframes flashIcon {
  0%   { filter: brightness(1); }
  50%  { filter: brightness(2); }
  100% { filter: brightness(1); }
}

.decodeButton {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  padding: 1rem 1.5rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  min-width: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.decodeButton:hover:not(:disabled) {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.decodeButton:disabled {
  background: var(--text-tertiary, #9ca3af);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.vinHelper {
  font-size: 0.85rem;
  color: #6b7280;
  font-weight: normal;
}

.sectionHeader {
  margin: 2rem 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e5e7eb;
}

.sectionHeader h3 {
  margin: 0;
  color: #1f2937;
  font-size: 1.25rem;
  font-weight: 600;
}

.autoFilledNote {
  color: #059669;
  font-weight: 500;
  margin-top: 0.25rem;
  display: block;
}

/* Mobile responsiveness for VIN decoder */
@media (max-width: 768px) {
  .vinInputGroup {
    flex-direction: column;
    gap: 0.5rem;
  }

  .scanButton,
  .decodeButton {
    width: 100%;
    min-width: auto;
  }
}
