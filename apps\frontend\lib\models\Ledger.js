import mongoose from 'mongoose';

const ledgerSchema = new mongoose.Schema({
  type: {
    type: String,
    required: true,
    enum: [
      'Vehicle Purchase',
      'Repair Cost', 
      'General Cost',
      'Sale',
      'Insurance',
      'Registration',
      'Inspection',
      'Marketing',
      'Office Supplies',
      'Utilities',
      'Other Income',
      'Other Expense'
    ],
    trim: true
  },
  description: {
    type: String,
    required: true,
    trim: true,
    maxlength: 500
  },
  amount: {
    type: Number,
    required: true,
    min: 0.01
  },
  currency: {
    type: String,
    default: 'CAD',
    enum: ['CAD', 'USD', 'EUR'],
    trim: true
  },
  date: {
    type: Date,
    required: true,
    default: Date.now
  },
  vehicleId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Vehicle',
    required: false
  },
  addedBy: {
    type: String,
    required: true,
    trim: true
  },
  receiptUrls: [{
    type: String,
    trim: true
  }],
  s3Path: {
    type: String,
    trim: true
  },
  notes: {
    type: String,
    trim: true,
    maxlength: 1000
  },
  // Additional metadata for better tracking
  category: {
    type: String,
    enum: ['vehicle-purchase', 'vehicle-repair', 'general-cost', 'sale', 'insurance', 'registration', 'inspection', 'marketing', 'office', 'utilities', 'other'],
    default: 'other'
  },
  isIncome: {
    type: Boolean,
    default: false
  },
  fiscalYear: {
    type: Number,
    default: function() {
      return new Date().getFullYear();
    }
  },
  quarter: {
    type: Number,
    min: 1,
    max: 4,
    default: function() {
      return Math.ceil((new Date().getMonth() + 1) / 3);
    }
  },
  receiptCount: {
    type: Number,
    default: 0,
    min: 0
  }
}, {
  timestamps: true
});

// Indexes for better query performance
ledgerSchema.index({ type: 1, date: -1 });
ledgerSchema.index({ vehicleId: 1, date: -1 });
ledgerSchema.index({ addedBy: 1, date: -1 });
ledgerSchema.index({ category: 1, date: -1 });
ledgerSchema.index({ fiscalYear: 1, quarter: 1 });
ledgerSchema.index({ date: -1 });
ledgerSchema.index({ isIncome: 1, date: -1 });

// Pre-save middleware to set category and fiscal data
ledgerSchema.pre('save', function(next) {
  // Set category based on type
  const categoryMap = {
    'Vehicle Purchase': 'vehicle-purchase',
    'Repair Cost': 'vehicle-repair',
    'General Cost': 'general-cost',
    'Sale': 'sale',
    'Insurance': 'insurance',
    'Registration': 'registration',
    'Inspection': 'inspection',
    'Marketing': 'marketing',
    'Office Supplies': 'office',
    'Utilities': 'utilities',
    'Other Income': 'other',
    'Other Expense': 'other'
  };
  
  this.category = categoryMap[this.type] || 'other';
  
  // Set isIncome based on type
  const incomeTypes = ['Sale', 'Other Income'];
  this.isIncome = incomeTypes.includes(this.type);
  
  // Set fiscal year and quarter
  const transactionDate = new Date(this.date);
  this.fiscalYear = transactionDate.getFullYear();
  this.quarter = Math.ceil((transactionDate.getMonth() + 1) / 3);
  
  // Set receipt count
  this.receiptCount = this.receiptUrls ? this.receiptUrls.length : 0;
  
  next();
});

// Virtual for formatted amount
ledgerSchema.virtual('formattedAmount').get(function() {
  const currencyMap = {
    'CAD': 'en-CA',
    'USD': 'en-US',
    'EUR': 'de-DE'
  };
  
  return new Intl.NumberFormat(currencyMap[this.currency] || 'en-CA', {
    style: 'currency',
    currency: this.currency
  }).format(this.amount);
});

// Virtual for display date
ledgerSchema.virtual('displayDate').get(function() {
  return new Intl.DateTimeFormat('en-CA', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(this.date);
});

// Method to generate S3 path
ledgerSchema.methods.generateS3Path = function() {
  const date = new Date(this.date);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const transactionId = this._id.toString();
  
  return `receipts/${this.category}/${year}-${month}/${transactionId}/`;
};

// Static method to get transactions by date range
ledgerSchema.statics.findByDateRange = function(startDate, endDate, filters = {}) {
  const query = {
    date: {
      $gte: new Date(startDate),
      $lte: new Date(endDate)
    },
    ...filters
  };
  
  return this.find(query)
    .populate('vehicleId', 'make model year vin')
    .sort({ date: -1 });
};

// Static method to get summary by period
ledgerSchema.statics.getSummary = function(startDate, endDate) {
  return this.aggregate([
    {
      $match: {
        date: {
          $gte: new Date(startDate),
          $lte: new Date(endDate)
        }
      }
    },
    {
      $group: {
        _id: '$isIncome',
        total: { $sum: '$amount' },
        count: { $sum: 1 },
        types: { $addToSet: '$type' }
      }
    }
  ]);
};

const Ledger = mongoose.models.Ledger || mongoose.model('Ledger', ledgerSchema);

export default Ledger;
