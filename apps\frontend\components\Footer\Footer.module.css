/* Footer Styles */

.footer {
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 50%, var(--bg-secondary) 100%);
  color: var(--text-primary);
  margin-top: -30px;
  padding-top: 4rem;
  position: relative;
  width: 100vw;
  margin-left: calc(-50vw + 50%);
  margin-right: calc(-50vw + 50%);
}

/* Enhanced dark mode footer contrast */
[data-theme="dark"] .footer {
  background: #151b22;
  border-top: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.3);
  width: 100vw;
  margin-left: calc(-50vw + 50%);
  margin-right: calc(-50vw + 50%);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Main Footer Content */
.footerContent {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 3rem;
  padding: 1rem 0 3rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Brand Section */
.brandSection {
  max-width: 350px;
}

.brandHeader {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.brandIcon {
  font-size: 2rem;
  color: var(--brand-red);
}

.brandName {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.brandFaze {
  color: var(--text-primary);
}

.brandN {
  color: var(--brand-red);
}

.brandAuto {
  color: var(--text-primary);
}

.brandDescription {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 1.5rem;
  font-size: 0.95rem;
}

.socialLinks {
  display: flex;
  gap: 1rem;
}

.socialLink {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--bg-tertiary);
  border-radius: 50%;
  color: var(--text-secondary);
  text-decoration: none;
  transition: all 0.3s ease;
  font-size: 1.2rem;
}

.socialLink:hover {
  background: var(--accent-primary);
  color: var(--text-primary);
  transform: translateY(-2px);
}

/* Link Sections */
.linkSection {
  display: flex;
  flex-direction: column;
}

.sectionTitle {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
  border-bottom: 2px solid var(--brand-red);
  padding-bottom: 0.5rem;
  display: inline-block;
}

.linkList {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.footerLink {
  color: var(--text-secondary);
  text-decoration: none;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  padding: 0.25rem 0;
  border-left: 3px solid transparent;
  padding-left: 0.5rem;
}

.footerLink:hover {
  color: var(--accent-secondary);
  border-left-color: var(--accent-primary);
  padding-left: 1rem;
}

.serviceItem {
  color: var(--text-secondary);
  font-size: 0.95rem;
  padding: 0.25rem 0;
  display: block;
}

/* Contact Section */
.contactSection {
  display: flex;
  flex-direction: column;
}

.contactAndHours {
  display: flex;
  gap: 2rem;
  align-items: flex-start;
}

.contactInfo {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.contactItem {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  color: var(--text-secondary);
  font-size: 0.95rem;
  line-height: 1.4;
}

.contactIcon {
  color: var(--brand-red);
  font-size: 1.2rem;
  margin-top: 0.1rem;
  flex-shrink: 0;
}

.businessHours {
  flex: 1;
  background: var(--bg-tertiary);
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid var(--brand-red);
  margin-top: 1.5rem;
}

.hoursTitle {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 1rem 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.hoursText {
  color: var(--text-secondary);
  font-size: 0.95rem;
  line-height: 1.5;
  margin: 0;
}

/* Footer Bottom */
.footerBottom {
  padding: 1.5rem 0;
}

.bottomContent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.copyright {
  color: var(--text-tertiary);
  font-size: 0.9rem;
  margin: 0;
}

.legalLinks {
  display: flex;
  gap: 2rem;
}

.legalLink {
  color: var(--text-tertiary);
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

/* Light mode - red hover color matching input focus */
:root .legalLink:hover {
  color: #e53e3e; /* Red color matching light mode input focus */
}

[data-theme="light"] .legalLink:hover {
  color: #e53e3e; /* Red color matching light mode input focus */
}

/* Dark mode - blue hover color matching input focus */
[data-theme="dark"] .legalLink:hover {
  color: rgba(99, 179, 237, 0.9); /* Blue color matching dark mode input focus */
}

/* Footer Controls */
.footerControls {
  display: flex;
  align-items: center;
  gap: 1rem;
  order: 2;
}

/* Adjust bottom content layout for controls */
.bottomContent {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  align-items: center;
  gap: 1rem;
}

.copyright {
  justify-self: start;
}

.footerControls {
  justify-self: center;
}

.legalLinks {
  justify-self: end;
}

/* Tablet Responsive */
@media (max-width: 1024px) {
  .footerContent {
    grid-template-columns: 1fr 2fr;
    gap: 2rem;
  }

  .brandSection {
    max-width: none;
  }

  .contactAndHours {
    display: flex;
    gap: 2rem;
    align-items: flex-start;
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .footerContent {
    grid-template-columns: 1fr;
    gap: 2rem;
    padding: 2rem 0;
  }
  
  .brandHeader {
    justify-content: center;
    text-align: center;
  }
  
  .brandDescription {
    text-align: center;
  }
  
  .socialLinks {
    justify-content: center;
  }
  
  .linkSection,
  .contactSection {
    text-align: left;
  }

  .contactAndHours {
    flex-direction: column;
    gap: 1.5rem;
    align-items: stretch;
  }

  .contactInfo {
    text-align: left;
  }

  .businessHours {
    margin-top: 0;
    text-align: left;
  }



  .sectionTitle {
    text-align: left;
    width: 100%;
  }

  .contactItem {
    justify-content: flex-start;
    text-align: center;
  }

  .businessHours {
    text-align: center;
  }

  .hoursTitle {
    justify-content: center;
  }

  .bottomContent {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 1.5rem;
  }

  .copyright,
  .footerControls,
  .legalLinks {
    justify-self: center;
  }

  .legalLinks {
    justify-content: center;
  }

  .footerControls {
    flex-direction: column;
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 0.5rem;
  }
  
  .footerContent {
    padding: 1.5rem 0;
  }
  
  .brandName {
    font-size: 1.5rem;
  }
  
  .socialLinks {
    gap: 0.75rem;
  }
  
  .socialLink {
    width: 35px;
    height: 35px;
    font-size: 1rem;
  }
  
  .legalLinks {
    flex-direction: column;
    gap: 1rem;
  }
}

/* RTL Support for Footer */
[dir="rtl"] .footer {
  direction: rtl;
  text-align: right;
}

[dir="rtl"] .brandHeader {
  flex-direction: row-reverse;
}

[dir="rtl"] .socialLinks {
  flex-direction: row-reverse;
}

[dir="rtl"] .contactItem {
  flex-direction: row-reverse;
  text-align: right;
}

[dir="rtl"] .businessHours {
  border-left: none;
  border-right: 4px solid var(--brand-red);
}

[dir="rtl"] .footerLink {
  border-left: none;
  border-right: 3px solid transparent;
  padding-left: 0;
  padding-right: 0.5rem;
}

[dir="rtl"] .footerLink:hover {
  border-right-color: var(--accent-primary);
  border-left-color: transparent;
  padding-right: 1rem;
  padding-left: 0;
}

[dir="rtl"] .hoursTitle {
  flex-direction: row-reverse;
}

[dir="rtl"] .bottomContent {
  direction: rtl;
}

[dir="rtl"] .legalLinks {
  flex-direction: row-reverse;
}

[dir="rtl"] .footerControls {
  flex-direction: row-reverse;
}
