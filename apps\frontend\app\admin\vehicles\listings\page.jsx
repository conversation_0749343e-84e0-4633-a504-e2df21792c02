'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import styles from './AdminListings.module.css';

export default function AdminListingsPage() {
  const [vehicles, setVehicles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [filters, setFilters] = useState({
    make: '',
    model: '',
    year: '',
    status: ''
  });
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [copiedUuid, setCopiedUuid] = useState('');
  const router = useRouter();

  const itemsPerPage = 20;

  useEffect(() => {
    fetchVehicles();
  }, [currentPage, filters]);

  const fetchVehicles = async () => {
    try {
      setLoading(true);
      const queryParams = new URLSearchParams({
        page: currentPage.toString(),
        limit: itemsPerPage.toString(),
        admin: 'true', // Mark as admin request to get all vehicles including drafts
        ...Object.fromEntries(Object.entries(filters).filter(([_, value]) => value))
      });

      const response = await fetch(`/api/vehicles?${queryParams}`);
      const data = await response.json();

      if (data.success) {
        setVehicles(data.data || []);
        setTotalPages(data.pagination?.pages || 1);
      } else {
        setError(data.error || 'Failed to fetch vehicles');
      }
    } catch (err) {
      setError('Error fetching vehicles: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (field, value) => {
    setFilters(prev => ({ ...prev, [field]: value }));
    setCurrentPage(1); // Reset to first page when filtering
  };

  const copyUuid = async (uuid) => {
    try {
      await navigator.clipboard.writeText(uuid);
      setCopiedUuid(uuid);
      setTimeout(() => setCopiedUuid(''), 2000);
    } catch (err) {
      console.error('Failed to copy UUID:', err);
    }
  };

  const getImagePreview = (vehicle) => {
    if (vehicle.media && vehicle.media.length > 0) {
      const firstImage = vehicle.media.find(m => m.type === 'image');
      return firstImage?.url;
    }
    return vehicle.images && vehicle.images.length > 0 ? vehicle.images[0] : null;
  };

  const formatMediaKeys = (media) => {
    if (!media || media.length === 0) return 'No media';
    return media.map(m => `${m.type}: ${m.key}`).join(', ');
  };

  if (loading) {
    return (
      <div className={styles.container}>
        <div className={styles.loading}>
          <div className={styles.spinner}></div>
          <p>Loading vehicles...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h1>Admin Listings</h1>
        <p>Complete vehicle inventory with UUID tracking and media management</p>
      </div>

      {/* Filters */}
      <div className={styles.filters}>
        <div className={styles.filterGroup}>
          <label>Make:</label>
          <input
            type="text"
            value={filters.make}
            onChange={(e) => handleFilterChange('make', e.target.value)}
            placeholder="Filter by make..."
            className={styles.filterInput}
          />
        </div>
        <div className={styles.filterGroup}>
          <label>Model:</label>
          <input
            type="text"
            value={filters.model}
            onChange={(e) => handleFilterChange('model', e.target.value)}
            placeholder="Filter by model..."
            className={styles.filterInput}
          />
        </div>
        <div className={styles.filterGroup}>
          <label>Year:</label>
          <input
            type="number"
            value={filters.year}
            onChange={(e) => handleFilterChange('year', e.target.value)}
            placeholder="Filter by year..."
            className={styles.filterInput}
          />
        </div>
        <div className={styles.filterGroup}>
          <label>Status:</label>
          <select
            value={filters.status}
            onChange={(e) => handleFilterChange('status', e.target.value)}
            className={styles.filterSelect}
          >
            <option value="">All Statuses</option>
            <option value="active">Active</option>
            <option value="draft">Draft</option>
            <option value="sold">Sold</option>
            <option value="on_hold">On Hold</option>
            <option value="archived">Archived</option>
          </select>
        </div>
      </div>

      {error && (
        <div className={styles.error}>
          <p>Error: {error}</p>
        </div>
      )}

      {/* Vehicle Grid */}
      <div className={styles.vehicleGrid}>
        {vehicles.map((vehicle) => (
          <div key={vehicle._id} className={styles.vehicleCard}>
            <div className={styles.cardHeader}>
              <h3>{vehicle.year} {vehicle.make} {vehicle.model}</h3>
              <span className={`${styles.status} ${styles[vehicle.status]}`}>
                {vehicle.status}
              </span>
            </div>

            <div className={styles.cardContent}>
              {/* Image Preview */}
              <div className={styles.imagePreview}>
                {getImagePreview(vehicle) ? (
                  <img 
                    src={getImagePreview(vehicle)} 
                    alt={`${vehicle.year} ${vehicle.make} ${vehicle.model}`}
                    className={styles.previewImage}
                  />
                ) : (
                  <div className={styles.noImage}>No Image</div>
                )}
              </div>

              {/* Vehicle Details */}
              <div className={styles.details}>
                <div className={styles.detailRow}>
                  <span className={styles.label}>UUID:</span>
                  <div className={styles.uuidContainer}>
                    <code className={styles.uuid}>{vehicle.uuid || 'Not set'}</code>
                    {vehicle.uuid && (
                      <button
                        onClick={() => copyUuid(vehicle.uuid)}
                        className={styles.copyButton}
                        title="Copy UUID"
                      >
                        {copiedUuid === vehicle.uuid ? '✓' : '📋'}
                      </button>
                    )}
                  </div>
                </div>
                
                <div className={styles.detailRow}>
                  <span className={styles.label}>ID:</span>
                  <code className={styles.mongoId}>{vehicle._id}</code>
                </div>
                
                <div className={styles.detailRow}>
                  <span className={styles.label}>VIN:</span>
                  <span>{vehicle.vin}</span>
                </div>

                <div className={styles.detailRow}>
                  <span className={styles.label}>Media:</span>
                  <div className={styles.mediaInfo}>
                    {vehicle.media && vehicle.media.length > 0 ? (
                      <details className={styles.mediaDetails}>
                        <summary>{vehicle.media.length} files</summary>
                        <div className={styles.mediaList}>
                          {vehicle.media.map((media, index) => (
                            <div key={index} className={styles.mediaItem}>
                              <span className={styles.mediaType}>{media.type}:</span>
                              <code className={styles.mediaKey}>{media.key}</code>
                            </div>
                          ))}
                        </div>
                      </details>
                    ) : (
                      <span className={styles.noMedia}>No media files</span>
                    )}
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className={styles.actions}>
                <button
                  onClick={() => router.push(`/admin/vehicles/edit/${vehicle._id}`)}
                  className={styles.editButton}
                >
                  Edit
                </button>
                <button
                  onClick={() => router.push(`/vehicles/${vehicle._id}`)}
                  className={styles.viewButton}
                >
                  View
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className={styles.pagination}>
          <button
            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
            className={styles.pageButton}
          >
            Previous
          </button>
          
          <span className={styles.pageInfo}>
            Page {currentPage} of {totalPages}
          </span>
          
          <button
            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
            disabled={currentPage === totalPages}
            className={styles.pageButton}
          >
            Next
          </button>
        </div>
      )}

      {vehicles.length === 0 && !loading && (
        <div className={styles.noResults}>
          <p>No vehicles found matching your criteria.</p>
        </div>
      )}
    </div>
  );
}
