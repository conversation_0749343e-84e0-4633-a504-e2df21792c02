import mongoose from 'mongoose';

const featureSchema = new mongoose.Schema(
  {
    category: {
      type: String,
      required: true,
      enum: ['exterior', 'interior', 'mechanical', 'safety', 'entertainment'],
      index: true
    },
    value: {
      type: String,
      required: true,
      unique: true,
      trim: true
    }
  },
  {
    timestamps: true
  }
);

// Compound index for efficient category-based queries
featureSchema.index({ category: 1, value: 1 });

// Static method to get features grouped by category
featureSchema.statics.getGroupedFeatures = function() {
  return this.aggregate([
    {
      $group: {
        _id: '$category',
        features: {
          $push: {
            _id: '$_id',
            value: '$value',
            createdAt: '$createdAt'
          }
        }
      }
    },
    {
      $sort: { '_id': 1 }
    }
  ]);
};

// Static method to get features by category
featureSchema.statics.getByCategory = function(category) {
  return this.find({ category }).sort({ value: 1 });
};

// Instance method to format for display
featureSchema.methods.getDisplayName = function() {
  return this.value.charAt(0).toUpperCase() + this.value.slice(1);
};

// Prevent model overwrite issues in dev/hot reload
export default mongoose.models.Feature || mongoose.model('Feature', featureSchema);
