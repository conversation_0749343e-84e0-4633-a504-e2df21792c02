import mongoose, { Schema } from 'mongoose';

/**
 * RIN (Registration Identification Number) Schema
 * Stores dealer registration information for compliance forms
 */
const rinSchema = new Schema(
  {
    businessName: {
      type: String,
      required: true,
      trim: true,
      index: true
    },
    
    address: {
      type: String,
      required: true,
      trim: true
    },
    
    phone: {
      type: String,
      required: true,
      trim: true
    },
    
    email: {
      type: String,
      required: true,
      trim: true,
      lowercase: true
    },
    
    rin: {
      type: String,
      required: true,
      unique: true,
      trim: true,
      index: true
    },
    
    omvicNumber: {
      type: String,
      trim: true,
      sparse: true // Allow null/undefined values but ensure uniqueness when present
    },
    
    // Additional dealer information
    licenseNumber: {
      type: String,
      trim: true
    },
    
    isActive: {
      type: Boolean,
      default: true,
      index: true
    },
    
    // Contact person information
    contactPerson: {
      name: String,
      title: String,
      phone: String,
      email: String
    },
    
    // Business details
    businessType: {
      type: String,
      enum: ['dealer', 'broker', 'individual'],
      default: 'dealer'
    },
    
    notes: {
      type: String,
      trim: true
    }
  },
  { 
    timestamps: true,
    collection: 'rins'
  }
);

// Indexes for performance
rinSchema.index({ businessName: 1, isActive: 1 });
rinSchema.index({ rin: 1 }, { unique: true });
rinSchema.index({ omvicNumber: 1 }, { sparse: true, unique: true });

// Virtual for display name
rinSchema.virtual('displayName').get(function() {
  return `${this.businessName} (RIN: ${this.rin})`;
});

// Ensure virtual fields are serialized
rinSchema.set('toJSON', { virtuals: true });
rinSchema.set('toObject', { virtuals: true });

export default mongoose.models.Rin || mongoose.model('Rin', rinSchema);
