.container {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  gap: 2rem;
}

.titleSection h1 {
  margin: 0 0 0.5rem 0;
  color: #1a1a1a;
  font-size: 2rem;
}

.titleSection p {
  margin: 0;
  color: #666;
  font-size: 1rem;
}

.actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.addButton {
  background: #10b981;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 500;
  transition: background-color 0.2s;
  border: none;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.addButton:hover {
  background: #059669;
}

.filterToggle {
  background: #3b82f6;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.filterToggle:hover {
  background: #2563eb;
}

.exportButton {
  background: #8b5cf6;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.exportButton:hover {
  background: #7c3aed;
}

/* Summary Cards */
.summaryCards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.summaryCard {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.summaryCard h3 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  color: #374151;
  font-weight: 600;
}

.summaryCard p {
  margin: 0 0 0.5rem 0;
  font-size: 1.75rem;
  font-weight: 700;
}

.summaryCard small {
  color: #6b7280;
  font-size: 0.875rem;
}

.incomeAmount {
  color: #10b981 !important;
}

.expenseAmount {
  color: #ef4444 !important;
}

/* Filters Panel */
.filtersPanel {
  background: #f9fafb;
  padding: 1.5rem;
  border-radius: 12px;
  margin-bottom: 2rem;
  border: 1px solid #e5e7eb;
}

.filtersGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.filterInput,
.filterSelect {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.875rem;
  background: white;
}

.filterInput:focus,
.filterSelect:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.filterActions {
  display: flex;
  justify-content: flex-end;
}

.clearButton {
  background: #6b7280;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  border: none;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background-color 0.2s;
}

.clearButton:hover {
  background: #4b5563;
}

/* Table Styles */
.tableContainer {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  margin-bottom: 2rem;
}

.ledgerTable {
  width: 100%;
  border-collapse: collapse;
}

.ledgerTable th {
  background: #f9fafb;
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e5e7eb;
  font-size: 0.875rem;
}

.ledgerTable td {
  padding: 1rem;
  border-bottom: 1px solid #f3f4f6;
  vertical-align: top;
}

.ledgerTable tr:hover {
  background: #f9fafb;
}

.incomeRow {
  border-left: 4px solid #10b981;
}

.expenseRow {
  border-left: 4px solid #ef4444;
}

.description {
  max-width: 300px;
  word-wrap: break-word;
}

.typeTag {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.typeTag.sale {
  background: #dcfce7;
  color: #166534;
}

.typeTag.vehiclePurchase {
  background: #fef3c7;
  color: #92400e;
}

.typeTag.vehicleRepair {
  background: #fee2e2;
  color: #991b1b;
}

.typeTag.generalCost {
  background: #e0e7ff;
  color: #3730a3;
}

.typeTag.other {
  background: #f3f4f6;
  color: #374151;
}

.vehicleInfo {
  font-size: 0.875rem;
  color: #374151;
  font-weight: 500;
}

.noVehicle,
.noReceipts {
  color: #9ca3af;
  font-style: italic;
}

.receiptCount {
  color: #059669;
  font-weight: 500;
  font-size: 0.875rem;
}

.viewButton {
  background: #3b82f6;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  transition: background-color 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
}

.viewButton:hover {
  background: #2563eb;
}

/* Loading and Error States */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  color: #6b7280;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 2rem;
}

.noData {
  text-align: center;
  padding: 4rem;
  color: #6b7280;
}

.noData p {
  margin-bottom: 1rem;
  font-size: 1.125rem;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-top: 2rem;
}

.pageButton {
  background: #3b82f6;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  border: none;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.pageButton:hover:not(:disabled) {
  background: #2563eb;
}

.pageButton:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.pageInfo {
  color: #6b7280;
  font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }

  .header {
    flex-direction: column;
    align-items: stretch;
  }

  .actions {
    justify-content: stretch;
  }

  .actions > * {
    flex: 1;
    text-align: center;
  }

  .summaryCards {
    grid-template-columns: 1fr;
  }

  .filtersGrid {
    grid-template-columns: 1fr;
  }

  .tableContainer {
    overflow-x: auto;
  }

  .ledgerTable {
    min-width: 800px;
  }

  .pagination {
    flex-direction: column;
    gap: 0.5rem;
  }
}
