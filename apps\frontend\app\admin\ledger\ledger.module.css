.container {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  background-color: #ffffff;
  min-height: 100vh;
  color: var(--text-primary, #333);
  transition: background-color 0.3s ease;
}

[data-theme="dark"] .container {
  background: var(--bg-primary);
  color: var(--text-primary);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  gap: 2rem;
}

.titleSection h1 {
  margin: 0 0 0.5rem 0;
  color: var(--text-primary, #1a1a1a);
  font-size: 2rem;
}

.titleSection p {
  margin: 0;
  color: var(--text-secondary, #666);
  font-size: 1rem;
}

.actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.addButton {
  background: #10b981;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 500;
  transition: background-color 0.2s;
  border: none;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.addButton:hover {
  background: #059669;
}

/* Mobile Actions - Hidden by default */
.mobileActions {
  display: none;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.mobileAddButton {
  grid-column: 1 / -1;
  background: #10b981;
  color: white;
  padding: 1rem;
  border-radius: 12px;
  text-decoration: none;
  font-weight: 600;
  text-align: center;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  min-height: 48px;
}

.mobileAddButton:hover {
  background: #059669;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.mobileFilterToggle,
.mobileExportButton {
  background: var(--bg-secondary, #f8f9fa);
  color: var(--text-primary, #333);
  padding: 0.75rem;
  border-radius: 12px;
  border: 1px solid var(--border-color, #e5e7eb);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  min-height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.mobileFilterToggle:hover,
.mobileExportButton:hover {
  background: var(--bg-tertiary, #e9ecef);
  transform: translateY(-1px);
}

.filterToggle {
  background: #3b82f6;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.filterToggle:hover {
  background: #2563eb;
}

.exportButton {
  background: #8b5cf6;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.exportButton:hover {
  background: #7c3aed;
}

/* Summary Cards */
.summaryCards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.summaryCard {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.summaryCard h3 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  color: #374151;
  font-weight: 600;
}

.summaryCard p {
  margin: 0 0 0.5rem 0;
  font-size: 1.75rem;
  font-weight: 700;
}

.summaryCard small {
  color: #6b7280;
  font-size: 0.875rem;
}

.incomeAmount {
  color: #10b981 !important;
}

.expenseAmount {
  color: #ef4444 !important;
}

/* Filters Panel */
.filtersPanel {
  background: #f9fafb;
  padding: 1.5rem;
  border-radius: 12px;
  margin-bottom: 2rem;
  border: 1px solid #e5e7eb;
}

.filtersGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.filterInput,
.filterSelect {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.875rem;
  background: white;
}

.filterInput:focus,
.filterSelect:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.filterActions {
  display: flex;
  justify-content: flex-end;
}

.clearButton {
  background: #6b7280;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  border: none;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background-color 0.2s;
}

.clearButton:hover {
  background: #4b5563;
}

/* Table Styles */
.tableContainer {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  margin-bottom: 2rem;
}

.ledgerTable {
  width: 100%;
  border-collapse: collapse;
}

.ledgerTable th {
  background: #f9fafb;
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e5e7eb;
  font-size: 0.875rem;
}

.ledgerTable td {
  padding: 1rem;
  border-bottom: 1px solid #f3f4f6;
  vertical-align: top;
}

.ledgerTable tr:hover {
  background: #f9fafb;
}

.incomeRow {
  border-left: 4px solid #10b981;
}

.expenseRow {
  border-left: 4px solid #ef4444;
}

.description {
  max-width: 300px;
  word-wrap: break-word;
}

.typeTag {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.typeTag.sale {
  background: #dcfce7;
  color: #166534;
}

.typeTag.vehiclePurchase {
  background: #fef3c7;
  color: #92400e;
}

.typeTag.vehicleRepair {
  background: #fee2e2;
  color: #991b1b;
}

.typeTag.generalCost {
  background: #e0e7ff;
  color: #3730a3;
}

.typeTag.other {
  background: #f3f4f6;
  color: #374151;
}

.vehicleInfo {
  font-size: 0.875rem;
  color: #374151;
  font-weight: 500;
}

.noVehicle,
.noReceipts {
  color: #9ca3af;
  font-style: italic;
}

.receiptCount {
  color: #059669;
  font-weight: 500;
  font-size: 0.875rem;
}

.viewButton {
  background: #3b82f6;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  transition: background-color 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
}

.viewButton:hover {
  background: #2563eb;
}

/* Loading and Error States */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  color: #6b7280;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 2rem;
}

.noData {
  text-align: center;
  padding: 4rem;
  color: #6b7280;
}

.noData p {
  margin-bottom: 1rem;
  font-size: 1.125rem;
}

/* Mobile Transaction Cards - Hidden by default */
.mobileCardsContainer {
  display: none;
  flex-direction: column;
  gap: 1rem;
}

.transactionCard {
  background: var(--bg-secondary, white);
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: 16px;
  padding: 1.5rem;
  transition: all 0.2s ease;
  cursor: pointer;
}

.transactionCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.incomeCard {
  border-left: 4px solid #10b981;
}

.expenseCard {
  border-left: 4px solid #ef4444;
}

.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.cardDate {
  font-size: 0.875rem;
  color: var(--text-secondary, #6b7280);
  font-weight: 500;
}

.cardAmount {
  font-size: 1.25rem;
  font-weight: 700;
}

.cardContent {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.cardType {
  display: flex;
  align-items: center;
}

.cardDescription {
  font-size: 1rem;
  color: var(--text-primary, #333);
  line-height: 1.4;
}

.cardVehicle {
  font-size: 0.875rem;
  color: var(--text-secondary, #6b7280);
  background: var(--bg-tertiary, #f3f4f6);
  padding: 0.5rem 0.75rem;
  border-radius: 8px;
  display: inline-block;
}

.cardMeta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
  color: var(--text-secondary, #6b7280);
  gap: 1rem;
}

.cardReceipts {
  background: var(--bg-tertiary, #f3f4f6);
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
}

.cardAddedBy {
  font-style: italic;
}

.cardActions {
  display: flex;
  justify-content: flex-end;
}

.cardViewButton {
  background: #3b82f6;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.cardViewButton:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

.mobileNoData {
  text-align: center;
  padding: 3rem 1rem;
  color: var(--text-secondary, #6b7280);
  background: var(--bg-secondary, white);
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: 16px;
}

.mobileNoData p {
  margin: 0 0 1rem 0;
  font-size: 1.125rem;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-top: 2rem;
}

.pageButton {
  background: #3b82f6;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  border: none;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.pageButton:hover:not(:disabled) {
  background: #2563eb;
}

.pageButton:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.pageInfo {
  color: #6b7280;
  font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }

  /* Hide desktop header actions, show mobile actions */
  .header {
    margin-bottom: 1rem;
  }

  .actions {
    display: none;
  }

  .mobileActions {
    display: grid;
  }

  /* Make summary cards responsive */
  .summaryCards {
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
  }

  .summaryCard {
    padding: 1rem;
  }

  .summaryCard h3 {
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
  }

  .summaryCard p {
    font-size: 1.5rem;
  }

  .summaryCard small {
    font-size: 0.75rem;
  }

  /* Hide desktop table, show mobile cards */
  .tableContainer {
    display: none;
  }

  .mobileCardsContainer {
    display: flex;
  }

  /* Make filters responsive */
  .filtersGrid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .filterInput,
  .filterSelect {
    padding: 0.75rem;
    font-size: 1rem;
    min-height: 48px;
  }

  .filterActions {
    justify-content: center;
  }

  .clearButton {
    padding: 0.75rem 1.5rem;
    min-height: 48px;
  }

  /* Make pagination mobile-friendly */
  .pagination {
    flex-direction: column;
    gap: 0.75rem;
  }

  .pageButton {
    padding: 0.75rem 1rem;
    min-height: 48px;
  }

  .pageInfo {
    text-align: center;
  }
}
