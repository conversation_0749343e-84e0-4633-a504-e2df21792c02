.complianceFormsPanel {
  background: none;
  border-radius: 0;
  padding: 24px 0;
  margin: 20px 0;
  box-shadow: none;
  border: none;
  border-top: 1px solid var(--border-color, rgba(0, 0, 0, 0.1));
}

.header {
  margin-bottom: 24px;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.header h3 {
  color: var(--text-primary, #333);
  margin: 0;
  font-size: 1.5rem;
}

.header p {
  color: var(--text-secondary, #666);
  margin: 0;
  font-size: 0.95rem;
}

.selectedCount {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(220, 38, 38, 0.3);
}

.section {
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.section h4 {
  color: #333;
  margin: 0 0 16px 0;
  font-size: 1.1rem;
  font-weight: 600;
}

/* Vehicle Selection - using global .formInput class */

/* Form Type Selection */
.formTypesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.formTypeCard {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 20px;
  border: 2px solid var(--border-color, #e0e0e0);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: var(--bg-secondary, white);
}

.formTypeCard:hover {
  border-color: #dc2626;
  background: var(--bg-hover, rgba(220, 38, 38, 0.02));
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.formTypeCard.selected {
  border-color: #dc2626;
  background: var(--bg-hover, rgba(220, 38, 38, 0.05));
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.2);
}

.formIcon {
  font-size: 2rem;
  flex-shrink: 0;
}

.formInfo {
  flex: 1;
}

.formInfo h5 {
  margin: 0 0 8px 0;
  color: var(--text-primary, #333);
  font-size: 1.1rem;
  font-weight: 600;
}

.formInfo p {
  margin: 0;
  color: var(--text-secondary, #666);
  font-size: 0.9rem;
  line-height: 1.4;
}

/* Form Fields */
.formFields {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.fieldGroup {
  background: none;
  border-radius: 0;
  padding: 20px 0;
  border: none;
  border-bottom: 1px solid var(--border-color, #e2e8f0);
}

.fieldGroup h5 {
  margin: 0 0 16px 0;
  color: var(--text-primary, #333);
  font-size: 1rem;
  font-weight: 600;
  border-bottom: 2px solid #dc2626;
  padding-bottom: 8px;
}

.vehicleSelector {
  margin-bottom: 16px;
  padding: 16px 0;
  background: none;
  border-radius: 0;
  border: none;
}

/* Vehicle dropdown - using global .formInput class */

/* RIN Selector */
.rinSelector {
  margin-bottom: 16px;
  padding: 16px 0;
  background: none;
  border-radius: 0;
  border: none;
}

/* RIN dropdown - using global .formInput class */

.fieldsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

/* Fields grid inputs - using global .formInput class */

/* Textarea styling removed - using global .formInput class with additional properties */
.textarea {
  resize: vertical;
  min-height: 100px;
}

/* Signature Section */
.signatureSection {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-top: 16px;
}

.signatureBox {
  background: none;
  border: none;
  border-radius: 0;
  padding: 16px 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
  border-bottom: 1px solid var(--border-primary, #e0e0e0);
}

.signatureBox h6 {
  margin: 0;
  color: var(--text-primary, #333);
  font-size: 0.95rem;
  font-weight: 600;
  text-align: center;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border-primary, #e0e0e0);
}



/* Generate Button */
.generateSection {
  text-align: center;
  margin: 24px 0;
}

.generateButton {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  color: white;
  border: none;
  padding: 16px 32px;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 200px;
}

.generateButton:hover:not(:disabled) {
  background: linear-gradient(135deg, #b91c1c, #991b1b);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
}

.generateButton:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Results */
.results {
  margin-top: 20px;
  padding: 16px;
  border-radius: 8px;
  text-align: center;
}

.successMessage {
  background: rgba(5, 150, 105, 0.1);
  color: #059669;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid rgba(5, 150, 105, 0.2);
  font-weight: 600;
}

.errorMessage {
  background: rgba(220, 38, 38, 0.1);
  color: #dc2626;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid rgba(220, 38, 38, 0.2);
  font-weight: 600;
}

/* Info Note */
.infoNote {
  background: rgba(59, 130, 246, 0.1);
  border-radius: 8px;
  padding: 20px;
  border-left: 4px solid #3b82f6;
  margin-top: 24px;
}

.infoNote h4 {
  color: #1e40af;
  margin: 0 0 12px 0;
  font-size: 1rem;
}

.infoNote ul {
  margin: 0;
  padding-left: 20px;
  color: #1e40af;
}

.infoNote li {
  margin-bottom: 8px;
  font-size: 0.9rem;
  line-height: 1.4;
}

.infoNote li:last-child {
  margin-bottom: 0;
}

/* New Form Elements */
.checkboxGroup {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin: 16px 0;
}

.checkboxLabel {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 0.95rem;
  color: #333;
}

.checkboxLabel input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: #dc2626;
}

.radioGroup {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin: 12px 0;
}

.radioLabel {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  font-size: 0.95rem;
  color: #333;
}

.radioLabel input[type="radio"] {
  width: 16px;
  height: 16px;
  accent-color: #dc2626;
}

.fileInput {
  width: 100%;
  padding: 12px;
  border: 2px dashed #dc2626;
  border-radius: 8px;
  background: rgba(220, 38, 38, 0.02);
  cursor: pointer;
  transition: all 0.2s ease;
}

.fileInput:hover {
  background: rgba(220, 38, 38, 0.05);
  border-color: #b91c1c;
}

.fileNote {
  font-size: 0.85rem;
  color: #666;
  margin-top: 8px;
  font-style: italic;
}

.signatureSection {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-top: 16px;
}

.signatureGroup {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.signatureGroup label {
  font-weight: 600;
  color: #333;
  font-size: 0.95rem;
}



/* Mobile Responsiveness */
@media (max-width: 768px) {
  .complianceFormsPanel {
    padding: 16px;
    margin: 16px 0;
  }

  .formTypesGrid {
    grid-template-columns: 1fr;
  }

  .formTypeCard {
    padding: 16px;
  }

  .fieldsGrid {
    grid-template-columns: 1fr;
  }

  .generateButton {
    width: 100%;
    min-width: auto;
  }

  .fieldGroup {
    padding: 16px;
  }

  .signatureSection {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .radioGroup {
    flex-direction: column;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .header h3 {
    font-size: 1.3rem;
  }

  .section h4 {
    font-size: 1rem;
  }

  .formTypeCard {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .formIcon {
    font-size: 1.5rem;
  }

  .formInfo h5 {
    font-size: 1rem;
  }

  /* Fields grid inputs use global .formInput class */

  .generateButton {
    padding: 12px 24px;
    font-size: 1rem;
  }

  .fieldGroup h5 {
    font-size: 0.95rem;
  }
}

/* Invoice Mode - using global .formInput class */

.invoiceModeHelp {
  margin: 8px 0 0 0;
  padding: 10px 14px;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 500;
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  color: #856404;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Dynamic Fields Styles */
.dynamicFieldsContainer {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.dynamicField {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.dynamicField textarea {
  flex: 1;
  min-height: 60px;
  resize: vertical;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-family: inherit;
  font-size: 14px;
  line-height: 1.4;
}

.dynamicField textarea:focus {
  outline: none;
  border-color: #dc2626;
  box-shadow: 0 0 0 2px rgba(220, 38, 38, 0.1);
}

.addButton {
  background: linear-gradient(135deg, #16a34a, #15803d);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
  align-self: flex-start;
}

.addButton:hover {
  background: linear-gradient(135deg, #15803d, #166534);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(22, 163, 74, 0.3);
}

.removeButton {
  background: #ef4444;
  color: white;
  border: none;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-top: 8px;
}

.removeButton:hover {
  background: #dc2626;
  transform: scale(1.1);
}

/* Print Styles */
@media print {
  .complianceFormsPanel {
    box-shadow: none;
    border: none;
    background: white;
  }

  .generateButton,
  .infoNote {
    display: none;
  }
}
