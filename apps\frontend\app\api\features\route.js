import { NextResponse } from 'next/server';
import { connectToDatabase } from '../../../lib/dbConnect';
import Feature from '../../../models/Feature';

/**
 * GET /api/features - Get all features, optionally grouped by category
 */
export async function GET(request) {
  try {
    await connectToDatabase();
    
    const { searchParams } = new URL(request.url);
    const grouped = searchParams.get('grouped') === 'true';
    
    if (grouped) {
      // Return features grouped by category
      const groupedFeatures = await Feature.getGroupedFeatures();
      
      // Convert to object format for easier frontend consumption
      const featuresObject = {};
      groupedFeatures.forEach(group => {
        featuresObject[group._id] = group.features;
      });
      
      return NextResponse.json({
        success: true,
        data: featuresObject
      });
    } else {
      // Return all features as flat array
      const features = await Feature.find({}).sort({ category: 1, value: 1 });
      
      return NextResponse.json({
        success: true,
        data: features
      });
    }
  } catch (error) {
    console.error('Error fetching features:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch features'
    }, { status: 500 });
  }
}

/**
 * POST /api/features - Create a new feature
 */
export async function POST(request) {
  try {
    await connectToDatabase();
    
    const { category, value } = await request.json();
    
    // Validate required fields
    if (!category || !value) {
      return NextResponse.json({
        success: false,
        error: 'Category and value are required'
      }, { status: 400 });
    }
    
    // Validate category
    const validCategories = ['exterior', 'interior', 'mechanical', 'safety', 'entertainment'];
    if (!validCategories.includes(category)) {
      return NextResponse.json({
        success: false,
        error: 'Invalid category'
      }, { status: 400 });
    }
    
    // Create new feature
    const feature = new Feature({
      category: category.toLowerCase(),
      value: value.trim()
    });
    
    await feature.save();
    
    return NextResponse.json({
      success: true,
      message: 'Feature created successfully',
      data: feature
    }, { status: 201 });
    
  } catch (error) {
    console.error('Error creating feature:', error);
    
    // Handle duplicate value error
    if (error.code === 11000) {
      return NextResponse.json({
        success: false,
        error: 'Feature value already exists'
      }, { status: 409 });
    }
    
    return NextResponse.json({
      success: false,
      error: 'Failed to create feature'
    }, { status: 500 });
  }
}
