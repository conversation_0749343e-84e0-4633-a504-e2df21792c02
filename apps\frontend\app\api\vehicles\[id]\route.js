import { NextResponse } from 'next/server';
import Vehicle from '../../../../lib/models/Vehicle';
import { connectToDatabase } from '../../../../lib/dbConnect';
import { S3Client, DeleteObjectCommand, PutObjectCommand } from '@aws-sdk/client-s3';
import { v4 as uuidv4 } from 'uuid';
import path from 'path';
import { withDealerAuth } from '../../../../lib/authMiddleware';

const s3 = new S3Client({
  region: process.env.CUSTOM_AWS_REGION,
  credentials: {
    accessKeyId: process.env.CUSTOM_AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.CUSTOM_AWS_SECRET_ACCESS_KEY,
  },
});

export async function GET(req, { params }) {
  console.log('🔍 GET request for vehicle');

  try {
    await connectToDatabase();
    console.log('✅ Database connected for GET');

    // Await params in Next.js 15+
    const { id } = await params;
    console.log('🔍 Fetching vehicle with ID:', id);

    const vehicle = await Vehicle.findById(id);
    if (!vehicle) {
      console.log('❌ Vehicle not found');
      return NextResponse.json({ success: false, message: 'Vehicle not found' }, { status: 404 });
    }

    console.log('✅ Vehicle found:', vehicle.make, vehicle.model, vehicle.year);
    return NextResponse.json({ success: true, data: vehicle });
  } catch (error) {
    console.error('❌ Error fetching vehicle:', error);
    return NextResponse.json({ success: false, error: error.message }, { status: 500 });
  }
}

// Rate limiting to prevent spam
const lastRequestTimes = new Map();

export const PUT = withDealerAuth(async function(req, { params }) {
  console.log('🔄 PUT request received');

  // Get request details for debugging
  const userAgent = req.headers.get('user-agent');
  const referer = req.headers.get('referer');
  const contentType = req.headers.get('content-type');

  console.log('🔍 Request details:', {
    userAgent: userAgent?.substring(0, 100),
    referer,
    contentType
  });

  // Rate limiting - only allow one request per vehicle per 5 seconds
  const { id } = await params;
  const now = Date.now();
  const lastRequest = lastRequestTimes.get(id);

  if (lastRequest && (now - lastRequest) < 5000) {
    console.log('⚠️ Rate limited - too many requests for vehicle:', id);
    return NextResponse.json({
      success: false,
      error: 'Too many requests. Please wait before updating again.'
    }, { status: 429 });
  }

  lastRequestTimes.set(id, now);

  try {
    await connectToDatabase();

    // Await params in Next.js 15+
    const { id } = await params;
    console.log('🔄 Updating vehicle with ID:', id);
    const contentType = req.headers.get('content-type');
    let updateData = {};
    let newImages = [];
    let imagesToDelete = [];

    if (contentType && contentType.includes('multipart/form-data')) {
      // Handle FormData (with file uploads)
      const formData = await req.formData();

      // Extract form fields
      for (let [key, value] of formData.entries()) {
        if (key === 'newImages') {
          newImages.push(value);
        } else if (key === 'imagesToDelete') {
          imagesToDelete.push(value);
        } else if (key === 'features') {
          // Parse features JSON data
          try {
            const parsedFeatures = JSON.parse(value);
            console.log('📝 Parsed features:', parsedFeatures);

            // Clean and validate features data
            const cleanedFeatures = {
              exterior: [],
              interior: [],
              mechanical: [],
              safety: [],
              entertainment: []
            };

            // Process each category
            Object.keys(cleanedFeatures).forEach(category => {
              if (parsedFeatures[category] && Array.isArray(parsedFeatures[category])) {
                console.log(`🔍 Processing ${category} features:`, parsedFeatures[category]);
                cleanedFeatures[category] = parsedFeatures[category].filter(feature => {
                  // Only include features that have both value and status
                  const isValid = feature &&
                         typeof feature === 'object' &&
                         feature.value &&
                         feature.status &&
                         ['included', 'not_available'].includes(feature.status);

                  if (!isValid) {
                    console.log(`❌ Invalid feature in ${category}:`, feature);
                  }

                  return isValid;
                });
                console.log(`✅ Cleaned ${category} features:`, cleanedFeatures[category]);
              }
            });

            updateData.features = cleanedFeatures;
            console.log('🧹 Cleaned features:', cleanedFeatures);
          } catch (error) {
            console.error('Error parsing features data:', error);
            updateData.features = {
              exterior: [],
              interior: [],
              mechanical: [],
              safety: [],
              entertainment: []
            };
          }
        } else {
          updateData[key] = value;
        }
      }

      // Get current vehicle to access existing images
      const currentVehicle = await Vehicle.findById(id);
      if (!currentVehicle) {
        return NextResponse.json({ success: false, message: 'Vehicle not found' }, { status: 404 });
      }

      let updatedImages = [...(currentVehicle.images || [])];

      // Delete specified images from S3 and remove from array
      if (imagesToDelete.length > 0) {
        for (const imageUrl of imagesToDelete) {
          try {
            // Extract key from S3 URL
            // New format: vehicles/{make}/{model}/{year}/{uuid}.{ext}
            const urlParts = imageUrl.split('/');
            const bucketIndex = urlParts.findIndex(part => part.includes('.s3.'));
            let key;
            if (bucketIndex !== -1 && bucketIndex + 1 < urlParts.length) {
              key = urlParts.slice(bucketIndex + 1).join('/');
            } else {
              // Fallback for old format
              key = urlParts.slice(-2).join('/');
            }

            await s3.send(new DeleteObjectCommand({
              Bucket: process.env.CUSTOM_AWS_S3_BUCKET_NAME,
              Key: key,
            }));

            // Remove from images array
            updatedImages = updatedImages.filter(img => img !== imageUrl);
          } catch (s3Error) {
            console.error('Error deleting image from S3:', s3Error);
          }
        }
      }

      // Upload new images to S3
      if (newImages.length > 0) {
        for (const imageFile of newImages) {
          try {
            const buffer = Buffer.from(await imageFile.arrayBuffer());
            const uuid = uuidv4();
            const fileExtension = path.extname(imageFile.name);
            const vehicleYear = updateData.year || currentVehicle.year;
            const vehicleMake = (updateData.make || currentVehicle.make).replace(/\s+/g, '-').toLowerCase();
            const vehicleModel = (updateData.model || currentVehicle.model).replace(/\s+/g, '-').toLowerCase();
            const fileKey = `vehicles/${vehicleMake}/${vehicleModel}/${vehicleYear}/${uuid}${fileExtension}`;

            const uploadParams = {
              Bucket: process.env.CUSTOM_AWS_S3_BUCKET_NAME,
              Key: fileKey,
              Body: buffer,
              ContentType: imageFile.type,
              CacheControl: 'public, max-age=31536000',
            };

            await s3.send(new PutObjectCommand(uploadParams));

            const imageUrl = `https://${process.env.CUSTOM_AWS_S3_BUCKET_NAME}.s3.${process.env.CUSTOM_AWS_REGION}.amazonaws.com/${fileKey}`;
            updatedImages.push(imageUrl);
          } catch (uploadError) {
            console.error('Error uploading image:', uploadError);
          }
        }
      }

      // Update images array and primary image URL
      updateData.images = updatedImages;
      if (updatedImages.length > 0) {
        updateData.imageUrl = updatedImages[0]; // Set first image as primary
      }
    } else {
      // Handle JSON data (no file uploads)
      updateData = await req.json();
      console.log('📝 JSON Update data received:', updateData);

      // Ensure features is properly formatted if it exists
      if (updateData.features) {
        if (typeof updateData.features === 'string') {
          try {
            updateData.features = JSON.parse(updateData.features);
          } catch (error) {
            console.error('Error parsing features from JSON:', error);
            updateData.features = {
              exterior: [],
              interior: [],
              mechanical: [],
              safety: [],
              entertainment: []
            };
          }
        }

        // Clean and validate features data
        const cleanedFeatures = {
          exterior: [],
          interior: [],
          mechanical: [],
          safety: [],
          entertainment: []
        };

        // Process each category
        Object.keys(cleanedFeatures).forEach(category => {
          if (updateData.features[category] && Array.isArray(updateData.features[category])) {
            console.log(`🔍 Processing ${category} features (JSON):`, updateData.features[category]);
            cleanedFeatures[category] = updateData.features[category].filter(feature => {
              // Only include features that have both value and status
              const isValid = feature &&
                     typeof feature === 'object' &&
                     feature.value &&
                     feature.status &&
                     ['included', 'not_available'].includes(feature.status);

              if (!isValid) {
                console.log(`❌ Invalid feature in ${category} (JSON):`, feature);
              }

              return isValid;
            });
            console.log(`✅ Cleaned ${category} features (JSON):`, cleanedFeatures[category]);
          }
        });

        updateData.features = cleanedFeatures;
        console.log('🧹 Cleaned features (JSON):', cleanedFeatures);
      }
    }

    const updatedVehicle = await Vehicle.findByIdAndUpdate(id, updateData, {
      new: true,
      runValidators: true,
    });

    if (!updatedVehicle) {
      return NextResponse.json({ success: false, message: 'Vehicle not found' }, { status: 404 });
    }

    return NextResponse.json({ success: true, data: updatedVehicle });
  } catch (error) {
    console.error('Error updating vehicle:', error);
    return NextResponse.json({ success: false, error: error.message }, { status: 500 });
  }
});

export const DELETE = withDealerAuth(async function(req, { params }) {
  console.log('🗑️ DELETE request received');

  try {
    await connectToDatabase();
    console.log('✅ Database connected');

    // Await params in Next.js 15+
    const { id } = await params;
    console.log('🔍 Deleting vehicle with ID:', id);

    // First, get the vehicle to access its images
    const vehicle = await Vehicle.findById(id);

    if (!vehicle) {
      console.log('❌ Vehicle not found');
      return NextResponse.json({ success: false, message: 'Vehicle not found' }, { status: 404 });
    }

    console.log('✅ Vehicle found:', vehicle.make, vehicle.model, vehicle.year);

    // Skip S3 deletion for now to avoid timeout issues
    console.log('⚠️ Skipping S3 image deletion to prevent timeout');

    // TODO: Implement background job for S3 cleanup
    if (vehicle.images && vehicle.images.length > 0) {
      console.log(`ℹ️ Vehicle has ${vehicle.images.length} images that should be cleaned up later`);
    }

    // Delete vehicle from database
    console.log('🗑️ Deleting vehicle from database...');
    const deleteResult = await Vehicle.findByIdAndDelete(id);
    console.log('✅ Vehicle deleted from database:', deleteResult ? 'Success' : 'Not found');

    return NextResponse.json({
      success: true,
      message: 'Vehicle and associated images deleted successfully'
    });
  } catch (error) {
    console.error('❌ Error deleting vehicle:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to delete vehicle',
      details: error.stack
    }, { status: 500 });
  }
});
