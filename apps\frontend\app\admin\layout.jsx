'use client';
import { useState, useEffect } from 'react';
import Sidebar from '../../components/Sidebar/Sidebar';
import AdminProtectedLayout from '../../components/AdminProtectedLayout/AdminProtectedLayout';
import { useSidebar } from '../../contexts/SidebarContext';
import './admin.css'; // You'll add layout styles here

export default function AdminLayout({ children }) {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(true); // Start collapsed (showing icons)
  const [previouslyOpen, setPreviouslyOpen] = useState(false); // Track if sidebar was open before mobile
  const { setSidebarOpen } = useSidebar();

  // Initialize sidebar state on mount
  useEffect(() => {
    setSidebarOpen(false); // Sidebar starts collapsed (showing icons)
  }, [setSidebarOpen]);

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
    setSidebarOpen(sidebarCollapsed); // Update global sidebar state (inverted because we're toggling)

    // Track if sidebar was opened manually for desktop restoration
    if (window.innerWidth >= 768) {
      setPreviouslyOpen(sidebarCollapsed); // Will be true when opening sidebar
    }
  };

  // Handle screen size changes with proper responsive behavior
  useEffect(() => {
    if (typeof window !== "undefined") {
      let previousWidth = window.innerWidth;

      const handleResize = () => {
        const currentWidth = window.innerWidth;
        const isMobile = currentWidth < 768;
        const wasDesktop = previousWidth >= 768;
        const isDesktop = currentWidth >= 768;

        // Transitioning from desktop to mobile - force close sidebar
        if (wasDesktop && isMobile) {
          // Remember if sidebar was open before going mobile
          setPreviouslyOpen(!sidebarCollapsed);
          setSidebarCollapsed(true);
          setSidebarOpen(false);
        }

        // Transitioning from mobile to desktop - restore previous state if it was open
        if (!wasDesktop && isDesktop && previouslyOpen) {
          setSidebarCollapsed(false);
          setSidebarOpen(true);
        }

        // Update previous width for next comparison
        previousWidth = currentWidth;
      };

      window.addEventListener("resize", handleResize);
      return () => window.removeEventListener("resize", handleResize);
    }
  }, [sidebarCollapsed, previouslyOpen, setSidebarOpen]);

  return (
    <AdminProtectedLayout>
      <div className={`admin-layout ${sidebarCollapsed ? 'sidebar-collapsed' : ''}`}>
        <Sidebar collapsed={sidebarCollapsed} onToggle={toggleSidebar} />
        <main className="admin-content">
          {children}
        </main>
      </div>
    </AdminProtectedLayout>
  );
}
