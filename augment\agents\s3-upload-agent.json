{
  "name": "S3 Upload Management Agent",
  "description": "AI agent specialized in managing AWS S3 uploads for vehicle images, videos, and documents in the FazeNAuto platform",
  "version": "1.0.0",
  "capabilities": [
    "Upload images and videos to S3",
    "Manage file organization and naming",
    "Handle image processing and optimization",
    "Generate watermarks and branding",
    "Manage file permissions and access",
    "Handle batch uploads",
    "Monitor upload performance",
    "Implement file validation and security"
  ],
  "context": {
    "domain": "File Storage & Management",
    "platform": "FazeNAuto",
    "storage": "AWS S3",
    "processing": "Sharp.js for image processing",
    "framework": "Next.js"
  },
  "prompts": {
    "upload_vehicle_images": {
      "description": "Upload and process vehicle images to S3",
      "template": "Upload vehicle images to S3 with the following specifications:\n\n**Upload Details:**\n- Vehicle: {year} {make} {model}\n- VIN: {vin}\n- Image Count: {image_count}\n- File Types: {file_types}\n- Max Size: {max_size_mb}MB per file\n\n**Processing Requirements:**\n- Resize for web optimization\n- Generate thumbnails\n- Add watermarks: {watermark_enabled}\n- Compress for performance\n- Generate multiple formats if needed\n\n**S3 Configuration:**\n- Bucket: {bucket_name}\n- Path Structure: vehicles/{make}/{model}/{year}/\n- Naming: {uuid}.{extension}\n- Permissions: Public read\n- Cache Control: 1 year\n\n**Requirements:**\n1. Validate file types and sizes\n2. Process images in parallel\n3. Generate unique file names\n4. Create organized folder structure\n5. Return public URLs\n6. Handle upload failures gracefully\n7. Log upload metrics\n\nGenerate the S3 upload implementation.",
      "variables": [
        "year", "make", "model", "vin", "image_count", 
        "file_types", "max_size_mb", "watermark_enabled", 
        "bucket_name"
      ]
    },
    "batch_upload_processing": {
      "description": "Handle batch upload of multiple files",
      "template": "Process batch upload of {file_count} files:\n\n**Batch Details:**\n- Total Files: {file_count}\n- Total Size: {total_size_mb}MB\n- File Types: {file_types}\n- Processing Options: {processing_options}\n\n**Performance Requirements:**\n- Parallel uploads: {parallel_limit} concurrent\n- Progress tracking: Real-time updates\n- Error handling: Continue on individual failures\n- Memory management: Process in chunks\n\n**Output Requirements:**\n- Success/failure status per file\n- Upload URLs for successful files\n- Error details for failed uploads\n- Overall batch summary\n- Performance metrics\n\n**Requirements:**\n1. Implement parallel processing\n2. Track upload progress\n3. Handle memory efficiently\n4. Provide detailed error reporting\n5. Generate batch completion report\n6. Cleanup temporary files\n\nGenerate batch upload implementation.",
      "variables": [
        "file_count", "total_size_mb", "file_types", 
        "processing_options", "parallel_limit"
      ]
    },
    "image_optimization": {
      "description": "Optimize images for web performance",
      "template": "Optimize images for web performance:\n\n**Input Images:**\n- Count: {image_count}\n- Original Format: {original_format}\n- Average Size: {average_size_mb}MB\n\n**Optimization Settings:**\n- Target Formats: {target_formats}\n- Quality Settings: {quality_settings}\n- Resize Dimensions: {resize_dimensions}\n- Compression: {compression_level}\n\n**Output Requirements:**\n- Web-optimized versions\n- Thumbnail versions (150x150, 300x300)\n- Mobile-optimized versions\n- Original backup (optional)\n\n**Processing Steps:**\n1. Validate input images\n2. Generate multiple sizes/formats\n3. Apply compression\n4. Add watermarks if requested\n5. Upload all versions to S3\n6. Return URLs for each version\n\nGenerate image optimization implementation.",
      "variables": [
        "image_count", "original_format", "average_size_mb",
        "target_formats", "quality_settings", "resize_dimensions",
        "compression_level"
      ]
    },
    "watermark_application": {
      "description": "Apply watermarks to vehicle images",
      "template": "Apply FazeNAuto watermarks to vehicle images:\n\n**Watermark Settings:**\n- Logo: FazeNAuto with red 'N'\n- Position: {watermark_position}\n- Opacity: {watermark_opacity}%\n- Size: {watermark_size}% of image\n\n**Image Details:**\n- Input Images: {image_count}\n- Dimensions: Various sizes\n- Formats: {image_formats}\n\n**Requirements:**\n1. Load FazeNAuto logo/watermark\n2. Calculate optimal positioning\n3. Apply with specified opacity\n4. Maintain image quality\n5. Handle different image sizes\n6. Preserve original aspect ratios\n7. Generate watermarked versions\n\nGenerate watermark application implementation.",
      "variables": [
        "watermark_position", "watermark_opacity", 
        "watermark_size", "image_count", "image_formats"
      ]
    }
  ],
  "workflows": {
    "complete_upload_process": {
      "description": "End-to-end file upload and processing workflow",
      "steps": [
        {
          "step": 1,
          "action": "validate_files",
          "description": "Validate file types, sizes, and formats"
        },
        {
          "step": 2,
          "action": "generate_file_names",
          "description": "Generate unique file names and paths"
        },
        {
          "step": 3,
          "action": "process_images",
          "description": "Resize, optimize, and add watermarks"
        },
        {
          "step": 4,
          "action": "upload_to_s3",
          "description": "Upload processed files to S3"
        },
        {
          "step": 5,
          "action": "generate_urls",
          "description": "Generate public URLs for uploaded files"
        },
        {
          "step": 6,
          "action": "update_database",
          "description": "Store file URLs and metadata in database"
        },
        {
          "step": 7,
          "action": "cleanup_temp_files",
          "description": "Clean up temporary processing files"
        }
      ]
    },
    "bulk_migration": {
      "description": "Migrate existing files to new S3 structure",
      "steps": [
        {
          "step": 1,
          "action": "inventory_existing_files",
          "description": "Catalog all existing files and locations"
        },
        {
          "step": 2,
          "action": "plan_migration_structure",
          "description": "Plan new folder structure and naming"
        },
        {
          "step": 3,
          "action": "process_and_migrate",
          "description": "Process and move files to new structure"
        },
        {
          "step": 4,
          "action": "update_references",
          "description": "Update database references to new URLs"
        },
        {
          "step": 5,
          "action": "verify_migration",
          "description": "Verify all files migrated successfully"
        },
        {
          "step": 6,
          "action": "cleanup_old_files",
          "description": "Remove old files after verification"
        }
      ]
    }
  },
  "data_models": {
    "upload_request": {
      "required_fields": [
        "files", "upload_type", "target_path", "processing_options"
      ],
      "optional_fields": [
        "watermark_settings", "resize_options", "compression_level",
        "metadata", "tags", "access_permissions"
      ]
    },
    "upload_result": {
      "fields": [
        "success", "file_url", "file_key", "file_size", 
        "processing_time", "error_message", "metadata"
      ]
    },
    "batch_result": {
      "fields": [
        "total_files", "successful_uploads", "failed_uploads",
        "total_size", "processing_time", "error_summary"
      ]
    }
  },
  "api_endpoints": {
    "upload_single": "POST /api/upload/single",
    "upload_batch": "POST /api/upload/batch",
    "upload_vehicle_images": "POST /api/vehicles/upload-images",
    "process_images": "POST /api/image-processing",
    "get_upload_status": "GET /api/upload/status/[id]",
    "delete_file": "DELETE /api/upload/[key]"
  },
  "s3_configuration": {
    "bucket_structure": {
      "vehicles": "vehicles/{make}/{model}/{year}/",
      "processed": "vehicles/{vehicleId}/processed/",
      "thumbnails": "vehicles/{vehicleId}/thumbnails/",
      "documents": "documents/{type}/{date}/",
      "temp": "temp/{sessionId}/"
    },
    "naming_convention": {
      "images": "{uuid}.{extension}",
      "videos": "videos/{uuid}.{extension}",
      "thumbnails": "thumb_{size}_{uuid}.{extension}",
      "processed": "{platform}_{index}_{timestamp}.{extension}"
    },
    "permissions": {
      "public_read": ["vehicles/*", "processed/*"],
      "private": ["documents/*", "temp/*"],
      "authenticated_read": ["admin/*"]
    }
  },
  "file_validation": {
    "allowed_types": {
      "images": ["image/jpeg", "image/png", "image/webp"],
      "videos": ["video/mp4", "video/quicktime", "video/avi"],
      "documents": ["application/pdf", "application/msword"]
    },
    "size_limits": {
      "images": "10MB",
      "videos": "100MB",
      "documents": "5MB"
    },
    "security_checks": [
      "File type validation",
      "Malware scanning",
      "Content validation",
      "Size verification"
    ]
  },
  "processing_options": {
    "image_formats": {
      "jpeg": { "quality": 85, "progressive": true },
      "webp": { "quality": 80, "effort": 4 },
      "png": { "compressionLevel": 8 }
    },
    "resize_presets": {
      "thumbnail": { "width": 300, "height": 300, "fit": "cover" },
      "medium": { "width": 800, "height": 600, "fit": "inside" },
      "large": { "width": 1200, "height": 900, "fit": "inside" }
    },
    "watermark_settings": {
      "logo_path": "/assets/watermark/fazenauto-logo.png",
      "positions": ["bottom-right", "bottom-left", "center"],
      "opacity_range": [30, 70],
      "size_percentage": [5, 15]
    }
  },
  "performance_optimization": {
    "parallel_uploads": {
      "max_concurrent": 5,
      "chunk_size": "5MB",
      "retry_attempts": 3
    },
    "caching": {
      "cache_control": "public, max-age=31536000",
      "cdn_integration": "CloudFront distribution",
      "browser_caching": "Aggressive caching for static assets"
    },
    "compression": {
      "gzip_compression": true,
      "image_optimization": "Sharp.js with mozjpeg",
      "progressive_jpeg": true
    }
  },
  "error_handling": {
    "upload_failures": {
      "retry_logic": "Exponential backoff with max 3 attempts",
      "partial_failure": "Continue with successful uploads",
      "error_reporting": "Detailed error logs and user feedback"
    },
    "processing_errors": {
      "invalid_files": "Skip and report invalid files",
      "memory_issues": "Process in smaller batches",
      "timeout_handling": "Implement processing timeouts"
    }
  },
  "monitoring": {
    "metrics": [
      "Upload success rate",
      "Average upload time",
      "File processing performance",
      "Storage usage trends",
      "Error rates by type"
    ],
    "alerts": [
      "High failure rates",
      "Storage quota warnings",
      "Performance degradation",
      "Security violations"
    ],
    "logging": {
      "upload_events": "Log all upload attempts and results",
      "performance_metrics": "Track processing times and sizes",
      "error_details": "Detailed error logging for debugging"
    }
  },
  "security": {
    "access_control": {
      "authentication": "Require valid user session",
      "authorization": "Role-based upload permissions",
      "rate_limiting": "Prevent abuse with rate limits"
    },
    "file_security": {
      "virus_scanning": "Scan uploaded files for malware",
      "content_validation": "Validate file contents match extensions",
      "sanitization": "Remove metadata and potential threats"
    },
    "data_protection": {
      "encryption": "Encrypt files at rest and in transit",
      "backup_strategy": "Regular backups with versioning",
      "access_logging": "Log all file access attempts"
    }
  }
}
