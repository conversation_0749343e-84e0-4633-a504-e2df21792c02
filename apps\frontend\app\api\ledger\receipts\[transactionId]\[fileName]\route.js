import { NextResponse } from 'next/server';
import { connectToDatabase } from '../../../../../../lib/dbConnect';
import Ledger from '../../../../../../lib/models/Ledger';
import { S3Client, GetObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

const s3 = new S3Client({
  region: process.env.CUSTOM_AWS_REGION,
  credentials: {
    accessKeyId: process.env.CUSTOM_AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.CUSTOM_AWS_SECRET_ACCESS_KEY,
  },
});

// GET /api/ledger/receipts/[transactionId]/[fileName] - Download specific receipt
export async function GET(request, { params }) {
  try {
    await connectToDatabase();

    const { transactionId, fileName } = await params;

    // Verify transaction exists and user has access
    const transaction = await Ledger.findById(transactionId);
    
    if (!transaction) {
      return NextResponse.json(
        { success: false, error: 'Transaction not found' },
        { status: 404 }
      );
    }

    // Check if the file exists in the transaction's receipts
    const receiptExists = transaction.receiptUrls.some(url => 
      url.includes(fileName)
    );

    if (!receiptExists) {
      return NextResponse.json(
        { success: false, error: 'Receipt file not found' },
        { status: 404 }
      );
    }

    // Generate S3 key
    const s3Key = `${transaction.s3Path}${fileName}`;

    try {
      // Generate signed URL for download
      const command = new GetObjectCommand({
        Bucket: 'fazenauto-ledger-documents',
        Key: s3Key,
      });

      const signedUrl = await getSignedUrl(s3, command, { 
        expiresIn: 3600 // 1 hour
      });

      // Return redirect to signed URL
      return NextResponse.redirect(signedUrl);

    } catch (s3Error) {
      console.error('S3 error:', s3Error);
      return NextResponse.json(
        { success: false, error: 'File not accessible' },
        { status: 404 }
      );
    }

  } catch (error) {
    console.error('Error downloading receipt:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to download receipt' },
      { status: 500 }
    );
  }
}
