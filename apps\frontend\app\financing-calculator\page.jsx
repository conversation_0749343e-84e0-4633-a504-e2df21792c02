'use client';

import { useState } from 'react';
import { useLanguage } from '../../contexts/LanguageContext';
import { useTheme } from '../../contexts/ThemeContext';

export default function FinancingCalculator() {
  const { t } = useLanguage();
  const { currentTheme } = useTheme();
  
  const [formData, setFormData] = useState({
    vehiclePrice: '',
    tradeInValue: '',
    existingLoanBalance: '',
    downPayment: '',
    loanTerm: '48',
    interestRate: '6.99',
    provincialTax: '13'
  });

  const [results, setResults] = useState({
    totalFinanced: 0,
    monthlyPayment: 0,
    biWeeklyPayment: 0,
    weeklyPayment: 0,
    totalInterestMonthly: 0,
    totalInterestBiWeekly: 0,
    totalInterestWeekly: 0
  });

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const calculatePayments = () => {
    console.log('🧮 Calculate Payments called with formData:', formData);

    const vehiclePrice = parseFloat(formData.vehiclePrice) || 0;
    const tradeInValue = parseFloat(formData.tradeInValue) || 0;
    const existingLoanBalance = parseFloat(formData.existingLoanBalance) || 0;
    const downPayment = parseFloat(formData.downPayment) || 0;
    const provincialTaxRate = parseFloat(formData.provincialTax) / 100 || 0;
    const annualRate = parseFloat(formData.interestRate) / 100 || 0;
    const termMonths = parseInt(formData.loanTerm) || 48;

    console.log('🧮 Parsed values:', {
      vehiclePrice,
      tradeInValue,
      existingLoanBalance,
      downPayment,
      provincialTaxRate,
      annualRate,
      termMonths
    });

    // Calculate net trade-in value (trade-in minus existing loan)
    const netTradeIn = Math.max(0, tradeInValue - existingLoanBalance);

    // Calculate taxable amount (vehicle price minus trade-in value)
    const taxableAmount = Math.max(0, vehiclePrice - tradeInValue);
    const taxAmount = taxableAmount * provincialTaxRate;

    // Calculate total amount to be financed
    const totalFinanced = vehiclePrice + taxAmount - netTradeIn - downPayment;

    console.log('🧮 Calculation steps:', {
      netTradeIn,
      taxableAmount,
      taxAmount,
      totalFinanced
    });

    if (totalFinanced <= 0) {
      setResults({
        totalFinanced: 0,
        monthlyPayment: 0,
        biWeeklyPayment: 0,
        weeklyPayment: 0,
        totalInterestMonthly: 0,
        totalInterestBiWeekly: 0,
        totalInterestWeekly: 0
      });
      return;
    }

    // Monthly payment calculation
    const monthlyRate = annualRate / 12;
    let monthlyPayment = 0;
    let totalInterestMonthly = 0;

    if (monthlyRate > 0) {
      monthlyPayment = (totalFinanced * monthlyRate * Math.pow(1 + monthlyRate, termMonths)) / 
                      (Math.pow(1 + monthlyRate, termMonths) - 1);
      totalInterestMonthly = (monthlyPayment * termMonths) - totalFinanced;
    } else {
      monthlyPayment = totalFinanced / termMonths;
      totalInterestMonthly = 0;
    }

    // Bi-weekly payment calculation (26 payments per year)
    const biWeeklyRate = annualRate / 26;
    const biWeeklyTerms = Math.round((termMonths / 12) * 26);
    let biWeeklyPayment = 0;
    let totalInterestBiWeekly = 0;

    if (biWeeklyRate > 0) {
      biWeeklyPayment = (totalFinanced * biWeeklyRate * Math.pow(1 + biWeeklyRate, biWeeklyTerms)) / 
                       (Math.pow(1 + biWeeklyRate, biWeeklyTerms) - 1);
      totalInterestBiWeekly = (biWeeklyPayment * biWeeklyTerms) - totalFinanced;
    } else {
      biWeeklyPayment = totalFinanced / biWeeklyTerms;
      totalInterestBiWeekly = 0;
    }

    // Weekly payment calculation (52 payments per year)
    const weeklyRate = annualRate / 52;
    const weeklyTerms = Math.round((termMonths / 12) * 52);
    let weeklyPayment = 0;
    let totalInterestWeekly = 0;

    if (weeklyRate > 0) {
      weeklyPayment = (totalFinanced * weeklyRate * Math.pow(1 + weeklyRate, weeklyTerms)) / 
                     (Math.pow(1 + weeklyRate, weeklyTerms) - 1);
      totalInterestWeekly = (weeklyPayment * weeklyTerms) - totalFinanced;
    } else {
      weeklyPayment = totalFinanced / weeklyTerms;
      totalInterestWeekly = 0;
    }

    setResults({
      totalFinanced: totalFinanced,
      monthlyPayment: monthlyPayment,
      biWeeklyPayment: biWeeklyPayment,
      weeklyPayment: weeklyPayment,
      totalInterestMonthly: totalInterestMonthly,
      totalInterestBiWeekly: totalInterestBiWeekly,
      totalInterestWeekly: totalInterestWeekly
    });
  };

  return (
    <div style={{ 
      minHeight: '100vh', 
      background: currentTheme === 'dark' 
        ? 'linear-gradient(to bottom right, #0d1117, #161b22)' 
        : 'var(--bg-primary)',
      color: 'var(--text-primary)',
      padding: '2rem 1rem'
    }}>
      <div style={{ maxWidth: '1000px', margin: '0 auto' }}>
        {/* Header */}
        <div style={{ textAlign: 'center', marginBottom: '2rem' }}>
          <h1 style={{ 
            fontSize: '2.5rem', 
            fontWeight: 'bold', 
            color: 'var(--text-primary)',
            marginBottom: '0.5rem'
          }}>
            Finance Calculator
          </h1>
        </div>

        {/* Calculator Card */}
        <div style={{
          background: 'var(--bg-primary)',
          border: '1px solid var(--border-primary)',
          borderRadius: '12px',
          padding: '2rem',
          boxShadow: 'var(--card-shadow)',
          marginBottom: '2rem'
        }}>
          {/* Payment Calculator Header */}
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            marginBottom: '2rem',
            paddingBottom: '1rem',
            borderBottom: '1px solid var(--border-primary)'
          }}>
            <span style={{ fontSize: '1.2rem', marginRight: '0.5rem' }}>💳</span>
            <h2 style={{ 
              fontSize: '1.5rem', 
              fontWeight: '600',
              color: 'var(--text-primary)',
              margin: 0
            }}>
              Payment Calculator
            </h2>
          </div>

          {/* Form Grid */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
            gap: '1.5rem',
            marginBottom: '2rem'
          }}>
            {/* Left Column */}
            <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
              {/* Price of your new vehicle */}
              <div>
                <label style={{ 
                  display: 'block', 
                  fontSize: '0.9rem', 
                  fontWeight: '500',
                  color: 'var(--text-primary)',
                  marginBottom: '0.5rem'
                }}>
                  Price of your new vehicle
                </label>
                <div style={{ position: 'relative' }}>
                  <span style={{
                    position: 'absolute',
                    left: '12px',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    color: 'var(--text-secondary)',
                    fontSize: '1rem'
                  }}>$</span>
                  <input
                    type="number"
                    value={formData.vehiclePrice}
                    onChange={(e) => handleInputChange('vehiclePrice', e.target.value)}
                    className="formInput"
                    style={{ paddingLeft: '2rem' }}
                    placeholder="0"
                  />
                </div>
              </div>

              {/* Your existing vehicle loan balance */}
              <div>
                <label style={{ 
                  display: 'block', 
                  fontSize: '0.9rem', 
                  fontWeight: '500',
                  color: 'var(--text-primary)',
                  marginBottom: '0.5rem'
                }}>
                  Your existing vehicle loan balance
                </label>
                <div style={{ position: 'relative' }}>
                  <span style={{
                    position: 'absolute',
                    left: '12px',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    color: 'var(--text-secondary)',
                    fontSize: '1rem'
                  }}>$</span>
                  <input
                    type="number"
                    value={formData.existingLoanBalance}
                    onChange={(e) => handleInputChange('existingLoanBalance', e.target.value)}
                    className="formInput"
                    style={{ paddingLeft: '2rem' }}
                    placeholder="0"
                  />
                </div>
              </div>

              {/* Duration of your loan */}
              <div>
                <label style={{ 
                  display: 'block', 
                  fontSize: '0.9rem', 
                  fontWeight: '500',
                  color: 'var(--text-primary)',
                  marginBottom: '0.5rem'
                }}>
                  Duration of your loan
                </label>
                <select
                  value={formData.loanTerm}
                  onChange={(e) => handleInputChange('loanTerm', e.target.value)}
                  className="formInput"
                >
                  <option value="12">12 months</option>
                  <option value="24">24 months</option>
                  <option value="36">36 months</option>
                  <option value="48">48 months</option>
                  <option value="60">60 months</option>
                  <option value="72">72 months</option>
                  <option value="84">84 months</option>
                </select>
              </div>

              {/* Expected interest rate */}
              <div>
                <label style={{ 
                  display: 'block', 
                  fontSize: '0.9rem', 
                  fontWeight: '500',
                  color: 'var(--text-primary)',
                  marginBottom: '0.5rem'
                }}>
                  Expected interest rate
                </label>
                <div style={{ position: 'relative' }}>
                  <input
                    type="number"
                    step="0.01"
                    value={formData.interestRate}
                    onChange={(e) => handleInputChange('interestRate', e.target.value)}
                    className="formInput"
                    style={{ paddingRight: '2rem' }}
                    placeholder="6.99"
                  />
                  <span style={{
                    position: 'absolute',
                    right: '12px',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    color: 'var(--text-secondary)',
                    fontSize: '1rem'
                  }}>%</span>
                </div>
              </div>
            </div>

            {/* Right Column */}
            <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
              {/* Value of your trade-in vehicle */}
              <div>
                <label style={{
                  display: 'block',
                  fontSize: '0.9rem',
                  fontWeight: '500',
                  color: 'var(--text-primary)',
                  marginBottom: '0.5rem'
                }}>
                  Value of your trade-in vehicle
                </label>
                <div style={{ position: 'relative' }}>
                  <span style={{
                    position: 'absolute',
                    left: '12px',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    color: 'var(--text-secondary)',
                    fontSize: '1rem'
                  }}>$</span>
                  <input
                    type="number"
                    value={formData.tradeInValue}
                    onChange={(e) => handleInputChange('tradeInValue', e.target.value)}
                    className="formInput"
                    style={{ paddingLeft: '2rem' }}
                    placeholder="0"
                  />
                </div>
              </div>

              {/* Your down payment */}
              <div>
                <label style={{
                  display: 'block',
                  fontSize: '0.9rem',
                  fontWeight: '500',
                  color: 'var(--text-primary)',
                  marginBottom: '0.5rem'
                }}>
                  Your down payment
                </label>
                <div style={{ position: 'relative' }}>
                  <span style={{
                    position: 'absolute',
                    left: '12px',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    color: 'var(--text-secondary)',
                    fontSize: '1rem'
                  }}>$</span>
                  <input
                    type="number"
                    value={formData.downPayment}
                    onChange={(e) => handleInputChange('downPayment', e.target.value)}
                    className="formInput"
                    style={{ paddingLeft: '2rem' }}
                    placeholder="0"
                  />
                </div>
              </div>

              {/* Provincial sales tax */}
              <div>
                <label style={{
                  display: 'block',
                  fontSize: '0.9rem',
                  fontWeight: '500',
                  color: 'var(--text-primary)',
                  marginBottom: '0.5rem'
                }}>
                  Provincial sales tax
                </label>
                <div style={{ position: 'relative' }}>
                  <input
                    type="number"
                    step="0.01"
                    value={formData.provincialTax}
                    onChange={(e) => handleInputChange('provincialTax', e.target.value)}
                    className="formInput"
                    style={{ paddingRight: '2rem' }}
                    placeholder="13"
                  />
                  <span style={{
                    position: 'absolute',
                    right: '12px',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    color: 'var(--text-secondary)',
                    fontSize: '1rem'
                  }}>%</span>
                </div>
              </div>

              {/* Your total amount to be financed */}
              <div>
                <label style={{
                  display: 'block',
                  fontSize: '0.9rem',
                  fontWeight: '500',
                  color: 'var(--text-primary)',
                  marginBottom: '0.5rem'
                }}>
                  Your total amount to be financed will be
                </label>
                <div style={{ position: 'relative' }}>
                  <span style={{
                    position: 'absolute',
                    left: '12px',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    color: 'var(--text-secondary)',
                    fontSize: '1rem'
                  }}>$</span>
                  <input
                    type="text"
                    value={results.totalFinanced.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                    readOnly
                    className="formInput"
                    style={{
                      paddingLeft: '2rem',
                      backgroundColor: 'var(--bg-secondary)',
                      color: 'var(--text-secondary)'
                    }}
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Calculate Button */}
          <div style={{ textAlign: 'center', marginBottom: '2rem' }}>
            <button
              onClick={calculatePayments}
              style={{
                background: currentTheme === 'dark'
                  ? 'var(--accent-primary)'
                  : 'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)',
                color: 'white',
                border: 'none',
                padding: '12px 48px',
                borderRadius: '6px',
                fontSize: '1rem',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                boxShadow: currentTheme === 'dark'
                  ? 'var(--card-shadow)'
                  : '0 2px 8px rgba(220, 38, 38, 0.3)',
                textTransform: 'uppercase',
                letterSpacing: '0.5px'
              }}
              onMouseOver={(e) => {
                if (currentTheme === 'dark') {
                  e.target.style.background = 'var(--accent-hover)';
                  e.target.style.transform = 'translateY(-1px)';
                  e.target.style.boxShadow = 'var(--card-shadow-hover)';
                } else {
                  e.target.style.background = 'linear-gradient(135deg, #b91c1c 0%, #991b1b 100%)';
                  e.target.style.transform = 'translateY(-1px)';
                  e.target.style.boxShadow = '0 4px 12px rgba(220, 38, 38, 0.4)';
                }
              }}
              onMouseOut={(e) => {
                if (currentTheme === 'dark') {
                  e.target.style.background = 'var(--accent-primary)';
                  e.target.style.transform = 'translateY(0)';
                  e.target.style.boxShadow = 'var(--card-shadow)';
                } else {
                  e.target.style.background = 'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)';
                  e.target.style.transform = 'translateY(0)';
                  e.target.style.boxShadow = '0 2px 8px rgba(220, 38, 38, 0.3)';
                }
              }}
            >
              Calculate Payment
            </button>
          </div>
        </div>

        {/* Results Table */}
        <div style={{
          background: 'var(--bg-secondary)',
          border: '1px solid var(--border-primary)',
          borderRadius: '12px',
          overflow: 'hidden',
          boxShadow: 'var(--card-shadow)'
        }}>
          <div style={{
            display: 'grid',
            gridTemplateColumns: '1fr 1fr 1fr',
            backgroundColor: 'var(--bg-tertiary)',
            borderBottom: '1px solid var(--border-primary)'
          }}>
            <div style={{
              padding: '1rem',
              fontWeight: '600',
              color: 'var(--text-primary)',
              borderRight: '1px solid var(--border-primary)',
              textAlign: 'center'
            }}>
              Payment Frequency
            </div>
            <div style={{
              padding: '1rem',
              fontWeight: '600',
              color: 'var(--text-primary)',
              borderRight: '1px solid var(--border-primary)',
              textAlign: 'center'
            }}>
              Payment Amount
            </div>
            <div style={{
              padding: '1rem',
              fontWeight: '600',
              color: 'var(--text-primary)',
              textAlign: 'center'
            }}>
              Total Interest to be Paid over the Duration of the Loan
            </div>
          </div>

          {/* Monthly Row */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: '1fr 1fr 1fr',
            borderBottom: '1px solid var(--border-primary)'
          }}>
            <div style={{
              padding: '1rem',
              color: 'var(--text-primary)',
              borderRight: '1px solid var(--border-primary)',
              fontWeight: '500'
            }}>
              Monthly
            </div>
            <div style={{
              padding: '1rem',
              color: 'var(--text-primary)',
              borderRight: '1px solid var(--border-primary)',
              textAlign: 'center',
              fontWeight: '600'
            }}>
              ${results.monthlyPayment.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
            </div>
            <div style={{
              padding: '1rem',
              color: 'var(--text-primary)',
              textAlign: 'center',
              fontWeight: '600'
            }}>
              ${results.totalInterestMonthly.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
            </div>
          </div>

          {/* Bi-weekly Row */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: '1fr 1fr 1fr',
            borderBottom: '1px solid var(--border-primary)'
          }}>
            <div style={{
              padding: '1rem',
              color: 'var(--text-primary)',
              borderRight: '1px solid var(--border-primary)',
              fontWeight: '500'
            }}>
              Bi-weekly
            </div>
            <div style={{
              padding: '1rem',
              color: 'var(--text-primary)',
              borderRight: '1px solid var(--border-primary)',
              textAlign: 'center',
              fontWeight: '600'
            }}>
              ${results.biWeeklyPayment.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
            </div>
            <div style={{
              padding: '1rem',
              color: 'var(--text-primary)',
              textAlign: 'center',
              fontWeight: '600'
            }}>
              ${results.totalInterestBiWeekly.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
            </div>
          </div>

          {/* Weekly Row */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: '1fr 1fr 1fr'
          }}>
            <div style={{
              padding: '1rem',
              color: 'var(--text-primary)',
              borderRight: '1px solid var(--border-primary)',
              fontWeight: '500'
            }}>
              Weekly
            </div>
            <div style={{
              padding: '1rem',
              color: 'var(--text-primary)',
              borderRight: '1px solid var(--border-primary)',
              textAlign: 'center',
              fontWeight: '600'
            }}>
              ${results.weeklyPayment.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
            </div>
            <div style={{
              padding: '1rem',
              color: 'var(--text-primary)',
              textAlign: 'center',
              fontWeight: '600'
            }}>
              ${results.totalInterestWeekly.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
            </div>
          </div>
        </div>

        {/* Apply Now Section */}
        <div style={{ textAlign: 'center', marginTop: '2rem' }}>
          <p style={{
            color: 'var(--text-secondary)',
            marginBottom: '1rem',
            fontSize: '1rem'
          }}>
            Ready to apply for financing?
          </p>
          <button
            onClick={() => window.location.href = '/financing-application'}
            style={{
              background: 'var(--accent-primary)',
              color: 'white',
              border: 'none',
              padding: '12px 32px',
              borderRadius: '8px',
              fontSize: '1rem',
              fontWeight: '500',
              cursor: 'pointer',
              transition: 'all 0.2s ease',
              boxShadow: 'var(--card-shadow)'
            }}
            onMouseOver={(e) => {
              e.target.style.background = 'var(--accent-hover)';
              e.target.style.transform = 'translateY(-1px)';
              e.target.style.boxShadow = 'var(--card-shadow-hover)';
            }}
            onMouseOut={(e) => {
              e.target.style.background = 'var(--accent-primary)';
              e.target.style.transform = 'translateY(0)';
              e.target.style.boxShadow = 'var(--card-shadow)';
            }}
          >
            Apply Now
          </button>
        </div>
      </div>
    </div>
  );
}
