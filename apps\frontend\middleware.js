import { NextResponse } from 'next/server';

/**
 * Global Next.js Middleware
 * 
 * This middleware runs on every request and can be used to:
 * - Track visitors automatically
 * - Handle authentication
 * - Redirect users
 * - Add security headers
 * 
 * For FazeNAuto, we use it primarily for visitor tracking
 */

export async function middleware(request) {
  const { pathname } = request.nextUrl;
  
  // Skip tracking for certain paths to avoid unnecessary processing
  const skipPaths = [
    '/api/',           // API routes (except visitor tracking)
    '/_next/',         // Next.js internal files
    '/favicon.ico',    // Favicon requests
    '/robots.txt',     // Robots file
    '/sitemap.xml',    // Sitemap
    '/manifest.json'   // PWA manifest
  ];
  
  // Check if we should skip tracking for this path
  const shouldSkip = skipPaths.some(path => pathname.startsWith(path));
  
  // Always skip API routes except for specific ones we want to track
  const isApiRoute = pathname.startsWith('/api/');
  const trackableApiRoutes = ['/api/vehicles', '/api/auth'];
  const shouldTrackApi = trackableApiRoutes.some(route => pathname.startsWith(route));
  
  if (!shouldSkip && (!isApiRoute || shouldTrackApi)) {
    try {
      // Track visitor by calling the API endpoint asynchronously
      fetch(new URL('/api/visitors', request.url), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-forwarded-for': request.headers.get('x-forwarded-for') || '',
          'x-real-ip': request.headers.get('x-real-ip') || '',
          'user-agent': request.headers.get('user-agent') || '',
          'referer': request.headers.get('referer') || ''
        }
      }).catch(error => {
        console.error('Background visitor tracking failed:', error);
      });
    } catch (error) {
      // Don't fail the request if tracking fails
      console.error('Visitor tracking middleware error:', error);
    }
  }
  
  // Continue with the request
  return NextResponse.next();
}

/**
 * Configure which paths this middleware should run on
 * 
 * We want to track most pages but exclude static assets and internal Next.js files
 */
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
};
