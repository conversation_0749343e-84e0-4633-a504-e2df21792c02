/* Home Page Styles */

.homePage {
  min-height: 100vh;
  background: var(--bg-primary);
  transition: background 0.3s ease;
}

/* Dark mode: Apply consistent gradient background to eliminate horizontal bands */
[data-theme="dark"] .homePage {
  background: var(--bg-primary);
}

/* Hero Section */
.heroSection {
  padding: 4rem 1rem;
  min-height: 80vh;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #2d3748 0%, #4a5568 50%, #718096 100%);
  position: relative;
  overflow: hidden;
}

.heroContent {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  position: relative;
  z-index: 3;
  min-height: 60vh;
}

.heroText {
  color: white;
  max-width: 100%;
  text-align: center;
}

.heroTitle {
  font-size: 4rem;
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  color: white;
}

.brandName {
  position: relative;
}

.brandFaze {
  color: white;
}

.brandN {
  color: #e53e3e;
}

.brandAuto {
  color: white;
}

/* Light mode brand text styling for About section */
[data-theme="light"] .aboutSection .brandFaze,
:root .aboutSection .brandFaze {
  color: #2d3748;
}

[data-theme="light"] .aboutSection .brandAuto,
:root .aboutSection .brandAuto {
  color: #2d3748;
}

/* Dark mode brand text styling for About section */
[data-theme="dark"] .aboutSection .brandFaze {
  color: white;
}

[data-theme="dark"] .aboutSection .brandAuto {
  color: white;
}

.heroSubtitle {
  font-size: 1.3rem;
  line-height: 1.6;
  margin-bottom: 2.5rem;
  color: #e2e8f0;
  opacity: 0.95;
}

.heroButtons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
}

.primaryBtn {
  background: linear-gradient(135deg, #e53e3e 0%, #fc8181 100%);
  color: white;
  padding: 1rem 2rem;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(229, 62, 62, 0.3);
  border: none;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  gap: 0.5rem;
}

.primaryBtn:hover {
  background: linear-gradient(135deg, #c53030 0%, #e53e3e 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(229, 62, 62, 0.4);
}

.secondaryBtn {
  background: transparent;
  color: white;
  padding: 1rem 2rem;
  border: 2px solid white;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  gap: 0.5rem;
}

.secondaryBtn:hover {
  background: white;
  color: #2d3748;
  border-color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(255, 255, 255, 0.2);
}



/* Stats Section */
.statsSection {
  padding: 3rem 1rem;
  background: white;
  transition: background 0.3s ease;
}

/* Dark mode: Stats section should use transparent background (main container provides gradient) */
[data-theme="dark"] .statsSection {
  background: transparent;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.statCard {
  text-align: center;
  padding: 2rem;
  background: var(--bg-secondary, linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%));
  border-radius: 12px;
  box-shadow: var(--card-shadow, 0 4px 15px rgba(0, 0, 0, 0.1));
  transition: transform 0.3s ease;
  border: 1px solid var(--border-primary, transparent);
}

.statCard:hover {
  transform: translateY(-5px);
  box-shadow: var(--card-shadow-hover, 0 6px 20px rgba(0, 0, 0, 0.15));
}

.statNumber {
  font-size: 3rem;
  font-weight: 700;
  color: var(--text-primary, #2d3748);
  margin-bottom: 0.5rem;
}

.statLabel {
  font-size: 1.1rem;
  color: var(--text-secondary, #4a5568);
  font-weight: 500;
}

/* Featured Section */
.featuredSection {
  padding: 4rem 1rem;
  background: var(--bg-secondary, #f8f9fa);
  transition: background 0.3s ease;
}

/* Light mode: Featured Vehicles section should be pure white */
[data-theme="light"] .featuredSection,
:root .featuredSection {
  background: var(--bg-primary, #ffffff);
}

/* Dark mode: Featured section should use transparent background (main container provides gradient) */
[data-theme="dark"] .featuredSection {
  background: transparent;
}

.sectionHeader {
  text-align: center;
  margin-bottom: 3rem;
}

.sectionTitle {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary, #2d3748);
  margin-bottom: 1rem;
}

.sectionSubtitle {
  font-size: 1.2rem;
  color: var(--text-secondary, #4a5568);
  max-width: 600px;
  margin: 0 auto;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #e2e8f0;
  border-top: 4px solid #e53e3e;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.vehicleGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.vehicleCard {
  background: var(--bg-primary, white);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: var(--card-shadow, 0 4px 15px rgba(0, 0, 0, 0.1));
  transition: all 0.3s ease;
  border: 1px solid var(--border-primary, transparent);
}

/* Dark mode styling for vehicle cards - match Services page style */
[data-theme="dark"] .vehicleCard {
  background: #0d1117;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.vehicleCard:hover {
  transform: translateY(-5px);
  box-shadow: var(--card-shadow-hover, 0 8px 25px rgba(0, 0, 0, 0.15));
}

.vehicleImageWrapper {
  position: relative;
  height: 250px;
  overflow: hidden;
  border-radius: 12px 12px 0 0;
}

.vehicleImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  transition: transform 0.3s ease;
}

.vehicleCard:hover .vehicleImage {
  transform: scale(1.05);
}

/* Compare Button */
.compareBtn {
  position: absolute;
  top: 10px;
  left: 10px;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: 2px solid white;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 2;
}

.compareBtn:hover {
  background: rgba(0, 0, 0, 0.9);
  transform: scale(1.1);
}

.compareBtnActive {
  background: var(--accent-secondary, #10b981);
  border-color: var(--accent-secondary, #10b981);
}

.compareBtnActive:hover {
  background: #0d9488;
  border-color: #0d9488;
}

.compareBtnDisabled {
  background: rgba(128, 128, 128, 0.7);
  cursor: not-allowed;
  opacity: 0.6;
}

.compareBtnDisabled:hover {
  transform: none;
  background: rgba(128, 128, 128, 0.7);
}

/* Remove the old priceTag styles since we're moving price to bottom */

.vehicleInfo {
  padding: 1.5rem;
}

.vehicleTitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary, #2d3748);
  margin-bottom: 1rem;
}

.vehicleSpecs {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  text-align: left;
}

.spec {
  background: var(--bg-tertiary, #f7fafc);
  color: var(--text-secondary, #4a5568);
  padding: 0.5rem 0.75rem;
  border-radius: 4px;
  font-size: 0.85rem;
  font-weight: 500;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Price Section */
.priceSection {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: var(--bg-tertiary, linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%));
  border-radius: 8px;
  text-align: center;
}

.price {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary, #2d3748);
  margin-bottom: 0.25rem;
}

.estimatedPayment {
  font-size: 0.9rem;
  color: var(--text-tertiary, #718096);
  font-weight: 500;
}

/* Card Buttons */
.cardButtons {
  display: flex;
  gap: 0.75rem;
}

.viewDetailsBtn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  text-decoration: none;
  flex: 1;
  text-align: center;
}

.contactBtn {
  border: none;
  padding: 0.75rem 1.5rem;
  flex: 1;
}

.sectionFooter {
  text-align: center;
}

.viewAllBtn {
  display: inline-block;
  background: transparent;
  color: #e53e3e;
  padding: 1rem 2rem;
  border: 2px solid #e53e3e;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

.viewAllBtn:hover {
  background: #e53e3e;
  color: white;
  transform: translateY(-2px);
}

/* Services Section */
.servicesSection {
  padding: 4rem 1rem;
  background: var(--bg-primary, white);
  transition: background 0.3s ease;
}

/* Dark mode: Services section should use transparent background (main container provides gradient) */
[data-theme="dark"] .servicesSection {
  background: transparent;
}

.servicesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.serviceCard {
  text-align: center;
  padding: 2.5rem 2rem;
  background: var(--bg-secondary, linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%));
  border-radius: 12px;
  box-shadow: var(--card-shadow, 0 4px 15px rgba(0, 0, 0, 0.1));
  transition: all 0.3s ease;
  border: 1px solid var(--border-primary, transparent);
}

/* Dark mode: Service cards should use primary background for contrast */
[data-theme="dark"] .serviceCard {
  background: var(--bg-primary);
}

.serviceCard:hover {
  transform: translateY(-5px);
  box-shadow: var(--card-shadow-hover, 0 8px 25px rgba(0, 0, 0, 0.15));
}

.serviceIcon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
  display: block;
}

.serviceTitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary, #2d3748);
  margin-bottom: 1rem;
}

.serviceDescription {
  color: var(--text-secondary, #4a5568);
  line-height: 1.6;
  font-size: 1rem;
}

/* About Section */
.aboutSection {
  padding: 4rem 1rem;
  background: var(--bg-primary, white);
  color: var(--text-primary, #2d3748);
  position: relative;
  transition: background 0.3s ease;
}

/* Dark mode: About section should use transparent background (main container provides gradient) */
[data-theme="dark"] .aboutSection {
  background: transparent;
  color: var(--text-primary);
}

/* Remove gradient overlay in light mode, keep for dark mode */
[data-theme="dark"] .aboutSection::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: linear-gradient(to bottom, rgba(26, 32, 44, 0) 0%, rgba(26, 32, 44, 0.3) 100%);
  pointer-events: none;
}

/* Light mode: no overlay */
[data-theme="light"] .aboutSection::after,
:root .aboutSection::after {
  display: none;
}

.aboutContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.aboutText .sectionTitle {
  color: var(--text-primary, #2d3748);
  text-align: center;
  margin-bottom: 2rem;
}

/* Dark mode specific styling for about title */
[data-theme="dark"] .aboutText .sectionTitle {
  color: white;
}

.aboutDescription {
  font-size: 1.1rem;
  line-height: 1.7;
  margin-bottom: 1.5rem;
  color: var(--text-secondary, #4a5568);
}

/* Dark mode specific styling for about description */
[data-theme="dark"] .aboutDescription {
  color: white;
  opacity: 0.95;
}

.aboutFeatures {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 2rem;
}

.feature {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--text-primary, #2d3748);
}

/* Dark mode specific styling for features */
[data-theme="dark"] .feature {
  color: var(--text-primary);
}

.featureIcon {
  background: #48bb78;
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
  font-weight: bold;
}

.aboutImage {
  display: flex;
  justify-content: center;
  align-items: center;
}

.aboutImagePlaceholder {
  font-size: 12rem;
  opacity: 0.8;
}

/* CTA Section */
.ctaSection {
  padding: 4rem 1rem;
  background: var(--bg-secondary, #f8f9fa);
  color: var(--text-primary, #2d3748);
  text-align: center;
  position: relative;
  margin-top: -30px;
  padding-top: 5rem;
  transition: background 0.3s ease;
}

/* Dark mode: CTA section should use transparent background (main container provides gradient) */
[data-theme="dark"] .ctaSection {
  background: transparent;
  color: var(--text-primary);
}

/* Light mode: Remove card-like appearance for seamless look */
[data-theme="light"] .ctaSection,
:root .ctaSection {
  background: transparent;
  box-shadow: none;
  border: none;
}

/* Remove gradient overlay in light mode for seamless appearance */
[data-theme="dark"] .ctaSection::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: linear-gradient(to bottom, rgba(15, 20, 25, 0) 0%, rgba(15, 20, 25, 0.4) 100%);
  pointer-events: none;
}

/* Light mode: no overlay for seamless look */
[data-theme="light"] .ctaSection::after,
:root .ctaSection::after {
  display: none;
}

.ctaContent {
  max-width: 800px;
  margin: 0 auto;
}

.ctaTitle {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--text-primary, #2d3748);
}

/* Dark mode: Ensure CTA title uses proper text color */
[data-theme="dark"] .ctaTitle {
  color: var(--text-primary);
}

.ctaSubtitle {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  color: var(--text-secondary, #4a5568);
}

/* Dark mode specific styling for CTA text */
[data-theme="dark"] .ctaTitle {
  color: white;
}

[data-theme="dark"] .ctaSubtitle {
  color: white;
  opacity: 0.9;
}

.ctaButtons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .heroContent {
    flex-direction: column;
    text-align: center;
    padding: 2rem 1rem;
  }

  .heroText {
    max-width: 100%;
  }

  .heroTitle {
    font-size: 2.5rem;
  }

  .heroButtons {
    justify-content: center;
  }

  .statsGrid {
    grid-template-columns: 1fr;
  }

  .vehicleGrid {
    grid-template-columns: 1fr;
  }

  .vehicleSpecs {
    justify-content: center;
  }

  .servicesGrid {
    grid-template-columns: 1fr;
  }

  .aboutContent {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 2rem;
  }

  .aboutText .sectionTitle {
    text-align: center;
  }

  .ctaTitle {
    font-size: 2rem;
  }

  .ctaButtons {
    flex-direction: column;
    align-items: center;
  }

  .ctaButtons .primaryBtn,
  .ctaButtons .secondaryBtn {
    width: 100%;
    max-width: 300px;
  }
}

/* RTL Support for Home Page */
[dir="rtl"] .homePage {
  direction: rtl;
  text-align: right;
}

[dir="rtl"] .heroText {
  text-align: center; /* Keep hero text centered for all languages */
}

[dir="rtl"] .heroButtons {
  flex-direction: row-reverse;
}

[dir="rtl"] .statsGrid {
  direction: rtl;
}

[dir="rtl"] .vehicleSpecs {
  flex-direction: row-reverse;
}

[dir="rtl"] .servicesGrid {
  direction: rtl;
}

[dir="rtl"] .serviceCard {
  text-align: right;
}

[dir="rtl"] .aboutContent {
  direction: rtl;
}

[dir="rtl"] .aboutText {
  text-align: right;
}

[dir="rtl"] .aboutFeatures {
  direction: rtl;
}

[dir="rtl"] .feature {
  flex-direction: row-reverse;
  text-align: right;
}

[dir="rtl"] .featureIcon {
  margin-left: 0.75rem;
  margin-right: 0;
}

[dir="rtl"] .ctaButtons {
  flex-direction: row-reverse;
}
