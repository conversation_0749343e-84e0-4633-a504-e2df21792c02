'use client';
import { useState, useEffect, Suspense } from 'react';
import styles from './ComplianceForms.module.css';
import ComplianceFormsPanel from '../../../components/ComplianceFormsPanel/ComplianceFormsPanel';

// Force dynamic rendering to avoid build-time issues
export const dynamic = 'force-dynamic';

export default function ComplianceFormsPage() {
  const [vehicles, setVehicles] = useState([]);
  const [selectedVehicles, setSelectedVehicles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchVehicles = async () => {
    try {
      setLoading(true);
      const res = await fetch('/api/vehicles');
      const data = await res.json();

      if (data.success) {
        setVehicles(data.data || []);
      } else {
        throw new Error(data.error || 'Failed to fetch vehicles');
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchVehicles();
  }, []);

  return (
    <Suspense fallback={<div>Loading...</div>}>
      <div className={styles.complianceFormsPage}>
        <div className={styles.header}>
        </div>

        <ComplianceFormsPanel
          selectedVehicles={selectedVehicles}
          onRefresh={fetchVehicles}
          vehicles={vehicles}
          onVehicleSelect={setSelectedVehicles}
        />
      </div>
    </Suspense>
  );
}
