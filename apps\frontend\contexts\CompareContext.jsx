'use client';

import { createContext, useContext, useState, useEffect, useCallback, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';

const CompareContext = createContext();

// Separate component to handle useSearchParams with Suspense
function CompareProviderWithSearchParams({ children }) {
  const [selectedVehicles, setSelectedVehicles] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();
  const searchParams = useSearchParams();

  // Load initial state from localStorage and URL params
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Check URL params first (for shared comparisons)
      const urlIds = searchParams.get('ids');
      if (urlIds) {
        const vehicleIds = urlIds.split(',').filter(id => id.trim());
        setSelectedVehicles(vehicleIds);
        // Also save to localStorage for persistence
        localStorage.setItem('compareVehicles', JSON.stringify(vehicleIds));
      } else {
        // Load from localStorage
        const saved = localStorage.getItem('compareVehicles');
        if (saved) {
          try {
            const vehicleIds = JSON.parse(saved);
            setSelectedVehicles(Array.isArray(vehicleIds) ? vehicleIds : []);
          } catch (error) {
            console.error('Error parsing saved comparison vehicles:', error);
            setSelectedVehicles([]);
          }
        }
      }
    }
    setIsLoading(false);
  }, [searchParams]);

  // Save to localStorage whenever selectedVehicles changes
  useEffect(() => {
    if (typeof window !== 'undefined' && !isLoading) {
      localStorage.setItem('compareVehicles', JSON.stringify(selectedVehicles));
    }
  }, [selectedVehicles, isLoading]);

  // Add vehicle to comparison
  const addToCompare = useCallback((vehicleId) => {
    setSelectedVehicles(prev => {
      if (prev.includes(vehicleId)) {
        return prev; // Already in comparison
      }
      if (prev.length >= 4) {
        // Limit to 4 vehicles for comparison
        return prev;
      }
      return [...prev, vehicleId];
    });
  }, []);

  // Remove vehicle from comparison
  const removeFromCompare = useCallback((vehicleId) => {
    setSelectedVehicles(prev => prev.filter(id => id !== vehicleId));
  }, []);

  // Check if vehicle is in comparison
  const isInCompare = useCallback((vehicleId) => {
    return selectedVehicles.includes(vehicleId);
  }, [selectedVehicles]);

  // Clear all comparisons
  const clearCompare = useCallback(() => {
    setSelectedVehicles([]);
  }, []);

  // Generate shareable URL
  const getShareableUrl = useCallback(() => {
    if (selectedVehicles.length === 0) return null;
    const baseUrl = typeof window !== 'undefined' ? window.location.origin : '';
    return `${baseUrl}/compare?ids=${selectedVehicles.join(',')}`;
  }, [selectedVehicles]);

  // Navigate to compare page
  const goToCompare = useCallback(() => {
    if (selectedVehicles.length > 0) {
      router.push(`/compare?ids=${selectedVehicles.join(',')}`);
    } else {
      router.push('/compare');
    }
  }, [selectedVehicles, router]);

  const value = {
    selectedVehicles,
    isLoading,
    addToCompare,
    removeFromCompare,
    isInCompare,
    clearCompare,
    getShareableUrl,
    goToCompare,
    compareCount: selectedVehicles.length,
    canAddMore: selectedVehicles.length < 4,
    hasComparisons: selectedVehicles.length > 0,
    canCompare: selectedVehicles.length >= 2
  };

  return (
    <CompareContext.Provider value={value}>
      {children}
    </CompareContext.Provider>
  );
}

// Main CompareProvider that wraps the search params logic in Suspense
export function CompareProvider({ children }) {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <CompareProviderWithSearchParams>
        {children}
      </CompareProviderWithSearchParams>
    </Suspense>
  );
}

export function useCompare() {
  const context = useContext(CompareContext);
  if (context === undefined) {
    throw new Error('useCompare must be used within a CompareProvider');
  }
  return context;
}

export default CompareContext;
