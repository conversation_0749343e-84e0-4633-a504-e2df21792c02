# Dark Mode & Multi-Language Implementation

## 🌟 Overview

This implementation provides a complete dark mode and multi-language system for the FazeNAuto Next.js application with Google Analytics 4 integration.

## ✅ Features Implemented

- ✅ Dark/Light mode toggle with system preference detection
- ✅ Multi-language support (English, Spanish, French)
- ✅ Persistent user preferences (localStorage)
- ✅ CSS variables for seamless theming
- ✅ Google Analytics 4 tracking (G-JBNQD8SDQ1)
- ✅ SSR-safe implementation
- ✅ Responsive design considerations
- ✅ Footer integration for theme/language controls

## 📁 File Structure

```
apps/frontend/
├── contexts/
│   ├── ThemeContext.jsx          # Theme management context
│   └── LanguageContext.jsx       # Language management context
├── components/
│   ├── ThemeToggle/
│   │   ├── ThemeToggle.jsx       # Theme toggle component
│   │   └── ThemeToggle.module.css
│   ├── LanguageSelector/
│   │   ├── LanguageSelector.jsx  # Language selector component
│   │   └── LanguageSelector.module.css
│   ├── Settings/
│   │   ├── Settings.jsx          # Settings page component
│   │   └── Settings.module.css
│   └── GoogleAnalytics/
│       └── GoogleAnalytics.jsx   # GA4 component
├── lib/
│   ├── translations.js           # Translation utilities
│   └── analytics.js              # GA4 utilities
├── locales/
│   ├── en/translation.json       # English translations
│   ├── es/translation.json       # Spanish translations
│   └── fr/translation.json       # French translations
├── app/
│   ├── layout.jsx                # Root layout with providers
│   ├── globals.css               # Global styles with CSS variables
│   ├── settings/page.jsx         # Settings page
│   └── demo/page.jsx             # Demo page
```

## 🎨 CSS Variables

The implementation uses CSS variables for theming:

```css
:root {
  /* Light theme */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --accent-primary: #3b82f6;
  /* ... more variables */
}

[data-theme="dark"] {
  /* Dark theme */
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  /* ... more variables */
}
```

## 🚀 Usage

### Theme Toggle
```jsx
import ThemeToggle from '../components/ThemeToggle/ThemeToggle';

<ThemeToggle showLabel={true} size="medium" />
```

### Language Selector
```jsx
import LanguageSelector from '../components/LanguageSelector/LanguageSelector';

<LanguageSelector showLabel={true} showFlags={true} variant="select" />
```

### Using Translations
```jsx
import { useLanguage } from '../contexts/LanguageContext';

function MyComponent() {
  const { t } = useLanguage();

  return (
    <div>
      <h1>{t('nav.about', 'About')}</h1>
      <p>{t('dashboard.title', 'Dashboard')}</p>
      <button>{t('common.save', 'Save')}</button>
    </div>
  );
}
```

### Analytics Tracking
```jsx
import { trackEvent, trackVehicleView } from '../lib/analytics';

// Track custom events
trackEvent('button_click', {
  category: 'engagement',
  label: 'Header CTA'
});

// Track vehicle interactions
trackVehicleView({
  id: 'vehicle-123',
  make: 'Toyota',
  model: 'Camry',
  year: 2020,
  price: 25000
});
```

## 🔧 Configuration

### Google Analytics 4
- Measurement ID: `G-JBNQD8SDQ1`
- Automatically tracks page views and route changes
- Custom event tracking available via utility functions

### Supported Languages
- English (en) 🇺🇸
- Spanish (es) 🇪🇸
- French (fr) 🇫🇷

### Translation Structure
Translations use dot notation for keys:
- `nav.*` - Navigation items
- `settings.*` - Settings page content
- `dashboard.*` - Dashboard content
- `common.*` - Common UI elements
- `admin.*` - Admin panel content

### Theme Options
- Light mode
- Dark mode
- System preference detection

## 📱 Integration Points

### Footer Integration
Theme toggle and language selector are integrated into the footer:
- Desktop: Horizontal layout in footer bottom
- Mobile: Vertical stacked layout

### Persistent Storage
User preferences are stored in `localStorage` under the `userSettings` key:
```json
{
  "preferences": {
    "theme": "dark",
    "language": "es"
  }
}
```

## 🎯 Demo Pages

- `/settings` - Settings page with theme and language controls
- `/demo` - Comprehensive demo showcasing all features

## 🔍 Testing

1. **Theme Switching**: Toggle between light/dark modes
2. **Language Switching**: Change languages and verify translations
3. **Persistence**: Refresh page and verify settings persist
4. **Analytics**: Check browser dev tools Network tab for GA4 requests
5. **Responsive**: Test on different screen sizes

## 🚨 Important Notes

- Theme controls are in the **footer**, not navbar
- All components use CSS variables for theming
- Translations are cached for performance
- GA4 tracking is automatic for page views
- SSR-safe implementation prevents hydration mismatches

## 🔧 Troubleshooting

### Common Issues
1. **Hydration Mismatch**: Theme detection runs client-side only
2. **Translation Loading**: Fallbacks provided for missing translations
3. **Analytics Not Working**: Check GA4 measurement ID and network requests

### Performance Tips
1. Translations are cached after first load
2. CSS variables provide smooth theme transitions
3. Components use React.memo where appropriate
