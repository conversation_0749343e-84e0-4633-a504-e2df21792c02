'use client';
import { useEffect, useState } from 'react';
import Link from 'next/link';
import styles from './Home.module.css';
import useVisitorTracking from '../hooks/useVisitorTracking';
import { useLanguage } from '../contexts/LanguageContext';
import { useCompare } from '../contexts/CompareContext';
import BrowseByMake from '../components/BrowseByMake/BrowseByMake';

export default function Home() {
  const [featuredVehicles, setFeaturedVehicles] = useState([]);
  const [loading, setLoading] = useState(true);
  const { t } = useLanguage();
  const { addToCompare, removeFromCompare, isInCompare, canAddMore } = useCompare();

  // Track visitor
  useVisitorTracking();

  useEffect(() => {
    const fetchFeaturedVehicles = async () => {
      try {
        const response = await fetch('/api/vehicles?status=active');
        const data = await response.json();
        if (data.success) {
          // Get first 3 vehicles for featured section
          setFeaturedVehicles(data.data.slice(0, 3));
        }
      } catch (error) {
        console.error('Error fetching vehicles:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchFeaturedVehicles();
  }, []);

  return (
    <div className={styles.homePage}>
      {/* Hero Section */}
      <section className={styles.heroSection}>
        <div className={styles.heroContent}>
          <div className={styles.heroText}>
            <h1 className={styles.heroTitle}>
              {t('home.hero.title')} <span className={styles.brandName}>
                <span className={styles.brandFaze}>Faze</span>
                <span className={styles.brandN}>N</span>
                <span className={styles.brandAuto}>Auto</span>
              </span>
            </h1>
            <p className={styles.heroSubtitle}>
              {t('home.hero.subtitle')}
            </p>
            <div className={styles.heroButtons}>
              <Link href="/inventory/used-cars" className={styles.primaryBtn}>
                {t('home.hero.browse_inventory')}
              </Link>
              <Link href="/about-us" className={styles.secondaryBtn}>
                {t('home.hero.learn_more')}
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Browse by Make Section */}
      <BrowseByMake />

      {/* Featured Vehicles Section */}
      <section className={styles.featuredSection}>
        <div className={styles.container}>
          <div className={styles.sectionHeader}>
            <h2 className={styles.sectionTitle}>{t('home.featured.title')}</h2>
            <p className={styles.sectionSubtitle}>
              {t('home.featured.subtitle')}
            </p>
          </div>

          {loading ? (
            <div className={styles.loading}>
              <div className={styles.spinner}></div>
              <p>{t('home.featured.loading')}</p>
            </div>
          ) : (
            <>
              <div className={styles.vehicleGrid}>
                {featuredVehicles.map((vehicle) => {
                  // Get the first image from images array or fall back to imageUrl
                  const primaryImage = (vehicle.images && vehicle.images.length > 0)
                    ? vehicle.images[0]
                    : vehicle.imageUrl;

                  const vehicleId = vehicle._id;
                  const isInComparison = isInCompare(vehicleId);

                  const handleCompareClick = () => {
                    if (isInComparison) {
                      removeFromCompare(vehicleId);
                    } else if (canAddMore) {
                      addToCompare(vehicleId);
                    }
                  };

                  return (
                    <div key={vehicle._id} className={styles.vehicleCard}>
                      <div className={styles.vehicleImageWrapper}>
                        <img
                          src={primaryImage}
                          alt={`${vehicle.year} ${vehicle.make} ${vehicle.model}`}
                          className={styles.vehicleImage}
                        />

                        {/* Compare Button */}
                        <button
                          className={`${styles.compareBtn} ${isInComparison ? styles.compareBtnActive : ''} ${!canAddMore && !isInComparison ? styles.compareBtnDisabled : ''}`}
                          onClick={handleCompareClick}
                          disabled={!canAddMore && !isInComparison}
                          title={isInComparison ? 'Remove from comparison' : canAddMore ? 'Add to comparison' : 'Maximum 4 vehicles can be compared'}
                        >
                          {isInComparison ? '✓' : '+'}
                        </button>
                      </div>
                      <div className={styles.vehicleInfo}>
                        <h3 className={styles.vehicleTitle}>
                          {vehicle.year} {vehicle.make} {vehicle.model}
                        </h3>
                        <div className={styles.vehicleSpecs}>
                          <span className={styles.spec}>
                            {vehicle.mileage ? `${vehicle.mileage.toLocaleString()} ${t('home.featured.km')}` : t('home.featured.na')}
                          </span>
                          <span className={styles.spec}>{t('home.featured.engine')} {vehicle.engine || t('home.featured.na')}</span>
                          <span className={styles.spec}>{t('home.featured.transmission')} {vehicle.transmission || t('home.featured.na')}</span>
                          <span className={styles.spec}>{t('home.featured.fuel_type')} {vehicle.fuelType || t('home.featured.na')}</span>
                          <span className={styles.spec}>{t('home.featured.doors')} {vehicle.doors || t('home.featured.na')}</span>
                          <span className={styles.spec}>{t('home.featured.driveline')} {vehicle.drivetrain || t('home.featured.na')}</span>
                        </div>
                        <div className={styles.priceSection}>
                          <div className={styles.price}>
                            ${vehicle.price?.toLocaleString() || t('home.featured.na')} {t('home.featured.plus_tax')}
                          </div>
                          <div className={styles.estimatedPayment}>
                            {t('home.featured.est_payment')}
                          </div>
                        </div>
                        <div className={styles.cardButtons}>
                          <Link
                            href={`/vehicles/${vehicle._id}`}
                            className={`glossy-button view-btn ${styles.viewDetailsBtn}`}
                          >
                            {t('home.featured.view_details')}
                          </Link>
                          <button className={`glossy-button contact-btn ${styles.contactBtn}`}>
                            {t('home.featured.contact_us')}
                          </button>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>

              <div className={styles.sectionFooter}>
                <Link href="/inventory/used-cars" className={styles.viewAllBtn}>
                  {t('home.featured.view_all')}
                </Link>
              </div>
            </>
          )}
        </div>
      </section>





      {/* CTA Section */}
      <section className={styles.ctaSection}>
        <div className={styles.container}>
          <div className={styles.ctaContent}>
            <h2 className={styles.ctaTitle}>{t('home.cta.title')}</h2>
            <p className={styles.ctaSubtitle}>
              {t('home.cta.subtitle')}
            </p>
            <div className={styles.ctaButtons}>
              <Link href="/inventory/used-cars" className={styles.primaryBtn}>
                {t('home.hero.browse_inventory')}
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
