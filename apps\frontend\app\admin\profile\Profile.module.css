.profilePage {
  padding: 2rem;
  background-color: var(--bg-secondary, #f9fafb);
  min-height: 100vh;
}

.header {
  margin-bottom: 2rem;
}

.header h1 {
  font-size: 2rem;
  font-weight: bold;
  color: var(--text-primary, #1f2937);
  margin-bottom: 0.5rem;
}

.header p {
  color: var(--text-secondary, #6b7280);
  font-size: 1.1rem;
}

.profileCard {
  background: var(--bg-primary, white);
  border-radius: 12px;
  box-shadow: var(--card-shadow, 0 4px 6px rgba(0, 0, 0, 0.05));
  padding: 2rem;
  max-width: 800px;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.section {
  border-bottom: 1px solid var(--border-primary, #e5e7eb);
  padding-bottom: 2rem;
}

.section:last-of-type {
  border-bottom: none;
  padding-bottom: 0;
}

.section h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary, #1f2937);
  margin-bottom: 1rem;
}

.formGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
}

.formGroup label {
  font-weight: 500;
  color: var(--text-primary, #374151);
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.formGroup input {
  padding: 0.75rem;
  border: 1px solid var(--border-primary, #d1d5db);
  border-radius: 6px;
  font-size: 1rem;
  background: var(--bg-primary);
  color: var(--text-primary);
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.formGroup input:focus {
  outline: none;
  border-color: var(--accent-primary, #3b82f6);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.message {
  padding: 1rem;
  border-radius: 6px;
  font-weight: 500;
  text-align: center;
}

.message.success {
  background-color: rgba(209, 250, 229, 0.8);
  color: var(--success, #065f46);
  border: 1px solid rgba(167, 243, 208, 0.8);
}

.message.error {
  background-color: var(--error-bg, #fee2e2);
  color: var(--error, #991b1b);
  border: 1px solid var(--error-border, #fca5a5);
}

.actions {
  display: flex;
  justify-content: flex-end;
  padding-top: 1rem;
}

.saveButton {
  background-color: var(--accent-primary, #3b82f6);
  color: white;
  padding: 0.75rem 2rem;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease, transform 0.1s ease;
}

.saveButton:hover:not(:disabled) {
  background-color: var(--accent-hover, #2563eb);
  transform: translateY(-1px);
}

.saveButton:disabled {
  background-color: var(--text-tertiary, #9ca3af);
  cursor: not-allowed;
  transform: none;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  color: var(--text-secondary, #6b7280);
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-primary, #e5e7eb);
  border-top: 3px solid var(--accent-primary, #3b82f6);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .profilePage {
    padding: 1rem;
  }

  .profileCard {
    padding: 1.5rem;
  }

  .header h1 {
    font-size: 1.5rem;
  }

  .formGrid {
    grid-template-columns: 1fr;
  }

  .actions {
    justify-content: stretch;
  }

  .saveButton {
    width: 100%;
  }
}
