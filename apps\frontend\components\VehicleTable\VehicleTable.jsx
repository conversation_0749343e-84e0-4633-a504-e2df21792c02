'use client';

import { useState, useMemo } from 'react';
import {
  useReactTable,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  getPaginationRowModel,
  flexRender,
} from '@tanstack/react-table';
import Link from 'next/link';
import styles from './VehicleTable.module.css';

export default function VehicleTable({
  vehicles,
  loading,
  error,
  filters,
  setFilters,
  onDelete,
  onRefresh,
  selectedVehicles = [],
  setSelectedVehicles = () => {},
  showSelection = false
}) {
  const [sorting, setSorting] = useState([]);
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showSoldModal, setShowSoldModal] = useState(false);
  const [showOnHoldModal, setShowOnHoldModal] = useState(false);
  const [showArchiveModal, setShowArchiveModal] = useState(false);
  const [showRemoveFromHoldModal, setShowRemoveFromHoldModal] = useState(false);
  const [selectedVehicle, setSelectedVehicle] = useState(null);
  const [actionLoading, setActionLoading] = useState(false);

  // Handle delete click
  const handleDeleteClick = (vehicle) => {
    setSelectedVehicle(vehicle);
    setShowDeleteModal(true);
  };

  // Handle sold click
  const handleSoldClick = (vehicle) => {
    setSelectedVehicle(vehicle);
    setShowSoldModal(true);
  };

  // Handle on hold click
  const handleOnHoldClick = (vehicle) => {
    setSelectedVehicle(vehicle);
    setShowOnHoldModal(true);
  };

  // Handle archive click
  const handleArchiveClick = (vehicle) => {
    setSelectedVehicle(vehicle);
    setShowArchiveModal(true);
  };

  // Handle remove from hold click
  const handleRemoveFromHoldClick = (vehicle) => {
    setSelectedVehicle(vehicle);
    setShowRemoveFromHoldModal(true);
  };

  // Confirm delete
  const confirmDelete = async () => {
    if (!selectedVehicle) return;

    setActionLoading(true);
    try {
      console.log('🗑️ Starting delete for vehicle:', selectedVehicle._id);

      // Get user authentication data
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      if (!user.email || !user.role) {
        alert('Authentication error. Please log in again.');
        return;
      }

      // Add timeout to delete request
      const deletePromise = fetch(`/api/vehicles/${selectedVehicle._id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userEmail: user.email,
          userRole: user.role
        }),
      });

      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Delete request timeout')), 30000) // 30 second timeout
      );

      const res = await Promise.race([deletePromise, timeoutPromise]);
      const responseData = await res.json();

      if (res.ok && responseData.success) {
        console.log('✅ Vehicle deleted successfully');
        onRefresh(); // Refresh the table
        setShowDeleteModal(false);
        setSelectedVehicle(null);
      } else {
        throw new Error(responseData.error || 'Failed to delete vehicle');
      }
    } catch (error) {
      console.error('❌ Delete failed:', error);
      alert('Error deleting vehicle: ' + error.message);
    } finally {
      setActionLoading(false);
    }
  };

  // Confirm sold
  const confirmSold = async () => {
    if (!selectedVehicle) return;

    setActionLoading(true);
    try {
      console.log('🔄 Updating vehicle status to sold:', selectedVehicle._id);

      // Get user authentication data
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      if (!user.email || !user.role) {
        alert('Authentication error. Please log in again.');
        return;
      }

      // Create FormData with authentication info
      const formData = new FormData();
      formData.append('userEmail', user.email);
      formData.append('userRole', user.role);
      formData.append('status', 'sold');

      const res = await fetch(`/api/vehicles/${selectedVehicle._id}`, {
        method: 'PUT',
        body: formData,
      });

      const responseData = await res.json();
      console.log('📝 API Response:', responseData);

      if (res.ok && responseData.success) {
        console.log('✅ Vehicle marked as sold successfully');
        onRefresh(); // Refresh the table
        setShowSoldModal(false);
        setSelectedVehicle(null);
      } else {
        throw new Error(responseData.error || 'Failed to mark vehicle as sold');
      }
    } catch (error) {
      console.error('❌ Mark as sold failed:', error);
      alert('Error marking vehicle as sold: ' + error.message);
    } finally {
      setActionLoading(false);
    }
  };

  // Confirm on hold
  const confirmOnHold = async () => {
    if (!selectedVehicle) return;

    setActionLoading(true);
    try {
      console.log('🔄 Updating vehicle status to on hold:', selectedVehicle._id);

      // Get user authentication data
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      if (!user.email || !user.role) {
        alert('Authentication error. Please log in again.');
        return;
      }

      // Create FormData with authentication info
      const formData = new FormData();
      formData.append('userEmail', user.email);
      formData.append('userRole', user.role);
      formData.append('status', 'on_hold');

      const res = await fetch(`/api/vehicles/${selectedVehicle._id}`, {
        method: 'PUT',
        body: formData,
      });

      const responseData = await res.json();
      console.log('📝 API Response:', responseData);

      if (res.ok && responseData.success) {
        console.log('✅ Vehicle marked as on hold successfully');
        onRefresh(); // Refresh the table
        setShowOnHoldModal(false);
        setSelectedVehicle(null);
      } else {
        throw new Error(responseData.error || 'Failed to mark vehicle as on hold');
      }
    } catch (error) {
      console.error('❌ Mark as on hold failed:', error);
      alert('Error marking vehicle as on hold: ' + error.message);
    } finally {
      setActionLoading(false);
    }
  };

  // Confirm archive
  const confirmArchive = async () => {
    if (!selectedVehicle) return;

    setActionLoading(true);
    try {
      console.log('🔄 Archiving vehicle:', selectedVehicle._id);

      // Get user authentication data
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      if (!user.email || !user.role) {
        alert('Authentication error. Please log in again.');
        return;
      }

      // Create FormData with authentication info
      const formData = new FormData();
      formData.append('userEmail', user.email);
      formData.append('userRole', user.role);
      formData.append('status', 'archived');

      const res = await fetch(`/api/vehicles/${selectedVehicle._id}`, {
        method: 'PUT',
        body: formData,
      });

      const responseData = await res.json();
      console.log('📝 API Response:', responseData);

      if (res.ok && responseData.success) {
        console.log('✅ Vehicle archived successfully');
        onRefresh(); // Refresh the table
        setShowArchiveModal(false);
        setSelectedVehicle(null);
      } else {
        throw new Error(responseData.error || 'Failed to archive vehicle');
      }
    } catch (error) {
      console.error('❌ Archive failed:', error);
      alert('Error archiving vehicle: ' + error.message);
    } finally {
      setActionLoading(false);
    }
  };

  // Confirm remove from hold
  const confirmRemoveFromHold = async () => {
    if (!selectedVehicle) return;

    try {
      setActionLoading(true);
      console.log('🔄 Removing vehicle from hold:', selectedVehicle._id);

      // Get user authentication data
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      if (!user.email || !user.role) {
        alert('Authentication error. Please log in again.');
        return;
      }

      // Create FormData with authentication info
      const formData = new FormData();
      formData.append('userEmail', user.email);
      formData.append('userRole', user.role);
      formData.append('status', 'active');

      const res = await fetch(`/api/vehicles/${selectedVehicle._id}`, {
        method: 'PUT',
        body: formData,
      });

      const responseData = await res.json();
      console.log('📝 API Response:', responseData);

      if (res.ok && responseData.success) {
        console.log('✅ Vehicle removed from hold successfully');
        onRefresh(); // Refresh the table
        setShowRemoveFromHoldModal(false);
        setSelectedVehicle(null);
      } else {
        throw new Error(responseData.error || 'Failed to remove vehicle from hold');
      }
    } catch (error) {
      console.error('❌ Error removing vehicle from hold:', error);
      alert('Failed to remove vehicle from hold. Please try again.');
    } finally {
      setActionLoading(false);
    }
  };

  // Cancel modals
  const cancelAction = () => {
    setShowDeleteModal(false);
    setShowSoldModal(false);
    setShowOnHoldModal(false);
    setShowArchiveModal(false);
    setShowRemoveFromHoldModal(false);
    setSelectedVehicle(null);
  };

  // Handle vehicle selection
  const handleSelectVehicle = (vehicleId, isSelected) => {
    if (isSelected) {
      setSelectedVehicles(prev => [...prev, vehicleId]);
    } else {
      setSelectedVehicles(prev => prev.filter(id => id !== vehicleId));
    }
  };

  const handleSelectAll = (isSelected) => {
    if (isSelected) {
      setSelectedVehicles(vehicles.map(v => v._id));
    } else {
      setSelectedVehicles([]);
    }
  };

  const columns = useMemo(() => {
    const baseColumns = [];

    // Add selection column if showSelection is true
    if (showSelection) {
      baseColumns.push({
        id: 'select',
        header: ({ table }) => (
          <input
            type="checkbox"
            checked={selectedVehicles.length === vehicles.length && vehicles.length > 0}
            onChange={(e) => handleSelectAll(e.target.checked)}
            className={styles.selectCheckbox}
          />
        ),
        cell: ({ row }) => (
          <input
            type="checkbox"
            checked={selectedVehicles.includes(row.original._id)}
            onChange={(e) => handleSelectVehicle(row.original._id, e.target.checked)}
            className={styles.selectCheckbox}
          />
        ),
      });
    }

    baseColumns.push({
      accessorKey: 'images',
      header: 'Image',
      cell: ({ row }) => {
        const vehicle = row.original;
        // Try images array first, then fall back to imageUrl for backward compatibility
        const images = vehicle.images;
        const imageUrl = vehicle.imageUrl;
        const firstImage = (images && images.length > 0) ? images[0] : imageUrl;

        return (
          <div className={styles.imageCell}>
            {firstImage ? (
              <img
                src={firstImage}
                alt="Vehicle"
                width={80}
                height={60}
                className={styles.vehicleImage}
                onError={(e) => {
                  e.target.style.display = 'none';
                  e.target.nextSibling.style.display = 'flex';
                }}
              />
            ) : (
              <div className={styles.noImage}>No Image</div>
            )}
            <div className={styles.noImage} style={{ display: 'none' }}>
              No Image
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'year',
      header: 'Year',
      cell: ({ getValue }) => getValue() || 'N/A',
    },
    {
      accessorKey: 'make',
      header: 'Make',
      cell: ({ getValue }) => {
        const make = getValue();
        if (!make || make === 'N/A') return 'N/A';

        // Create a mapping of make names to logo filenames
        const logoMap = {
          'volkswagen': 'Volkswagen-logo-2015-1920x1080.webp',
          'acura': 'acura-logo.webp',
          'audi': 'audi-logo.webp',
          'bmw': 'bmw-logo-1997.webp',
          'chevrolet': 'chevrolet-logo.webp',
          'dodge': 'dodge-logo.webp',
          'ford': 'ford-logo.webp',
          'hyundai': 'hyundai-logo.webp',
          'jeep': 'jeep-logo-1993-640.webp',
          'kia': 'kia-logo.webp',
          'lexus': 'lexus-logo.webp',
          'mazda': 'mazda-logo.webp',
          'mercedes-benz': 'mercedes-benz-logo.webp',
          'mercedes': 'mercedes-benz-logo.webp',
          'mini': 'mini-logo.webp',
          'nissan': 'nissan-logo-2001-2000x1750-show.webp',
          'subaru': 'subaru-logo.webp',
          'suzuki': 'suzuki-logo.webp',
          'toyota': 'toyota-logo.webp',
          'volvo': 'volvo-logo.webp'
        };

        const logoFileName = logoMap[make.toLowerCase()];

        return (
          <div className={styles.makeCell}>
            {logoFileName && (
              <img
                src={`/car-logos/${logoFileName}`}
                alt={`${make} logo`}
                className={styles.makeLogo}
                onError={(e) => {
                  e.target.style.display = 'none';
                }}
              />
            )}
            <span className={styles.makeText}>{make}</span>
          </div>
        );
      },
    },
    {
      accessorKey: 'model',
      header: 'Model',
      cell: ({ getValue }) => getValue() || 'N/A',
    },
    {
      accessorKey: 'price',
      header: 'Price',
      cell: ({ getValue }) => {
        const price = getValue();
        return price ? `$${Number(price).toLocaleString()}` : 'N/A';
      },
    },
    {
      accessorKey: 'mileage',
      header: 'Mileage',
      cell: ({ getValue }) => {
        const mileage = getValue();
        return mileage ? `${Number(mileage).toLocaleString()} km` : 'N/A';
      },
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ getValue }) => {
        const status = getValue() || 'draft';
        const displayStatus = status === 'on_hold' ? 'On Hold' : status.charAt(0).toUpperCase() + status.slice(1);
        return (
          <span className={`${styles.status} ${styles[status]}`}>
            {displayStatus}
          </span>
        );
      },
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => {
        const vehicle = row.original;
        return (
          <div className={styles.actions}>
            <Link
              href={`/admin/vehicles/edit/${vehicle._id}`}
              className={`enhanced-button ${styles.editBtn}`}
            >
              Edit
            </Link>
            {vehicle.status !== 'sold' && vehicle.status !== 'archived' && (
              <button
                onClick={() => handleSoldClick(vehicle)}
                className={`enhanced-button ${styles.soldBtn}`}
                disabled={actionLoading}
              >
                Sold
              </button>
            )}
            {vehicle.status !== 'sold' && vehicle.status !== 'on_hold' && vehicle.status !== 'archived' && (
              <button
                onClick={() => handleOnHoldClick(vehicle)}
                className={styles.onHoldBtn}
                disabled={actionLoading}
              >
                On Hold
              </button>
            )}
            {vehicle.status === 'on_hold' && (
              <button
                onClick={() => handleRemoveFromHoldClick(vehicle)}
                className={styles.removeFromHoldBtn}
                disabled={actionLoading}
              >
                Remove Hold
              </button>
            )}
            {vehicle.status === 'sold' && (
              <button
                onClick={() => handleArchiveClick(vehicle)}
                className={styles.archiveBtn}
                disabled={actionLoading}
              >
                Archive
              </button>
            )}
            <button
              onClick={() => handleDeleteClick(vehicle)}
              className={`enhanced-button ${styles.deleteBtn}`}
              disabled={actionLoading}
            >
              Delete
            </button>
          </div>
        );
      },
    });

    return baseColumns;
  }, [actionLoading, showSelection, selectedVehicles, vehicles]);

  const table = useReactTable({
    data: vehicles,
    columns,
    state: {
      sorting,
      pagination,
    },
    onSortingChange: setSorting,
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  if (loading) {
    return <div className={styles.loading}>Loading vehicles...</div>;
  }

  if (error) {
    return (
      <div className={styles.error}>
        <p>Error: {error}</p>
        <button onClick={onRefresh} className={styles.retryBtn}>
          Retry
        </button>
      </div>
    );
  }

  // Mobile card renderer
  const renderMobileCard = (vehicle) => {
    const primaryImage = (vehicle.images && vehicle.images.length > 0)
      ? vehicle.images[0]
      : vehicle.imageUrl;

    return (
      <div key={vehicle._id} className={styles.mobileVehicleCard}>
        {/* Card Header */}
        <div className={styles.mobileCardHeader}>
          <h3 className={styles.mobileCardTitle}>
            {vehicle.year} {vehicle.make} {vehicle.model}
          </h3>
          <span className={`${styles.mobileCardStatus} ${styles[vehicle.status]}`}>
            {vehicle.status}
          </span>
        </div>

        {/* Card Content */}
        <div className={styles.mobileCardContent}>
          {/* Image and Basic Details */}
          <div className={styles.mobileCardImageSection}>
            {primaryImage ? (
              <img
                src={primaryImage}
                alt={`${vehicle.year} ${vehicle.make} ${vehicle.model}`}
                className={styles.mobileCardImage}
              />
            ) : (
              <div className={styles.mobileCardNoImage}>No Image</div>
            )}

            <div className={styles.mobileCardDetails}>
              <div className={styles.mobileCardDetailRow}>
                <span className={styles.mobileCardDetailLabel}>Price</span>
                <span className={styles.mobileCardDetailValue}>
                  {vehicle.price ? `$${Number(vehicle.price).toLocaleString()}` : 'N/A'}
                </span>
              </div>
              <div className={styles.mobileCardDetailRow}>
                <span className={styles.mobileCardDetailLabel}>Mileage</span>
                <span className={styles.mobileCardDetailValue}>
                  {vehicle.mileage ? `${Number(vehicle.mileage).toLocaleString()} km` : 'N/A'}
                </span>
              </div>
              <div className={styles.mobileCardDetailRow}>
                <span className={styles.mobileCardDetailLabel}>VIN</span>
                <span className={styles.mobileCardDetailValue}>
                  {vehicle.vin ? vehicle.vin.slice(-8) : 'N/A'}
                </span>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className={styles.mobileCardActions}>
            <div className={styles.mobileActionRow}>
              <Link
                href={`/admin/vehicles/edit/${vehicle._id}`}
                className={`${styles.mobileActionButton} ${styles.edit}`}
              >
                ✏️ Edit
              </Link>
              {vehicle.status !== 'sold' && vehicle.status !== 'archived' && (
                <button
                  onClick={() => handleSoldClick(vehicle)}
                  className={`${styles.mobileActionButton} ${styles.sold}`}
                  disabled={actionLoading}
                >
                  ✅ Sold
                </button>
              )}
            </div>
            <div className={styles.mobileActionRow}>
              {vehicle.status !== 'sold' && vehicle.status !== 'on_hold' && vehicle.status !== 'archived' && (
                <button
                  onClick={() => handleOnHoldClick(vehicle)}
                  className={`${styles.mobileActionButton} ${styles.onHold}`}
                  disabled={actionLoading}
                >
                  ⏸️ On Hold
                </button>
              )}
              <button
                onClick={() => handleDeleteClick(vehicle)}
                className={`${styles.mobileActionButton} ${styles.delete}`}
                disabled={actionLoading}
              >
                🗑️ Delete
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className={styles.tableContainer}>
      {/* Filters */}
      <div className={styles.filters}>
        <input
          type="text"
          placeholder="Search by VIN, make, model..."
          value={filters.search}
          onChange={(e) => setFilters({ ...filters, search: e.target.value })}
          className={styles.searchInput}
        />

        <select
          value={filters.make}
          onChange={(e) => setFilters({ ...filters, make: e.target.value })}
          className={styles.filterSelect}
        >
          <option value="">All Makes</option>
          {[...new Set(vehicles.map(v => v.make).filter(Boolean))].map(make => (
            <option key={make} value={make}>{make}</option>
          ))}
        </select>

        <select
          value={filters.status}
          onChange={(e) => setFilters({ ...filters, status: e.target.value })}
          className={styles.filterSelect}
        >
          <option value="all">All Status</option>
          <option value="active">Active</option>
          <option value="draft">Draft</option>
          <option value="sold">Sold</option>
          <option value="archived">Archived</option>
        </select>

        <button onClick={onRefresh} className={`enhanced-button ${styles.refreshBtn}`}>
          🔄 Refresh
        </button>
      </div>

      {/* Desktop Table */}
      <div className={styles.tableWrapper}>
        <table className={styles.table}>
          <thead>
            {table.getHeaderGroups().map(headerGroup => (
              <tr key={headerGroup.id}>
                {headerGroup.headers.map(header => (
                  <th key={header.id} className={styles.th}>
                    {header.isPlaceholder ? null : (
                      <div
                        className={header.column.getCanSort() ? styles.sortable : ''}
                        onClick={header.column.getToggleSortingHandler()}
                      >
                        {flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                        {{
                          asc: ' 🔼',
                          desc: ' 🔽',
                        }[header.column.getIsSorted()] ?? null}
                      </div>
                    )}
                  </th>
                ))}
              </tr>
            ))}
          </thead>
          <tbody>
            {table.getRowModel().rows.map(row => (
              <tr key={row.id} className={styles.tr}>
                {row.getVisibleCells().map(cell => (
                  <td key={cell.id} className={styles.td}>
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>

        {/* Mobile Cards - Only visible on mobile */}
        <div className={styles.mobileCardsContainer}>
          {table.getRowModel().rows.map(row => renderMobileCard(row.original))}
        </div>
      </div>

      {/* Pagination */}
      <div className={styles.pagination}>
        <button
          onClick={() => table.setPageIndex(0)}
          disabled={!table.getCanPreviousPage()}
          className={styles.pageBtn}
        >
          {'<<'}
        </button>
        <button
          onClick={() => table.previousPage()}
          disabled={!table.getCanPreviousPage()}
          className={styles.pageBtn}
        >
          {'<'}
        </button>
        <span className={styles.pageInfo}>
          Page {table.getState().pagination.pageIndex + 1} of{' '}
          {table.getPageCount()}
        </span>
        <button
          onClick={() => table.nextPage()}
          disabled={!table.getCanNextPage()}
          className={styles.pageBtn}
        >
          {'>'}
        </button>
        <button
          onClick={() => table.setPageIndex(table.getPageCount() - 1)}
          disabled={!table.getCanNextPage()}
          className={styles.pageBtn}
        >
          {'>>'}
        </button>
        <select
          value={table.getState().pagination.pageSize}
          onChange={e => table.setPageSize(Number(e.target.value))}
          className={styles.pageSizeSelect}
        >
          {[10, 20, 30, 40, 50].map(pageSize => (
            <option key={pageSize} value={pageSize}>
              Show {pageSize}
            </option>
          ))}
        </select>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <div className={styles.modalOverlay}>
          <div className={styles.modal}>
            <h3>Confirm Delete</h3>
            <p>
              Are you sure you want to delete this vehicle?<br />
              <strong>{selectedVehicle?.year} {selectedVehicle?.make} {selectedVehicle?.model}</strong>
            </p>
            <div className={styles.modalActions}>
              <button
                onClick={confirmDelete}
                className={styles.confirmBtn}
                disabled={actionLoading}
              >
                {actionLoading ? 'Deleting...' : 'Yes, Delete'}
              </button>
              <button
                onClick={cancelAction}
                className={styles.cancelBtn}
                disabled={actionLoading}
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Sold Confirmation Modal */}
      {showSoldModal && (
        <div className={styles.modalOverlay}>
          <div className={styles.modal}>
            <h3>Mark as Sold</h3>
            <p>
              Are you sure you want to move this vehicle to sold?<br />
              <strong>{selectedVehicle?.year} {selectedVehicle?.make} {selectedVehicle?.model}</strong>
            </p>
            <div className={styles.modalActions}>
              <button
                onClick={confirmSold}
                className={styles.confirmBtn}
                disabled={actionLoading}
              >
                {actionLoading ? 'Processing...' : 'Yes, Mark as Sold'}
              </button>
              <button
                onClick={cancelAction}
                className={styles.cancelBtn}
                disabled={actionLoading}
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* On Hold Confirmation Modal */}
      {showOnHoldModal && (
        <div className={styles.modalOverlay}>
          <div className={styles.modal}>
            <h3>Mark as On Hold</h3>
            <p>
              Are you sure you want to mark this vehicle as on hold?<br />
              <strong>{selectedVehicle?.year} {selectedVehicle?.make} {selectedVehicle?.model}</strong>
            </p>
            <div className={styles.modalActions}>
              <button
                onClick={confirmOnHold}
                className={styles.confirmBtn}
                disabled={actionLoading}
              >
                {actionLoading ? 'Processing...' : 'Yes, Mark as On Hold'}
              </button>
              <button
                onClick={cancelAction}
                className={styles.cancelBtn}
                disabled={actionLoading}
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Archive Confirmation Modal */}
      {showArchiveModal && (
        <div className={styles.modalOverlay}>
          <div className={styles.modal}>
            <h3>Archive Vehicle</h3>
            <p>
              Are you sure you want to archive this vehicle?<br />
              <strong>{selectedVehicle?.year} {selectedVehicle?.make} {selectedVehicle?.model}</strong><br />
              <small>Archived vehicles will be hidden from all listings but can be restored later.</small>
            </p>
            <div className={styles.modalActions}>
              <button
                onClick={confirmArchive}
                className={styles.confirmBtn}
                disabled={actionLoading}
              >
                {actionLoading ? 'Archiving...' : 'Yes, Archive'}
              </button>
              <button
                onClick={cancelAction}
                className={styles.cancelBtn}
                disabled={actionLoading}
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Remove from Hold Confirmation Modal */}
      {showRemoveFromHoldModal && (
        <div className={styles.modalOverlay}>
          <div className={styles.modal}>
            <h3>Remove from Hold</h3>
            <p>
              Are you sure you want to remove this vehicle from hold and make it active?<br />
              <strong>{selectedVehicle?.year} {selectedVehicle?.make} {selectedVehicle?.model}</strong>
            </p>
            <div className={styles.modalActions}>
              <button
                onClick={confirmRemoveFromHold}
                className={styles.confirmBtn}
                disabled={actionLoading}
              >
                {actionLoading ? 'Removing...' : 'Yes, Remove Hold'}
              </button>
              <button
                onClick={cancelAction}
                className={styles.cancelBtn}
                disabled={actionLoading}
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
