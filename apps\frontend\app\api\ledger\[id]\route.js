import { NextResponse } from 'next/server';
import { connectToDatabase } from '../../../../lib/dbConnect';
import Ledger from '../../../../lib/models/Ledger';
import { S3Client, GetObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

const s3 = new S3Client({
  region: process.env.CUSTOM_AWS_REGION,
  credentials: {
    accessKeyId: process.env.CUSTOM_AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.CUSTOM_AWS_SECRET_ACCESS_KEY,
  },
});

// GET /api/ledger/[id] - Get single ledger entry
export async function GET(request, { params }) {
  try {
    await connectToDatabase();

    const { id } = await params;

    const ledgerEntry = await Ledger.findById(id)
      .populate('vehicleId', 'make model year vin displayName images')
      .lean();

    if (!ledgerEntry) {
      return NextResponse.json(
        { success: false, error: 'Ledger entry not found' },
        { status: 404 }
      );
    }

    // Generate signed URLs for receipts if they exist
    if (ledgerEntry.receiptUrls && ledgerEntry.receiptUrls.length > 0) {
      const signedUrls = [];
      
      for (const receiptUrl of ledgerEntry.receiptUrls) {
        try {
          // Extract S3 key from URL
          const urlParts = receiptUrl.split('/');
          const bucketIndex = urlParts.findIndex(part => part.includes('fazenauto-ledger-documents'));
          const s3Key = urlParts.slice(bucketIndex + 1).join('/');

          const command = new GetObjectCommand({
            Bucket: 'fazenauto-ledger-documents',
            Key: s3Key,
          });

          const signedUrl = await getSignedUrl(s3, command, { expiresIn: 3600 }); // 1 hour
          signedUrls.push({
            originalUrl: receiptUrl,
            signedUrl,
            fileName: s3Key.split('/').pop()
          });
        } catch (error) {
          console.error('Error generating signed URL:', error);
          signedUrls.push({
            originalUrl: receiptUrl,
            signedUrl: null,
            fileName: receiptUrl.split('/').pop(),
            error: 'Unable to generate download link'
          });
        }
      }

      ledgerEntry.signedReceiptUrls = signedUrls;
    }

    return NextResponse.json({
      success: true,
      data: ledgerEntry
    });

  } catch (error) {
    console.error('Error fetching ledger entry:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch ledger entry' },
      { status: 500 }
    );
  }
}

// PUT /api/ledger/[id] - Update ledger entry
export async function PUT(request, { params }) {
  try {
    await connectToDatabase();

    const { id } = await params;
    const body = await request.json();

    const ledgerEntry = await Ledger.findById(id);

    if (!ledgerEntry) {
      return NextResponse.json(
        { success: false, error: 'Ledger entry not found' },
        { status: 404 }
      );
    }

    // Update allowed fields
    const allowedFields = ['type', 'description', 'amount', 'currency', 'date', 'vehicleId', 'notes'];
    
    allowedFields.forEach(field => {
      if (body[field] !== undefined) {
        ledgerEntry[field] = body[field];
      }
    });

    await ledgerEntry.save();

    // Populate vehicle data for response
    await ledgerEntry.populate('vehicleId', 'make model year vin displayName');

    return NextResponse.json({
      success: true,
      data: ledgerEntry,
      message: 'Ledger entry updated successfully'
    });

  } catch (error) {
    console.error('Error updating ledger entry:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update ledger entry' },
      { status: 500 }
    );
  }
}

// DELETE /api/ledger/[id] - Delete ledger entry
export async function DELETE(request, { params }) {
  try {
    await connectToDatabase();

    const { id } = await params;

    const ledgerEntry = await Ledger.findById(id);

    if (!ledgerEntry) {
      return NextResponse.json(
        { success: false, error: 'Ledger entry not found' },
        { status: 404 }
      );
    }

    // Delete associated S3 files if they exist
    if (ledgerEntry.s3Path) {
      try {
        // Delete all files in the S3 folder
        const s3Path = ledgerEntry.s3Path;
        
        // Delete receipt files
        if (ledgerEntry.receiptUrls && ledgerEntry.receiptUrls.length > 0) {
          for (const receiptUrl of ledgerEntry.receiptUrls) {
            const urlParts = receiptUrl.split('/');
            const bucketIndex = urlParts.findIndex(part => part.includes('fazenauto-ledger-documents'));
            const s3Key = urlParts.slice(bucketIndex + 1).join('/');

            const deleteCommand = new DeleteObjectCommand({
              Bucket: 'fazenauto-ledger-documents',
              Key: s3Key,
            });

            await s3.send(deleteCommand);
          }
        }

        // Delete metadata.json
        const metadataDeleteCommand = new DeleteObjectCommand({
          Bucket: 'fazenauto-ledger-documents',
          Key: `${s3Path}metadata.json`,
        });

        await s3.send(metadataDeleteCommand);

      } catch (s3Error) {
        console.error('Error deleting S3 files:', s3Error);
        // Continue with database deletion even if S3 deletion fails
      }
    }

    // Delete the ledger entry from database
    await Ledger.findByIdAndDelete(id);

    return NextResponse.json({
      success: true,
      message: 'Ledger entry deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting ledger entry:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete ledger entry' },
      { status: 500 }
    );
  }
}
