/* Contact Page Styles */

.contactPage {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 2rem 1rem;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  text-align: center;
  margin-bottom: 3rem;
}

.title {
  font-size: 3rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 1rem;
}

.subtitle {
  font-size: 1.2rem;
  color: #4a5568;
  max-width: 600px;
  margin: 0 auto;
}

.content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.contactGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  min-height: 600px;
}

/* Contact Information */
.contactInfo {
  background: linear-gradient(135deg, #4a5568 0%, #718096 50%, #a0aec0 100%);
  color: white;
  padding: 3rem 2rem;
  display: flex;
  flex-direction: column;
  gap: 2rem;
  position: relative;
}

.contactInfo::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 30px;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.2) 100%);
}

.sectionTitle {
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: white;
}

.contactItem {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.icon {
  font-size: 1.5rem;
  color: #ffd700;
  margin-top: 0.25rem;
  flex-shrink: 0;
}

.contactItem h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: white;
}

.contactItem p {
  margin-bottom: 0.25rem;
  line-height: 1.4;
  color: rgba(255, 255, 255, 0.9);
}

.contactItem span {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
}

/* Contact Form */
.contactForm {
  padding: 3rem 2rem;
}

.contactForm .sectionTitle {
  color: #2d3748;
  margin-bottom: 2rem;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
}

.formGroup label {
  font-weight: 500;
  color: #2d3748;
  margin-bottom: 0.5rem;
  font-size: 0.95rem;
}

.formGroup input,
.formGroup select,
.formGroup textarea {
  padding: 0.75rem;
  border: 2px solid #e2e8f0;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  font-family: inherit;
}

.formGroup input:focus,
.formGroup select:focus,
.formGroup textarea:focus {
  outline: none;
  border-color: #e53e3e;
  box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
}

.formGroup textarea {
  resize: vertical;
  min-height: 120px;
}

.submitButton {
  background: #e53e3e;
  color: white;
  padding: 1rem 2rem;
  border: none;
  border-radius: 6px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
}

.submitButton:hover {
  background: #c53030;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(229, 62, 62, 0.3);
}

.footer {
  padding: 2rem;
  text-align: center;
  background: #f7fafc;
}

.backButton {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: #e53e3e;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  border: 2px solid #e53e3e;
}

.backButton:hover {
  background: #e53e3e;
  color: white;
  transform: translateY(-2px);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .contactPage {
    padding: 1rem 0.5rem;
  }

  .title {
    font-size: 2.5rem;
  }

  .contactGrid {
    grid-template-columns: 1fr;
  }

  .contactInfo {
    padding: 2.5rem 1.5rem;
    background: linear-gradient(135deg, #4a5568 0%, #718096 50%, #a0aec0 100%);
    border-radius: 12px 12px 0 0;
  }

  .contactInfo::after {
    height: 30px;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.2) 100%);
  }

  .contactForm {
    padding: 2rem 1.5rem;
    border-radius: 0 0 12px 12px;
  }

  .contactItem {
    flex-direction: row;
    text-align: left;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .contactItem:last-child {
    border-bottom: none;
  }

  .icon {
    align-self: flex-start;
    margin-top: 0.25rem;
  }

  .sectionTitle {
    text-align: center;
    margin-bottom: 2rem;
  }
}
