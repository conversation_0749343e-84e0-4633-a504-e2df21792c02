/* Compare Page Styling */

.container {
  min-height: 100vh;
  background: var(--bg-secondary, #f8fafc);
  padding: 2rem 1rem;
  transition: background 0.3s ease;
}

/* Dark mode */
[data-theme="dark"] .container {
  background: var(--bg-primary);
}

.header {
  background: var(--bg-primary, white);
  border: 1px solid var(--border-primary, #e2e8f0);
  border-radius: 12px;
  box-shadow: var(--card-shadow);
  margin-bottom: 2rem;
  padding: 1.5rem 2rem;
}

[data-theme="dark"] .header {
  background: var(--bg-secondary);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.headerContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1400px;
  margin: 0 auto;
  gap: 1rem;
}

.backBtn {
  background: transparent;
  border: 1px solid var(--border-primary, #e2e8f0);
  color: var(--text-secondary, #718096);
  padding: 0.75rem 1rem;
  border-radius: 6px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.backBtn:hover {
  background: var(--bg-hover, #f8f9fa);
  color: var(--text-primary, #2d3748);
  border-color: var(--text-secondary, #718096);
}

.titleSection {
  flex: 1;
  text-align: center;
}

.title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary, #2d3748);
  margin-bottom: 0.5rem;
}

.subtitle {
  color: var(--text-secondary, #718096);
  font-size: 1rem;
}

.headerActions {
  display: flex;
  gap: 0.75rem;
}

.shareBtn,
.clearBtn {
  padding: 0.75rem 1rem;
  border-radius: 6px;
  font-weight: 500;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.shareBtn {
  background: var(--accent-primary, #4299e1);
  color: white;
}

.shareBtn:hover {
  background: var(--accent-hover, #3182ce);
}

.clearBtn {
  background: transparent;
  border: 1px solid var(--border-primary, #e2e8f0);
  color: var(--text-secondary, #718096);
}

.clearBtn:hover {
  background: var(--bg-hover, #f8f9fa);
  color: var(--text-primary, #2d3748);
  border-color: var(--text-secondary, #718096);
}

/* Compare Grid */
.compareGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
  max-width: 1400px;
  margin: 0 auto;
}

/* Responsive adjustments for compare grid */
@media (min-width: 768px) and (max-width: 1200px) {
  .compareGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
}

@media (min-width: 1201px) {
  .compareGrid {
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 1.25rem;
  }
}

/* Mobile: Single column layout */
@media (max-width: 767px) {
  .compareGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

/* Loading State */
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
  background: var(--bg-primary, white);
  border-radius: 12px;
  box-shadow: var(--card-shadow);
  margin: 2rem auto;
  padding: 3rem;
  max-width: 500px;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid var(--border-primary, #e2e8f0);
  border-top: 4px solid var(--accent-primary, #4299e1);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loadingText {
  font-size: 1.1rem;
  color: var(--text-secondary, #718096);
}

/* Error State */
.errorContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
  background: var(--bg-primary, white);
  border: 1px solid var(--border-primary, #e2e8f0);
  border-radius: 12px;
  padding: 3rem;
  margin: 2rem auto;
  box-shadow: var(--card-shadow);
  max-width: 500px;
}

.errorIcon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.errorTitle {
  font-size: 1.5rem;
  color: var(--text-primary, #2d3748);
  margin-bottom: 1rem;
}

.errorText {
  font-size: 1rem;
  color: var(--text-secondary, #718096);
  margin-bottom: 1.5rem;
}

.retryBtn {
  background: var(--accent-primary, #4299e1);
  color: white;
  border: none;
  padding: 0.8rem 2rem;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.retryBtn:hover {
  background: var(--accent-hover, #3182ce);
}

/* Empty State */
.emptyContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 500px;
  text-align: center;
  background: var(--bg-primary, white);
  border: 1px solid var(--border-primary, #e2e8f0);
  border-radius: 12px;
  padding: 3rem;
  margin: 2rem auto;
  box-shadow: var(--card-shadow);
  max-width: 600px;
}

.emptyIcon {
  font-size: 5rem;
  margin-bottom: 1.5rem;
  opacity: 0.7;
}

.emptyTitle {
  font-size: 2rem;
  color: var(--text-primary, #2d3748);
  margin-bottom: 1rem;
  font-weight: 600;
}

.emptyText {
  font-size: 1.1rem;
  color: var(--text-secondary, #718096);
  margin-bottom: 0.5rem;
  line-height: 1.5;
}

.emptySubtext {
  color: var(--text-tertiary, #94a3b8);
  font-size: 1rem;
  margin-bottom: 2rem;
  line-height: 1.5;
}

.browseBtn {
  background: var(--accent-primary, #4299e1);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.browseBtn:hover {
  background: var(--accent-hover, #3182ce);
  transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 1rem 0.5rem;
  }

  .header {
    padding: 1rem;
    margin-bottom: 1rem;
  }

  .headerContent {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .titleSection {
    order: -1;
  }

  .title {
    font-size: 1.5rem;
  }

  .subtitle {
    font-size: 0.9rem;
  }

  .headerActions {
    justify-content: center;
    width: 100%;
    flex-wrap: wrap;
  }

  .shareBtn,
  .clearBtn {
    flex: 1;
    max-width: 120px;
    min-width: 100px;
  }

  .backBtn {
    padding: 0.5rem 0.75rem;
    font-size: 0.85rem;
  }

  .emptyContainer {
    padding: 2rem 1rem;
    margin: 1rem;
  }

  .emptyTitle {
    font-size: 1.5rem;
  }

  .emptyIcon {
    font-size: 4rem;
  }
}
