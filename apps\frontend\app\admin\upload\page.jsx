'use client';
import { useState, useEffect } from 'react';
import { decodeVIN, validateVIN, mapVINToFormData } from '../../../utils/vinDecoder';
import { getVehicleAttributes, getFallbackAttributes } from '../../../lib/getVehicleAttributes';
import VINScanner from '../../../components/VINScanner/VINScanner';
import FeaturesSelector from '../../../components/FeaturesSelector/FeaturesSelector';
import styles from './UploadForm.module.css';

export default function UploadForm() {
  const [message, setMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [vinDecoding, setVinDecoding] = useState(false);
  const [vinData, setVinData] = useState(null);
  const [showVINScanner, setShowVINScanner] = useState(false);
  const [vehicleAttributes, setVehicleAttributes] = useState(null);
  const [attributesLoading, setAttributesLoading] = useState(true);
  const [formData, setFormData] = useState({
    make: '',
    model: '',
    year: '',
    color: '',
    vin: '',
    mileage: '',
    price: '',
    engine: '',
    transmission: '',
    drivetrain: '',
    // Additional fields to match Vehicle Info
    fuelType: '',
    bodyClass: '',
    doors: '',
    cylinders: '',
    description: ''
  });

  const [selectedFeatures, setSelectedFeatures] = useState({
    exterior: [],
    interior: [],
    mechanical: [],
    safety: [],
    entertainment: []
  });

  // Fetch vehicle attributes on component mount
  useEffect(() => {
    const fetchAttributes = async () => {
      console.log('Starting to fetch vehicle attributes...');
      setAttributesLoading(true);

      try {
        console.log('Calling getVehicleAttributes...');
        const result = await getVehicleAttributes();
        console.log('Vehicle attributes result:', result);

        if (result.success && result.data) {
          console.log('Setting vehicle attributes:', result.data);
          setVehicleAttributes(result.data);
        } else {
          console.warn('API failed or returned no data, using fallback:', result.error);
          const fallback = getFallbackAttributes();
          console.log('Using fallback attributes:', fallback);
          setVehicleAttributes(fallback);
        }
      } catch (error) {
        console.error('Error in fetchAttributes:', error);
        const fallback = getFallbackAttributes();
        console.log('Using fallback attributes due to error:', fallback);
        setVehicleAttributes(fallback);
      }

      console.log('Setting attributesLoading to false');
      setAttributesLoading(false);
    };

    fetchAttributes();
  }, []);

  // Debug: Log form data changes
  useEffect(() => {
    console.log('🚨 FORM STATE UPDATE - Current form data:', {
      doors: formData.doors,
      cylinders: formData.cylinders,
      bodyClass: formData.bodyClass,
      fuelType: formData.fuelType
    });

    // Also log what dropdown options are available
    if (vehicleAttributes) {
      console.log('🚨 AVAILABLE DROPDOWN OPTIONS:');
      console.log('  Doors options:', vehicleAttributes.doors);
      console.log('  Cylinders options:', vehicleAttributes.cylinders);

      // Check if current values match any dropdown options
      if (vehicleAttributes.doors && formData.doors) {
        const doorMatch = vehicleAttributes.doors.find(opt =>
          (typeof opt === 'object' ? opt.value : opt) === formData.doors
        );
        console.log(`🚨 DOORS MATCH CHECK: "${formData.doors}" matches:`, doorMatch);
      }

      if (vehicleAttributes.cylinders && formData.cylinders) {
        const cylinderMatch = vehicleAttributes.cylinders.find(opt =>
          (typeof opt === 'object' ? opt.value : opt) === formData.cylinders
        );
        console.log(`🚨 CYLINDERS MATCH CHECK: "${formData.cylinders}" matches:`, cylinderMatch);
      }
    }
  }, [formData.doors, formData.cylinders, formData.bodyClass, formData.fuelType, vehicleAttributes]);

  // Helper function to render dropdown options
  const renderDropdownOptions = (attributeType, placeholder) => {
    console.log(`Rendering dropdown for ${attributeType}, loading: ${attributesLoading}, attributes:`, vehicleAttributes);

    if (attributesLoading) {
      return <option value="">Loading...</option>;
    }

    if (!vehicleAttributes || !vehicleAttributes[attributeType]) {
      console.log(`No attributes found for ${attributeType}`);
      return <option value="">{placeholder}</option>;
    }

    console.log(`Rendering options for ${attributeType}:`, vehicleAttributes[attributeType]);

    return (
      <>
        <option value="">{placeholder}</option>
        {vehicleAttributes[attributeType].map((option, index) => {
          // Handle both string values and objects with value/display properties
          if (typeof option === 'object' && option.value && option.display) {
            return (
              <option key={index} value={option.value}>
                {option.display}
              </option>
            );
          } else {
            return (
              <option key={index} value={option}>
                {option}
              </option>
            );
          }
        })}
      </>
    );
  };

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle VIN decoding
  const handleDecodeVIN = async () => {
    if (!formData.vin) {
      setMessage('Please enter a VIN first');
      return;
    }

    if (!validateVIN(formData.vin)) {
      setMessage('Invalid VIN format. VIN must be 17 characters long and contain only letters and numbers (no I, O, or Q).');
      return;
    }

    setVinDecoding(true);
    setMessage('');

    try {
      const result = await decodeVIN(formData.vin);

      if (result.success) {
        const mappedData = mapVINToFormData(result.data);
        setVinData(result.data);

        // Debug logging to see what values we're trying to set
        console.log('🔍 Upload Form - Mapped VIN Data:', mappedData);
        console.log('🔍 Upload Form - Setting form values:', {
          doors: mappedData.doors,
          cylinders: mappedData.cylinders,
          bodyClass: mappedData.bodyClass,
          fuelType: mappedData.fuelType
        });

        // Auto-populate form fields with ALL available data
        setFormData(prev => {
          const newFormData = {
            ...prev,
            make: mappedData.make || prev.make,
            model: mappedData.model || prev.model,
            year: mappedData.year || prev.year,
            engine: mappedData.engine || prev.engine,
            transmission: mappedData.transmission || prev.transmission,
            drivetrain: mappedData.driveline || prev.drivetrain,
            // Additional fields from Vehicle Info
            fuelType: mappedData.fuelType || prev.fuelType,
            bodyClass: mappedData.bodyClass || prev.bodyClass,
            doors: mappedData.doors || prev.doors,
            cylinders: mappedData.cylinders || prev.cylinders
          };

          console.log('🔍 Upload Form - Previous form data:', prev);
          console.log('🔍 Upload Form - New form data being set:', newFormData);
          console.log('🔍 Upload Form - Specific field updates:', {
            doors: { old: prev.doors, new: newFormData.doors },
            cylinders: { old: prev.cylinders, new: newFormData.cylinders },
            bodyClass: { old: prev.bodyClass, new: newFormData.bodyClass }
          });

          return newFormData;
        });

        // Enhanced success message showing what was populated
        const populatedFields = [];
        if (mappedData.doors) populatedFields.push(`Doors: ${mappedData.doors}`);
        if (mappedData.cylinders) populatedFields.push(`Cylinders: ${mappedData.cylinders}`);
        if (mappedData.bodyClass) populatedFields.push(`Body: ${mappedData.bodyClass}`);
        if (mappedData.fuelType) populatedFields.push(`Fuel: ${mappedData.fuelType}`);

        const additionalInfo = populatedFields.length > 0 ? ` | ${populatedFields.join(', ')}` : '';
        setMessage(`✅ VIN decoded successfully! Auto-populated: ${mappedData.year} ${mappedData.make} ${mappedData.model}${additionalInfo}`);

        // Debug alert to make it very visible
        setTimeout(() => {
          console.log('🚨 FINAL CHECK - Form state after VIN decode:');
          console.log('  doors:', formData.doors);
          console.log('  cylinders:', formData.cylinders);
          console.log('  bodyClass:', formData.bodyClass);

          // Show an alert with the current form state
          alert(`DEBUG: Form state after VIN decode:\nDoors: "${formData.doors}"\nCylinders: "${formData.cylinders}"\nBody Class: "${formData.bodyClass}"`);
        }, 1000); // Wait 1 second for state to update
      } else {
        setMessage(`❌ VIN Decoder Error: ${result.error}`);
      }
    } catch (error) {
      setMessage('❌ Failed to decode VIN. Please try again or enter details manually.');
      console.error('VIN decode error:', error);
    } finally {
      setVinDecoding(false);
    }
  };

  // Handle VIN detected from scanner
  const handleVINDetected = async (detectedVIN) => {
    setShowVINScanner(false);

    // Automatically decode the detected VIN
    if (validateVIN(detectedVIN)) {
      setVinDecoding(true);
      try {
        const result = await decodeVIN(detectedVIN);
        if (result.success) {
          const mappedData = mapVINToFormData(result.data);
          setVinData(result.data);

          setFormData(prev => ({
            ...prev,
            vin: detectedVIN,
            make: mappedData.make || prev.make,
            model: mappedData.model || prev.model,
            year: mappedData.year || prev.year,
            engine: mappedData.engine || prev.engine,
            transmission: mappedData.transmission || prev.transmission,
            drivetrain: mappedData.driveline || prev.drivetrain,
            // Additional fields from Vehicle Info
            fuelType: mappedData.fuelType || prev.fuelType,
            bodyClass: mappedData.bodyClass || prev.bodyClass,
            doors: mappedData.doors || prev.doors,
            cylinders: mappedData.cylinders || prev.cylinders
          }));

          setMessage(`✅ VIN scanned and decoded successfully! Auto-populated: ${mappedData.year} ${mappedData.make} ${mappedData.model}`);
        } else {
          setMessage(`✅ VIN scanned: ${detectedVIN}. Click "Decode VIN" to auto-populate details.`);
        }
      } catch (error) {
        setMessage(`✅ VIN scanned: ${detectedVIN}. Click "Decode VIN" to auto-populate details.`);
      } finally {
        setVinDecoding(false);
      }
    } else {
      setMessage(`⚠️ Scanned VIN may be invalid: ${detectedVIN}. Please verify.`);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setMessage('');

    try {
      const submitFormData = new FormData(e.target);

      // Add features data to form
      submitFormData.append('features', JSON.stringify(selectedFeatures));

      const res = await fetch('/api/vehicles/upload', {
        method: 'POST',
        body: submitFormData,
      });

      // Check if response is ok
      if (!res.ok) {
        throw new Error(`HTTP error! status: ${res.status}`);
      }

      const data = await res.json();
      console.log('Upload response:', data); // Debug logging

      if (data.success) {
        setMessage(data.message || 'Vehicle uploaded successfully!');

        // Reset form data on successful upload
        setFormData({
          make: '',
          model: '',
          year: '',
          color: '',
          vin: '',
          mileage: '',
          price: '',
          engine: '',
          transmission: '',
          drivetrain: '',
          fuelType: '',
          bodyClass: '',
          doors: '',
          cylinders: '',
          description: ''
        });

        // Reset file inputs
        const form = e.target;
        const fileInputs = form.querySelectorAll('input[type="file"]');
        fileInputs.forEach(input => {
          input.value = '';
        });

        // Reset selected features
        setSelectedFeatures({
          exterior: [],
          interior: [],
          mechanical: [],
          safety: [],
          entertainment: []
        });

        // Clear VIN data
        setVinData(null);
      } else {
        console.error('Upload failed:', data.error); // Debug logging
        setMessage(data.error || 'Error uploading vehicle. Please try again.');
      }
    } catch (error) {
      console.error('Upload error:', error); // Debug logging
      setMessage(`Error uploading vehicle: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.formWrapper}>
        <h1 className={styles.title}>Upload a Vehicle</h1>

        <form onSubmit={handleSubmit} encType="multipart/form-data" className={styles.form}>

          {/* Basic Vehicle Information */}
          <div className={styles.twoColumnGrid}>
            <div className={styles.inputGroup}>
              <label className={styles.label}>Make *</label>
              <input
                name="make"
                value={formData.make}
                onChange={handleInputChange}
                placeholder="e.g., Toyota, Honda, Ford"
                required
                className="formInput"
              />
            </div>

            <div className={styles.inputGroup}>
              <label className={styles.label}>Model *</label>
              <input
                name="model"
                value={formData.model}
                onChange={handleInputChange}
                placeholder="e.g., Camry, Civic, F-150"
                required
                className="formInput"
              />
            </div>
          </div>

          <div className={styles.twoColumnGrid}>
            <div className={styles.inputGroup}>
              <label className={styles.label}>Year *</label>
              <input
                name="year"
                type="number"
                value={formData.year}
                onChange={handleInputChange}
                placeholder="e.g., 2020"
                min="1900"
                max={new Date().getFullYear() + 1}
                required
                className="formInput"
              />
            </div>

            <div className={styles.inputGroup}>
              <label className={styles.label}>Color *</label>
              <input
                name="color"
                value={formData.color}
                onChange={handleInputChange}
                placeholder="e.g., Red, Blue, Silver"
                required
                className="formInput"
              />
            </div>
          </div>

          <div className={styles.inputGroup}>
            <label className={styles.label}>VIN *
              <span className={styles.vinHelper}>
                (17-character Vehicle Identification Number)
              </span>
            </label>
            <div className={styles.vinInputGroup}>
              <input
                name="vin"
                value={formData.vin}
                onChange={handleInputChange}
                placeholder="Enter VIN to auto-populate vehicle details"
                maxLength="17"
                required
                className="formInput"
                style={{ textTransform: 'uppercase' }}
              />
              <button
                type="button"
                onClick={() => setShowVINScanner(true)}
                className={styles.scanButton}
              >
                <span>📷</span>
                <span>Scan VIN</span>
              </button>
              <button
                type="button"
                onClick={handleDecodeVIN}
                disabled={vinDecoding || !formData.vin || formData.vin.length !== 17}
                className={styles.decodeButton}
              >
                {vinDecoding ? '🔄 Decoding...' : '🔍 Decode VIN'}
              </button>
            </div>

          </div>

          <div className={styles.twoColumnGrid}>
            <div className={styles.inputGroup}>
              <label className={styles.label}>Odometer (KM) *</label>
              <input
                name="mileage"
                type="number"
                value={formData.mileage}
                onChange={handleInputChange}
                placeholder="e.g., 50000"
                min="0"
                required
                className="formInput"
              />
            </div>

            <div className={styles.inputGroup}>
              <label className={styles.label}>Price (CAD) *</label>
              <input
                name="price"
                type="number"
                value={formData.price}
                onChange={handleInputChange}
                placeholder="e.g., 25000"
                min="0"
                step="100"
                required
                className="formInput"
              />
            </div>
          </div>

          {/* Engine & Drivetrain Information */}
          <div className={styles.twoColumnGrid}>
            <div className={styles.inputGroup}>
              <label className={styles.label}>Engine *</label>
              <select
                name="engine"
                value={formData.engine}
                onChange={handleInputChange}
                required
                className="formInput"
              >
                {renderDropdownOptions('engine', 'Select Engine')}
              </select>
            </div>

            <div className={styles.inputGroup}>
              <label className={styles.label}>Transmission *</label>
              <select
                name="transmission"
                value={formData.transmission}
                onChange={handleInputChange}
                required
                className="formInput"
              >
                {renderDropdownOptions('transmission', 'Select Transmission')}
              </select>
            </div>
          </div>

          <div className={styles.twoColumnGrid}>
            <div className={styles.inputGroup}>
              <label className={styles.label}>Drivetrain *</label>
              <select
                name="drivetrain"
                value={formData.drivetrain}
                onChange={handleInputChange}
                required
                className="formInput"
              >
                {renderDropdownOptions('drivetrain', 'Select Drivetrain')}
              </select>
            </div>

            <div className={styles.inputGroup}>
              <label className={styles.label}>Fuel Type</label>
              <select
                name="fuelType"
                value={formData.fuelType}
                onChange={handleInputChange}
                className="formInput"
              >
                {renderDropdownOptions('fueltype', 'Select Fuel Type')}
              </select>
            </div>
          </div>

          <div className={styles.twoColumnGrid}>
            <div className={styles.inputGroup}>
              <label className={styles.label}>Body Class</label>
              <select
                name="bodyClass"
                value={formData.bodyClass}
                onChange={handleInputChange}
                className="formInput"
              >
                {renderDropdownOptions('bodyclass', 'Select Body Class')}
              </select>
            </div>

            <div className={styles.inputGroup}>
              <label className={styles.label}>Doors</label>
              <select
                name="doors"
                value={formData.doors}
                onChange={handleInputChange}
                className="formInput"
              >
                {renderDropdownOptions('doors', 'Select Doors')}
              </select>
            </div>
          </div>

          <div className={styles.twoColumnGrid}>
            <div className={styles.inputGroup}>
              <label className={styles.label}>Cylinders</label>
              <select
                name="cylinders"
                value={formData.cylinders}
                onChange={handleInputChange}
                className="formInput"
              >
                {renderDropdownOptions('cylinders', 'Select Cylinders')}
              </select>
            </div>


          </div>

          {/* Vehicle Description */}
          <div className={styles.inputGroup}>
            <label className={styles.label}>Description</label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              rows="4"
              className="formInput"
              placeholder="Enter vehicle description..."
            />
          </div>

          {/* Vehicle Images */}
          <div className={styles.inputGroup}>
            <label className={styles.label}>Vehicle Images *</label>
            <input
              type="file"
              name="images"
              accept="image/*"
              multiple
              required
              className={styles.fileInput}
            />
            <small className={styles.helpText}>
              Upload multiple images of the vehicle. Maximum 50 images, 10MB each.
            </small>
          </div>

          {/* Vehicle Video */}
          <div className={styles.inputGroup}>
            <label className={styles.label}>Vehicle Video (Optional)</label>
            <input
              type="file"
              name="video"
              accept="video/mp4,video/avi,video/mov,video/wmv,video/webm"
              className={styles.fileInput}
            />
            <small className={styles.helpText}>
              Upload a video of the vehicle (MP4, AVI, MOV, WMV, WebM). Maximum size: 100MB.
            </small>
          </div>

          {/* Vehicle Features */}
          <FeaturesSelector
            selectedFeatures={selectedFeatures}
            onFeaturesChange={setSelectedFeatures}
          />

          <button
            type="submit"
            disabled={isLoading}
            className={styles.submitButton}
          >
            {isLoading ? 'Uploading...' : 'Upload Vehicle'}
          </button>

          {message && (
            <div className={`${styles.message} ${message.includes('Error') ? styles.messageError : styles.messageSuccess}`}>
              {message}
            </div>
          )}
        </form>
      </div>

      {/* VIN Scanner Modal */}
      {showVINScanner && (
        <VINScanner
          onVINDetected={handleVINDetected}
          onClose={() => setShowVINScanner(false)}
        />
      )}
    </div>
  );
}
