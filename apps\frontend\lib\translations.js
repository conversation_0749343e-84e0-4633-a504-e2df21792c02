// Legacy translation functions for backward compatibility
// These are kept for the hybrid JS → JSON translation sync system

// Translation files loader
const translationFiles = {
  en: () => import('../locales/en/translation.json'),
  es: () => import('../locales/es/translation.json'),
  fr: () => import('../locales/fr/translation.json'),
  ur: () => import('../locales/ur/translation.json'),
  ar: () => import('../locales/ar/translation.json'),
};

// Cache for loaded translations
const translationCache = {};

export async function loadTranslation(language) {
  if (translationCache[language]) {
    console.log(`📚 Translation cache hit for ${language}:`, Object.keys(translationCache[language]).length, 'keys');
    return translationCache[language];
  }

  try {
    // Check if we have a translation file for this language
    if (!translationFiles[language]) {
      console.warn(`No translation file found for language: ${language}`);
      // Fallback to English
      if (language !== 'en') {
        return loadTranslation('en');
      }
      return {};
    }

    console.log(`📚 Loading translation for ${language}...`);
    const translationModule = await translationFiles[language]();
    const translation = translationModule.default || translationModule;
    translationCache[language] = translation;
    console.log(`📚 Successfully loaded ${language} translation:`, Object.keys(translation).length, 'keys');
    console.log(`📚 Sample keys for ${language}:`, Object.keys(translation).slice(0, 5));
    return translation;
  } catch (error) {
    console.error(`Failed to load translation for ${language}:`, error);
    // Fallback to English
    if (language !== 'en') {
      return loadTranslation('en');
    }
    // If even English fails, return empty object to prevent crashes
    return {};
  }
}

export function getTranslation(language) {
  return (key, fallback = key) => {
    try {
      const translation = translationCache[language] || {};

      // First try to get the key directly (for dot notation keys like "nav.home")
      if (translation[key]) {
        return translation[key];
      }

      // If direct key doesn't exist, try nested approach (for nested objects)
      if (key.includes('.')) {
        const keys = key.split('.');
        let value = translation;

        for (const k of keys) {
          if (value && typeof value === 'object' && k in value) {
            value = value[k];
          } else {
            return fallback;
          }
        }

        return typeof value === 'string' ? value : fallback;
      }

      // If no dots and not found directly, return fallback
      return fallback;
    } catch (error) {
      console.error(`Translation error for key "${key}":`, error);
      return fallback;
    }
  };
}

// Preload translations for better performance
export async function preloadTranslations() {
  const languages = Object.keys(translationFiles);
  await Promise.all(languages.map(lang => loadTranslation(lang)));
}



// Translation system for WrenchsUp
export const translations = {
  en: {
    // Navigation
    'nav.home': 'Home',
    'nav.inventory': 'Inventory',
    'nav.used_vehicles': 'Used Vehicles',
    'nav.financing': 'Financing',
    'nav.pricing': 'Pricing',
    'nav.features': 'Features',
    'nav.about': 'About',
    'nav.contact': 'Contact',
    'nav.login': 'Dealer Login',
    'nav.signup': 'Get Started',
    'nav.dashboard': 'Dashboard',
    'nav.jobs': 'Jobs',
    'nav.quotes': 'Quotes',
    'nav.invoices': 'Invoices',
    'nav.customers': 'Customers',
    'nav.vehicles': 'Vehicle Management',
    'nav.admin': 'Admin',
    
    // Settings
    'settings.title': 'Settings',
    'settings.subtitle': 'Manage your account preferences and settings',
    'settings.notifications': 'Notifications',
    'settings.privacy': 'Privacy',
    'settings.preferences': 'Preferences',
    'settings.account': 'Account',
    'settings.theme': 'Theme',
    'settings.theme.description': 'Choose your preferred theme',
    'settings.language': 'Language',
    'settings.language.description': 'Select your preferred language',
    'settings.currency': 'Currency',
    'settings.currency.description': 'Default currency for pricing',
    
    // Free Trial
    'trial.title': 'Start Your Free Trial',
    'trial.subtitle': 'Choose your plan and start your 14-day free trial. No credit card required.',
    'trial.continue': 'Continue with',
    'trial.selected': 'Selected Plan:',
    'trial.then': '14-day free trial, then',
    
    // Profile
    'profile.title': 'Profile',
    'profile.subtitle': 'Manage your account information',
    'profile.subscription': 'Subscription',
    'profile.no_subscription': 'No active subscription found.',
    'profile.start_trial': 'Start Free Trial',
    
    // Admin
    'admin.title': 'Admin Panel',
    'admin.subtitle': 'Manage job types, pricing, and parts inventory',
    'admin.job_types': 'Job Types',
    'admin.parts_inventory': 'Parts & Inventory',
    'admin.settings': 'Settings',
    'admin.service_types': 'Service Types',
    'admin.base_price': 'Base Price:',
    'admin.est_time': 'Est. Time:',
    'admin.edit': 'Edit',
    'admin.delete': 'Delete',

    // Vehicle Inventory Management
    'admin.vehicle_inventory_management': 'Inventory Management',
    'admin.show_syndication': 'Show Syndication',
    'admin.hide_syndication': 'Hide Syndication',
    'admin.visitors': 'Visitors',
    'admin.show_image_processing': 'Show Image Processing',
    'admin.hide_image_processing': 'Hide Image Processing',
    'admin.show_ad_generator': 'Show Ad Generator',
    'admin.hide_ad_generator': 'Hide Ad Generator',
    'admin.vehicles_selected': 'vehicle(s) selected',
    'admin.total_vehicles': 'Total Vehicles',
    'admin.active_listings': 'Active Listings',
    'admin.draft_listings': 'Draft Listings',
    'admin.sold_vehicles': 'Sold Vehicles',
    'admin.archived_vehicles': 'Archived Vehicles',
    'admin.click_to_view_all': 'Click to view all vehicles',
    'admin.image_processing_pipeline': 'Image Processing Pipeline',
    'admin.batch_process_images': 'Batch process vehicle images for multiple platforms',
    'admin.vin_to_ad_generator': 'VIN-to-Ad Generator',
    'admin.generate_compelling_ads': 'Generate compelling ads from VIN using AI',

    // Dashboard
    'dashboard.title': 'Dashboard',
    'dashboard.subtitle': 'Welcome back! Here\'s your business overview.',
    'dashboard.welcome_back_user': 'Welcome back, {name}! Here\'s your business overview.',
    'dashboard.total_jobs': 'Total Jobs',
    'dashboard.revenue': 'Revenue',
    'dashboard.pending_jobs': 'Pending Jobs',
    'dashboard.customers': 'Customers',
    'dashboard.recent_jobs': 'Recent Jobs',
    'dashboard.no_recent_jobs': 'No recent jobs found.',

    // Jobs
    'jobs.title': 'Jobs',
    'jobs.subtitle': 'Manage your scheduled and completed jobs',
    'jobs.new': '+ New Job',
    'jobs.all': 'All Jobs',
    'jobs.no_jobs': 'No jobs',
    'jobs.get_started': 'Get started by creating a new job.',
    'jobs.status.completed': 'Completed',
    'jobs.status.scheduled': 'Scheduled',
    'jobs.status.in_progress': 'In Progress',

    // Customers
    'customers.title': 'Customers',
    'customers.subtitle': 'Manage your customer database',
    'customers.new': '+ New Customer',
    'customers.search': 'Search customers...',
    'customers.phone': 'Phone',
    'customers.address': 'Address',
    'customers.total_jobs': 'Total Jobs',
    'customers.total_spent': 'Total Spent',
    'customers.last_service': 'Last Service',
    'customers.view_history': 'View History',
    'customers.edit': 'Edit',
    'customers.schedule_job': 'Schedule Job',
    'customers.send_quote': 'Send Quote',
    
    // Vehicles
    'vehicles.title': 'Vehicles',
    'vehicles.subtitle': 'Manage customer vehicles and service history',
    'vehicles.add': '+ Add Vehicle',
    
    // Quotes
    'quotes.title': 'Quotes',
    'quotes.subtitle': 'Create and manage service quotes',
    'quotes.new': '+ New Quote',
    'quotes.search': 'Search quotes...',
    'quotes.all_status': 'All Status',

    // Invoices
    'invoices.title': 'Invoices',
    'invoices.subtitle': 'Track payments and manage billing',
    'invoices.new': '+ New Invoice',
    'invoices.search': 'Search invoices...',
    'invoices.all_status': 'All Status',

    // Common
    'common.loading': 'Loading...',
    'common.save': 'Save',
    'common.cancel': 'Cancel',
    'common.edit': 'Edit',
    'common.delete': 'Delete',
    'common.close': 'Close',
    'common.search': 'Search',

    // Footer
    'footer.language': 'Language',
    'footer.darkMode': 'Dark Mode',

    // Sidebar
    'sidebar.darkMode': 'Dark Mode',
    'sidebar.language': 'Language',
  },
  
  fr: {
    // Navigation
    'nav.home': 'Accueil',
    'nav.inventory': 'Inventaire',
    'nav.used_vehicles': 'Véhicules d\'occasion',
    'nav.financing': 'Financement',
    'nav.pricing': 'Tarifs',
    'nav.features': 'Fonctionnalités',
    'nav.about': 'À propos',
    'nav.contact': 'Contact',
    'nav.login': 'Connexion concessionnaire',
    'nav.signup': 'Commencer',
    'nav.dashboard': 'Tableau de bord',
    'nav.jobs': 'Travaux',
    'nav.quotes': 'Devis',
    'nav.invoices': 'Factures',
    'nav.customers': 'Clients',
    'nav.vehicles': 'Gestion des véhicules',
    'nav.admin': 'Admin',
    
    // Settings
    'settings.title': 'Paramètres',
    'settings.subtitle': 'Gérez vos préférences de compte et paramètres',
    'settings.notifications': 'Notifications',
    'settings.privacy': 'Confidentialité',
    'settings.preferences': 'Préférences',
    'settings.account': 'Compte',
    'settings.theme': 'Thème',
    'settings.theme.description': 'Choisissez votre thème préféré',
    'settings.language': 'Langue',
    'settings.language.description': 'Sélectionnez votre langue préférée',
    'settings.currency': 'Devise',
    'settings.currency.description': 'Devise par défaut pour les prix',
    
    // Free Trial
    'trial.title': 'Commencez votre essai gratuit',
    'trial.subtitle': 'Choisissez votre plan et commencez votre essai gratuit de 14 jours. Aucune carte de crédit requise.',
    'trial.continue': 'Continuer avec',
    'trial.selected': 'Plan sélectionné:',
    'trial.then': 'Essai gratuit de 14 jours, puis',
    
    // Profile
    'profile.title': 'Profil',
    'profile.subtitle': 'Gérez les informations de votre compte',
    'profile.subscription': 'Abonnement',
    'profile.no_subscription': 'Aucun abonnement actif trouvé.',
    'profile.start_trial': 'Commencer l\'essai gratuit',
    
    // Admin
    'admin.title': 'Panneau d\'administration',
    'admin.subtitle': 'Gérez les types de travaux, les prix et l\'inventaire des pièces',
    'admin.job_types': 'Types de travaux',
    'admin.parts_inventory': 'Pièces et inventaire',
    'admin.settings': 'Paramètres',
    'admin.service_types': 'Types de services',
    'admin.base_price': 'Prix de base:',
    'admin.est_time': 'Temps estimé:',
    'admin.edit': 'Modifier',
    'admin.delete': 'Supprimer',

    // Dashboard
    'dashboard.title': 'Tableau de bord',
    'dashboard.subtitle': 'Bon retour ! Voici un aperçu de votre entreprise.',
    'dashboard.welcome_back_user': 'Bon retour, {name} ! Voici un aperçu de votre entreprise.',
    'dashboard.total_jobs': 'Total des travaux',
    'dashboard.revenue': 'Revenus',
    'dashboard.pending_jobs': 'Travaux en attente',
    'dashboard.customers': 'Clients',
    'dashboard.recent_jobs': 'Travaux récents',
    'dashboard.no_recent_jobs': 'Aucun travail récent trouvé.',

    // Jobs
    'jobs.title': 'Travaux',
    'jobs.subtitle': 'Gérez vos travaux planifiés et terminés',
    'jobs.new': '+ Nouveau travail',
    'jobs.all': 'Tous les travaux',
    'jobs.no_jobs': 'Aucun travail',
    'jobs.get_started': 'Commencez par créer un nouveau travail.',
    'jobs.status.completed': 'Terminé',
    'jobs.status.scheduled': 'Planifié',
    'jobs.status.in_progress': 'En cours',

    // Customers
    'customers.title': 'Clients',
    'customers.subtitle': 'Gérez votre base de données clients',
    'customers.new': '+ Nouveau client',
    'customers.search': 'Rechercher des clients...',
    'customers.phone': 'Téléphone',
    'customers.address': 'Adresse',
    'customers.total_jobs': 'Total des travaux',
    'customers.total_spent': 'Total dépensé',
    'customers.last_service': 'Dernier service',
    'customers.view_history': 'Voir l\'historique',
    'customers.edit': 'Modifier',
    'customers.schedule_job': 'Planifier un travail',
    'customers.send_quote': 'Envoyer un devis',
    
    // Vehicles
    'vehicles.title': 'Véhicules',
    'vehicles.subtitle': 'Gérez les véhicules clients et l\'historique de service',
    'vehicles.add': '+ Ajouter un véhicule',

    // Quotes
    'quotes.title': 'Devis',
    'quotes.subtitle': 'Créez et gérez les devis de service',
    'quotes.new': '+ Nouveau devis',
    'quotes.search': 'Rechercher des devis...',
    'quotes.all_status': 'Tous les statuts',

    // Invoices
    'invoices.title': 'Factures',
    'invoices.subtitle': 'Suivez les paiements et gérez la facturation',
    'invoices.new': '+ Nouvelle facture',
    'invoices.search': 'Rechercher des factures...',
    'invoices.all_status': 'Tous les statuts',

    // Common
    'common.loading': 'Chargement...',
    'common.save': 'Enregistrer',
    'common.cancel': 'Annuler',
    'common.edit': 'Modifier',
    'common.delete': 'Supprimer',
    'common.close': 'Fermer',
    'common.search': 'Rechercher',

    // Footer
    'footer.language': 'Langue',
    'footer.darkMode': 'Mode Sombre',

    // Sidebar
    'sidebar.darkMode': 'Mode Sombre',
    'sidebar.language': 'Langue',
  },
  
  es: {
    // Navigation
    'nav.home': 'Inicio',
    'nav.inventory': 'Inventario',
    'nav.used_vehicles': 'Vehículos usados',
    'nav.financing': 'Financiamiento',
    'nav.pricing': 'Precios',
    'nav.features': 'Características',
    'nav.about': 'Acerca de',
    'nav.contact': 'Contacto',
    'nav.login': 'Acceso concesionario',
    'nav.signup': 'Comenzar',
    'nav.dashboard': 'Panel',
    'nav.jobs': 'Trabajos',
    'nav.quotes': 'Cotizaciones',
    'nav.invoices': 'Facturas',
    'nav.customers': 'Clientes',
    'nav.vehicles': 'Gestión de vehículos',
    'nav.admin': 'Admin',
    
    // Settings
    'settings.title': 'Configuración',
    'settings.subtitle': 'Administra las preferencias y configuración de tu cuenta',
    'settings.notifications': 'Notificaciones',
    'settings.privacy': 'Privacidad',
    'settings.preferences': 'Preferencias',
    'settings.account': 'Cuenta',
    'settings.theme': 'Tema',
    'settings.theme.description': 'Elige tu tema preferido',
    'settings.language': 'Idioma',
    'settings.language.description': 'Selecciona tu idioma preferido',
    'settings.currency': 'Moneda',
    'settings.currency.description': 'Moneda predeterminada para precios',
    
    // Free Trial
    'trial.title': 'Comienza tu prueba gratuita',
    'trial.subtitle': 'Elige tu plan y comienza tu prueba gratuita de 14 días. No se requiere tarjeta de crédito.',
    'trial.continue': 'Continuar con',
    'trial.selected': 'Plan seleccionado:',
    'trial.then': 'Prueba gratuita de 14 días, luego',
    
    // Profile
    'profile.title': 'Perfil',
    'profile.subtitle': 'Administra la información de tu cuenta',
    'profile.subscription': 'Suscripción',
    'profile.no_subscription': 'No se encontró suscripción activa.',
    'profile.start_trial': 'Comenzar prueba gratuita',
    
    // Admin
    'admin.title': 'Panel de administración',
    'admin.subtitle': 'Administra tipos de trabajo, precios e inventario de piezas',
    'admin.job_types': 'Tipos de trabajo',
    'admin.parts_inventory': 'Piezas e inventario',
    'admin.settings': 'Configuración',
    'admin.service_types': 'Tipos de servicios',
    'admin.base_price': 'Precio base:',
    'admin.est_time': 'Tiempo estimado:',
    'admin.edit': 'Editar',
    'admin.delete': 'Eliminar',

    // Vehicle Inventory Management
    'admin.vehicle_inventory_management': 'Gestión de Inventario',
    'admin.show_syndication': 'Mostrar Sindicación',
    'admin.hide_syndication': 'Ocultar Sindicación',
    'admin.visitors': 'Visitantes',
    'admin.show_image_processing': 'Mostrar Procesamiento de Imágenes',
    'admin.hide_image_processing': 'Ocultar Procesamiento de Imágenes',
    'admin.show_ad_generator': 'Mostrar Generador de Anuncios',
    'admin.hide_ad_generator': 'Ocultar Generador de Anuncios',
    'admin.vehicles_selected': 'vehículo(s) seleccionado(s)',
    'admin.total_vehicles': 'Total de Vehículos',
    'admin.active_listings': 'Listados Activos',
    'admin.draft_listings': 'Borradores',
    'admin.sold_vehicles': 'Vehículos Vendidos',
    'admin.click_to_view_all': 'Haz clic para ver todos los vehículos',
    'admin.image_processing_pipeline': 'Pipeline de Procesamiento de Imágenes',
    'admin.batch_process_images': 'Procesar imágenes de vehículos en lote para múltiples plataformas',
    'admin.vin_to_ad_generator': 'Generador VIN-a-Anuncio',
    'admin.generate_compelling_ads': 'Generar anuncios atractivos desde VIN usando IA',

    // Dashboard
    'dashboard.title': 'Panel',
    'dashboard.subtitle': '¡Bienvenido de vuelta! Aquí está el resumen de tu negocio.',
    'dashboard.welcome_back_user': '¡Bienvenido de vuelta, {name}! Aquí está el resumen de tu negocio.',
    'dashboard.total_jobs': 'Total de trabajos',
    'dashboard.revenue': 'Ingresos',
    'dashboard.pending_jobs': 'Trabajos pendientes',
    'dashboard.customers': 'Clientes',
    'dashboard.recent_jobs': 'Trabajos recientes',

    // Jobs
    'jobs.status.completed': 'Completado',
    'jobs.status.scheduled': 'Programado',
    'jobs.status.in_progress': 'En progreso',

    // Customers
    'customers.title': 'Clientes',
    'customers.subtitle': 'Administra tu base de datos de clientes',
    'customers.new': '+ Nuevo cliente',
    'customers.search': 'Buscar clientes...',
    'customers.phone': 'Teléfono',
    'customers.address': 'Dirección',
    'customers.total_jobs': 'Total de trabajos',
    'customers.total_spent': 'Total gastado',
    'customers.last_service': 'Último servicio',
    'customers.view_history': 'Ver historial',
    'customers.edit': 'Editar',
    'customers.schedule_job': 'Programar trabajo',
    'customers.send_quote': 'Enviar cotización',
    
    // Vehicles
    'vehicles.title': 'Vehículos',
    'vehicles.subtitle': 'Administra vehículos de clientes e historial de servicio',
    'vehicles.add': '+ Agregar vehículo',
    
    // Common
    'common.loading': 'Cargando...',
    'common.save': 'Guardar',
    'common.cancel': 'Cancelar',
    'common.edit': 'Editar',
    'common.delete': 'Eliminar',
    'common.close': 'Cerrar',
    'common.search': 'Buscar',

    // Footer
    'footer.language': 'Idioma',
    'footer.darkMode': 'Modo Oscuro',

    // Sidebar
    'sidebar.darkMode': 'Modo Oscuro',
    'sidebar.language': 'Idioma',
  }
};

// Legacy translation hook for backward compatibility
export function useTranslation(language = 'en') {
  const t = (key, fallback = key) => {
    return translations[language]?.[key] || translations.en[key] || fallback;
  };

  return { t };
}
