/* About Us Page Styles */

.aboutPage {
  min-height: 100vh;
  background: var(--bg-primary);
  padding: 2rem 1rem;
  transition: background 0.3s ease;
}

/* Dark mode background */
[data-theme="dark"] .aboutPage {
  background: linear-gradient(to bottom right, #0d1117, #161b22);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  text-align: center;
  margin-bottom: 3rem;
  padding-top: 2rem;
}

.title {
  font-size: 3rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 1rem;
  transition: color 0.3s ease;
}

.brandName {
  display: inline-block;
}

.brandFaze {
  color: var(--text-primary);
}

.brandN {
  color: #e53e3e;
  font-weight: 800;
}

.brandAuto {
  color: var(--text-primary);
}

.content {
  background: var(--bg-secondary);
  border-radius: 12px;
  box-shadow: var(--card-shadow);
  padding: 3rem 2rem;
  border: 1px solid var(--border-primary);
  transition: all 0.3s ease;
}

/* Dark mode content styling */
[data-theme="dark"] .content {
  background: rgba(22, 27, 34, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.aboutContent {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.aboutText {
  margin-bottom: 3rem;
}

.description {
  font-size: 1.1rem;
  line-height: 1.8;
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
  transition: color 0.3s ease;
}

.features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.feature {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--bg-primary);
  border-radius: 8px;
  border: 1px solid var(--border-primary);
  transition: all 0.3s ease;
}

/* Dark mode feature styling */
[data-theme="dark"] .feature {
  background: rgba(13, 17, 23, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.feature:hover {
  transform: translateY(-2px);
  box-shadow: var(--card-shadow);
}

.featureIcon {
  width: 24px;
  height: 24px;
  background: #10b981;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
  flex-shrink: 0;
}

.featureText {
  font-size: 1rem;
  font-weight: 500;
  color: var(--text-primary);
  transition: color 0.3s ease;
}

/* Responsive Design */
@media (max-width: 768px) {
  .title {
    font-size: 2.5rem;
  }
  
  .content {
    padding: 2rem 1.5rem;
  }
  
  .description {
    font-size: 1rem;
  }
  
  .features {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .feature {
    padding: 0.75rem;
  }
}

@media (max-width: 480px) {
  .aboutPage {
    padding: 1rem 0.5rem;
  }
  
  .title {
    font-size: 2rem;
  }
  
  .content {
    padding: 1.5rem 1rem;
  }
}

/* RTL Support */
[dir="rtl"] .aboutPage {
  direction: rtl;
}

[dir="rtl"] .feature {
  flex-direction: row-reverse;
}

[dir="rtl"] .aboutText {
  text-align: right;
}

[dir="rtl"] .header {
  text-align: center;
}
