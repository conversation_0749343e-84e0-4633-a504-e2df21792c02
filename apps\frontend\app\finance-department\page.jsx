'use client';

import { useLanguage } from '../../contexts/LanguageContext';
import { useTheme } from '../../contexts/ThemeContext';

export default function FinanceDepartment() {
  const { t } = useLanguage();
  const { currentTheme } = useTheme();

  return (
    <div style={{ 
      minHeight: '100vh', 
      background: currentTheme === 'dark' 
        ? 'linear-gradient(to bottom right, #0d1117, #161b22)' 
        : 'var(--bg-primary)',
      color: 'var(--text-primary)',
      padding: '2rem 1rem'
    }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        <div style={{ textAlign: 'center', marginBottom: '3rem' }}>
          <h1 style={{ 
            fontSize: '3rem', 
            fontWeight: 'bold', 
            color: 'var(--text-primary)',
            marginBottom: '1rem'
          }}>
            {t('nav.finance_department')}
          </h1>
        </div>
        
        <div style={{
          background: 'var(--bg-primary)',
          border: '1px solid var(--border-primary)',
          borderRadius: '12px',
          padding: '3rem',
          boxShadow: 'var(--card-shadow)',
          marginBottom: '2rem'
        }}>
          <div style={{ textAlign: 'center', marginBottom: '3rem' }}>
            <h2 style={{ 
              fontSize: '2rem', 
              fontWeight: '600',
              color: 'var(--text-primary)',
              marginBottom: '1rem'
            }}>
              Welcome to Our Finance Department
            </h2>
            <p style={{ 
              fontSize: '1.2rem',
              color: 'var(--text-secondary)',
              lineHeight: '1.6'
            }}>
              Our experienced finance team is here to help you secure the best financing options for your vehicle purchase.
            </p>
          </div>

          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',
            gap: '3rem',
            marginBottom: '3rem'
          }}>
            <div>
              <h3 style={{ 
                fontSize: '1.5rem', 
                fontWeight: '600',
                color: 'var(--text-primary)',
                marginBottom: '1.5rem'
              }}>
                Our Services
              </h3>
              <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>
                {[
                  'Competitive interest rates',
                  'Flexible payment terms',
                  'Quick approval process',
                  'Bad credit financing available',
                  'Trade-in evaluations'
                ].map((service, index) => (
                  <li key={index} style={{
                    display: 'flex',
                    alignItems: 'flex-start',
                    marginBottom: '1rem',
                    fontSize: '1.1rem'
                  }}>
                    <span style={{ 
                      color: 'var(--accent-primary)', 
                      marginRight: '0.75rem',
                      fontSize: '1.2rem',
                      fontWeight: 'bold'
                    }}>•</span>
                    <span style={{ color: 'var(--text-primary)' }}>{service}</span>
                  </li>
                ))}
              </ul>
            </div>

            <div>
              <h3 style={{ 
                fontSize: '1.5rem', 
                fontWeight: '600',
                color: 'var(--text-primary)',
                marginBottom: '1.5rem'
              }}>
                Contact Information
              </h3>
              <div style={{ fontSize: '1.1rem', lineHeight: '1.8' }}>
                <p style={{ marginBottom: '1rem', color: 'var(--text-primary)' }}>
                  <strong>Phone:</strong> <span style={{ color: 'var(--text-secondary)' }}>(*************</span>
                </p>
                <p style={{ marginBottom: '1rem', color: 'var(--text-primary)' }}>
                  <strong>Email:</strong> <span style={{ color: 'var(--text-secondary)' }}><EMAIL></span>
                </p>
                <p style={{ marginBottom: '1rem', color: 'var(--text-primary)' }}>
                  <strong>Hours:</strong>
                </p>
                <ul style={{ 
                  listStyle: 'none', 
                  padding: 0, 
                  marginLeft: '1rem',
                  color: 'var(--text-secondary)'
                }}>
                  <li style={{ marginBottom: '0.5rem' }}>Monday - Friday: 9:00 AM - 6:00 PM</li>
                  <li style={{ marginBottom: '0.5rem' }}>Saturday: 9:00 AM - 5:00 PM</li>
                  <li>Sunday: Closed</li>
                </ul>
              </div>
            </div>
          </div>

          <div style={{ textAlign: 'center' }}>
            <p style={{ 
              color: 'var(--text-secondary)', 
              marginBottom: '2rem',
              fontSize: '1.1rem'
            }}>
              Ready to get started? Contact our finance team today!
            </p>
            <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center', flexWrap: 'wrap' }}>
              <button 
                onClick={() => window.location.href = '/financing-application'}
                style={{
                  background: 'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)',
                  color: 'white',
                  border: 'none',
                  padding: '12px 32px',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  fontWeight: '600',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease',
                  boxShadow: '0 2px 8px rgba(220, 38, 38, 0.3)'
                }}
                onMouseOver={(e) => {
                  e.target.style.background = 'linear-gradient(135deg, #b91c1c 0%, #991b1b 100%)';
                  e.target.style.transform = 'translateY(-1px)';
                  e.target.style.boxShadow = '0 4px 12px rgba(220, 38, 38, 0.4)';
                }}
                onMouseOut={(e) => {
                  e.target.style.background = 'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)';
                  e.target.style.transform = 'translateY(0)';
                  e.target.style.boxShadow = '0 2px 8px rgba(220, 38, 38, 0.3)';
                }}
              >
                Apply for Financing
              </button>
              <button 
                onClick={() => window.location.href = '/financing-calculator'}
                style={{
                  background: 'var(--bg-secondary)',
                  color: 'var(--text-primary)',
                  border: '1px solid var(--border-primary)',
                  padding: '12px 32px',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  fontWeight: '500',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease',
                  boxShadow: 'var(--card-shadow)'
                }}
                onMouseOver={(e) => {
                  e.target.style.background = 'var(--bg-tertiary)';
                  e.target.style.transform = 'translateY(-1px)';
                  e.target.style.boxShadow = 'var(--card-shadow-hover)';
                }}
                onMouseOut={(e) => {
                  e.target.style.background = 'var(--bg-secondary)';
                  e.target.style.transform = 'translateY(0)';
                  e.target.style.boxShadow = 'var(--card-shadow)';
                }}
              >
                Calculate Payment
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
