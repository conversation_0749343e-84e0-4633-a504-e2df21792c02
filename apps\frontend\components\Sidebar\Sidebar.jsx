

'use client';
import { useState } from 'react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import ThemeToggle from '../ThemeToggle/ThemeToggle';
import LanguageSelector from '../LanguageSelector/LanguageSelector';
import './Sidebar.css'; // You'll create this CSS file

const navItems = [
  { label: 'Dashboard', href: '/admin/dashboard', icon: '📊' },
  { label: 'Inventory', href: '/admin/inventory', icon: '🚗' },
  { label: 'VIN Decode', href: '/admin/vehicle-info', icon: '🔍' },
  { label: 'Upload New', href: '/admin/vehicles/upload', icon: '📤' },
  { label: 'Admin Listings', href: '/admin/vehicles/listings', icon: '📋' },
  { label: 'Ledger', href: '/admin/ledger', icon: '💰' },
  { label: 'Compliance Forms', href: '/admin/compliance-forms', icon: '📄' },
  { label: 'Facebook Setup', href: '/admin/facebook-setup', icon: '📘' },
];

export default function Sidebar({ collapsed, onToggle }) {
  const pathname = usePathname();
  const router = useRouter();
  const [showLogoutConfirm, setShowLogoutConfirm] = useState(false);

  const handleLogoutClick = () => {
    setShowLogoutConfirm(true);
  };

  const confirmLogout = () => {
    // Clear user session
    localStorage.removeItem('user');
    // Redirect to login page
    router.push('/login');
  };

  const cancelLogout = () => {
    setShowLogoutConfirm(false);
  };

  return (
    <>
      {/* Toggle Button - Always Visible */}
      <button
        className={`sidebar-toggle ${collapsed ? 'collapsed' : ''}`}
        onClick={onToggle}
        aria-label={collapsed ? 'Expand sidebar' : 'Collapse sidebar'}
      >
        {collapsed ? '»' : '«'}
      </button>

      {/* Sidebar Content - Shows icons when collapsed, full content when expanded */}
      <div className={`sidebar ${collapsed ? 'collapsed' : ''}`}>
        <div className="sidebar-header">
          {!collapsed && <h2 className="sidebar-title">Admin Portal</h2>}
        </div>

        <nav>
          <ul>
            {navItems.map(({ label, href, icon }) => (
              <li key={href} className={pathname === href ? 'active' : ''}>
                <Link href={href} title={collapsed ? label : ''}>
                  <span className="nav-icon">{icon}</span>
                  {!collapsed && <span className="nav-label">{label}</span>}
                </Link>
              </li>
            ))}
          </ul>
        </nav>

        <div className="sidebar-footer">
          {/* Theme and Language Controls */}
          <div className="sidebar-controls">
            <div className="control-item">
              <ThemeToggle showLabel={false} size="small" />
            </div>
            <div className="control-item">
              <LanguageSelector showLabel={false} showFlags={true} variant="select" collapsed={collapsed} />
            </div>
          </div>

          <button onClick={handleLogoutClick} className="logout-btn">
            {collapsed ? '🚪' : 'Logout'}
          </button>
        </div>
      </div>

      {/* Logout Confirmation Modal */}
      {showLogoutConfirm && (
        <div className="logout-modal-overlay">
          <div className="logout-modal">
            <h3>Are you sure you want to log out?</h3>
            <div className="logout-modal-buttons">
              <button onClick={confirmLogout} className="logout-confirm-btn">
                Yes
              </button>
              <button onClick={cancelLogout} className="logout-cancel-btn">
                No
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}

