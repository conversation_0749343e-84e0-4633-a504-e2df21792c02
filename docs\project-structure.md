# FazeNAuto Project Structure

This document provides a comprehensive overview of the FazeNAuto monorepo structure and organization.

## 📁 Root Directory Overview

```
app/
├── 📄 README.md                    # Project overview and quick start
├── 📄 ADMIN_SETUP.md               # Admin setup instructions
├── 📄 package.json                 # Root package.json for monorepo
├── 📄 package-lock.json            # Dependency lock file
├── 📄 amplify.yml                  # AWS Amplify deployment config
├── 📁 apps/                        # Application directories
├── 📁 packages/                    # Shared packages and utilities
├── 📁 docs/                        # Documentation
├── 📁 scripts/                     # Database and utility scripts
├── 📁 infra/                       # Infrastructure configuration
├── 📁 augment/                     # AI agent configurations
└── 📁 node_modules/                # Dependencies (auto-generated)
```

## 🚀 Applications (`/apps/`)

### Frontend Application (`/apps/frontend/`)
```
apps/frontend/
├── 📄 package.json                 # Frontend dependencies
├── 📄 next.config.js               # Next.js configuration
├── 📄 .env.local                   # Environment variables (local)
├── 📄 jsconfig.json                # Path mappings and aliases
├── 📄 middleware.js                # Global Next.js middleware
├── 📁 app/                         # Next.js App Router
│   ├── 📄 layout.js                # Root layout component
│   ├── 📄 page.js                  # Home page
│   ├── 📄 globals.css              # Global styles
│   ├── 📁 admin/                   # Admin dashboard pages
│   │   ├── 📄 page.jsx             # Admin dashboard
│   │   ├── 📄 layout.jsx           # Admin layout
│   │   ├── 📁 upload/              # Vehicle upload page
│   │   ├── 📁 compliance-forms/    # Compliance forms
│   │   ├── 📁 vehicle-info/        # Vehicle information
│   │   └── 📁 vehicles/            # Vehicle management
│   ├── 📁 api/                     # API routes
│   │   ├── 📁 auth/                # Authentication endpoints
│   │   ├── 📁 vehicles/            # Vehicle CRUD operations
│   │   │   ├── 📄 route.js         # List/create vehicles
│   │   │   ├── 📁 upload/          # Vehicle upload endpoint
│   │   │   └── 📁 [id]/            # Individual vehicle operations
│   │   ├── 📁 visitors/            # Visitor tracking endpoints
│   │   ├── 📁 compliance-forms/    # Compliance form generation
│   │   ├── 📁 image-processing/    # Image processing
│   │   └── 📁 syndication/         # Marketplace syndication
│   ├── 📁 vehicles/                # Public vehicle pages
│   │   ├── 📄 page.jsx             # Vehicle listing page
│   │   ├── 📁 upload/              # Public upload form
│   │   └── 📁 [id]/                # Vehicle detail pages
│   ├── 📁 login/                   # Authentication pages
│   ├── 📁 contact/                 # Contact page
│   └── 📁 mobile-posting/          # Mobile posting interface
├── 📁 components/                  # Reusable React components
│   ├── 📁 Navbar/                  # Navigation component
│   ├── 📁 Sidebar/                 # Admin sidebar
│   ├── 📁 VehicleCard/             # Vehicle display card
│   ├── 📁 VehicleTable/            # Vehicle data table
│   ├── 📁 VisitorTracker/          # Visitor analytics
│   ├── 📁 MobilePostingUI/         # Mobile posting interface
│   ├── 📁 ComplianceFormsPanel/    # Compliance forms
│   └── 📁 SyndicationPanel/        # Marketplace syndication
├── 📁 lib/                         # Utility libraries
│   ├── 📄 dbConnect.js             # MongoDB connection
│   ├── 📄 adGenerator.js           # Ad generation utilities
│   ├── 📄 imageProcessor.js        # Image processing
│   ├── 📄 pdfGenerator.js          # PDF generation
│   ├── 📁 models/                  # Database models
│   │   ├── 📄 Vehicle.js           # Vehicle model
│   │   ├── 📄 User.js              # User model
│   │   └── 📄 Counter.js           # Counter model
│   └── 📁 utils/                   # Utility functions
│       └── 📄 invoiceGenerator.js  # Invoice generation
├── 📁 middleware/                  # Custom middleware
│   └── 📄 trackVisitor.js          # Visitor tracking middleware
├── 📁 utils/                       # Helper functions
│   └── 📄 vinDecoder.js            # VIN decoding utilities
├── 📁 hooks/                       # Custom React hooks
│   └── 📄 useVisitorTracking.js    # Visitor tracking hook
├── 📁 contexts/                    # React contexts
│   └── 📄 SidebarContext.jsx       # Sidebar state management
├── 📁 constants/                   # Application constants
│   └── 📄 index.js                 # Navigation and app constants
├── 📁 styles/                      # Styling files
│   └── 📄 global.css               # Global styles
├── 📁 assets/                      # Static assets
│   └── 📄 flogo.png                # FazeNAuto logo
├── 📁 models/                      # Additional models
│   └── 📄 Feature.js               # Feature model
├── 📁 scripts/                     # Utility scripts
│   └── 📄 populate-features.js     # Feature population
└── 📁 public/                      # Static assets
    ├── 📁 images/                  # Image assets
    ├── 📁 icons/                   # Icon files
    └── 📄 favicon.ico              # Site favicon
```

### API Application (`/apps/api/`)
```
apps/api/
├── 📄 package.json                 # API dependencies
├── 📁 domains/                     # Domain-driven design structure
│   ├── 📁 vehicles/                # Vehicle domain
│   │   ├── 📁 models/              # Vehicle data models
│   │   ├── 📁 routes/              # Vehicle API routes
│   │   ├── 📁 services/            # Vehicle business logic
│   │   └── 📁 utils/               # Vehicle utilities
│   ├── 📁 users/                   # User domain
│   ├── 📁 compliance/              # Compliance domain
│   └── 📁 analytics/               # Analytics domain
├── 📁 lib/                         # Shared libraries
│   ├── 📄 dbConnect.js             # Database connection
│   ├── 📄 auth.js                  # Authentication
│   ├── 📄 s3.js                    # AWS S3 integration
│   └── 📄 imageProcessor.js        # Image processing
└── 📁 middleware/                  # Express middleware
    ├── 📄 auth.js                  # Authentication middleware
    ├── 📄 validation.js            # Input validation
    └── 📄 errorHandler.js          # Error handling
```

## 📦 Shared Packages (`/packages/`)

```
packages/
├── 📁 lib/                         # Shared library code
│   ├── 📄 constants.js             # Application constants
│   ├── 📄 types.js                 # TypeScript type definitions
│   └── 📄 validators.js            # Data validation functions
└── 📁 utils/                       # Utility functions
    ├── 📄 s3.js                    # S3 utility functions
    ├── 📄 formatting.js            # Data formatting utilities
    └── 📄 validation.js            # Validation helpers
```

## 📚 Documentation (`/docs/`)

```
docs/
├── 📄 README.md                    # Documentation overview
├── 📄 AMPLIFY_DEPLOYMENT_GUIDE.md  # AWS Amplify deployment
├── 📄 MARKETPLACE_INTEGRATION_GUIDE.md # Marketplace integration
├── 📄 project-structure.md         # This file
├── 📁 getting-started/             # Setup and installation
│   ├── 📄 installation.md          # Installation guide
│   ├── 📄 development.md           # Development environment
│   └── 📄 deployment.md            # Deployment guide
├── 📁 architecture/                # System architecture
│   ├── 📄 overview.md              # System overview
│   ├── 📄 aws-s3-integration.md    # S3 integration details
│   ├── 📄 database.md              # Database design
│   └── 📄 oauth.md                 # Authentication system
├── 📁 api/                         # API documentation
│   ├── 📄 routes.md                # API routes reference
│   ├── 📄 authentication.md        # Auth endpoints
│   └── 📄 vehicles.md              # Vehicle endpoints
├── 📁 components/                  # Component documentation
│   ├── 📄 forms.md                 # Form components
│   ├── 📄 dashboards.md            # Dashboard components
│   └── 📄 navigation.md            # Navigation components
├── 📁 configuration/               # Configuration guides
│   ├── 📄 env-guide.md             # Environment variables
│   └── 📄 aws-setup.md             # AWS configuration
└── 📁 development/                 # Development guides
    ├── 📄 coding-standards.md       # Code standards
    ├── 📄 testing.md               # Testing guidelines
    └── 📄 troubleshooting.md       # Common issues
```

## 🤖 AI Agent Configuration (`/augment/`)

```
augment/
├── 📁 agents/                      # AI agent configurations
│   ├── 📄 vehicle-listing-agent.json    # Vehicle management agent
│   ├── 📄 dealer-rin-agent.json         # RIN management agent
│   ├── 📄 s3-upload-agent.json          # S3 upload agent
│   └── 📄 invoice-pdf-agent.json        # PDF generation agent
└── 📁 prompts/                     # Reusable development prompts
    ├── 📄 nextjs-api-route.md      # API route generation
    ├── 📄 react-component.md       # Component generation
    ├── 📄 database-script.md       # Database script generation
    └── 📄 pdf-generator.md         # PDF generator prompts
```

## 🛠️ Scripts (`/scripts/`)

```
scripts/
├── 📄 create-user.js               # Create admin users
├── 📄 init-counters.js             # Initialize database counters
├── 📄 init-db-indexes.js           # Create database indexes
├── 📄 reset-counters.js            # Reset invoice counters
├── 📄 reset-prod-counter.js        # Reset production counters
├── 📄 seed-rins.js                 # Seed RIN data
├── 📄 test-invoice-generation.js   # Test PDF generation
├── 📄 validate-db-performance.js   # Database performance tests
└── 📄 verify-build.js              # Build verification
```

## 🏗️ Infrastructure (`/infra/`)

```
infra/
├── 📄 amplify.yml                  # AWS Amplify configuration
└── 📄 s3-bucket.tf                # Terraform S3 configuration
```

## 🗄️ Database Structure

### MongoDB Collections
```
fazenauto (database)
├── vehicles                        # Vehicle listings
├── users                          # User accounts
├── dealers                        # Dealer information
├── invoices                       # Generated invoices
├── compliance_forms               # Compliance documents
├── visitors                       # Visitor tracking
├── counters                       # Auto-increment counters
└── sessions                       # User sessions
```

### Key Data Models
- **Vehicle**: Make, model, year, VIN, images, features, pricing
- **User**: Email, role, authentication data
- **Dealer**: RIN, business info, contact details
- **Invoice**: Invoice number, buyer/seller info, vehicle details
- **Visitor**: IP address, location, visit tracking

## 🔧 Configuration Files

### Root Level
- `package.json`: Monorepo dependencies and scripts
- `amplify.yml`: AWS Amplify deployment configuration
- `.env.example`: Environment variable template

### Frontend Specific
- `next.config.js`: Next.js configuration
- `.env.local`: Local environment variables
- `tailwind.config.js`: Tailwind CSS configuration (if used)

## 🚀 Deployment Structure

### AWS Amplify
```
Amplify App
├── Frontend (Next.js)              # Main application
├── API (Serverless Functions)      # API endpoints
├── Storage (S3)                    # Image/document storage
└── Database (MongoDB Atlas)        # Data persistence
```

## 📱 Key Features by Directory

### Vehicle Management
- **Location**: `/apps/frontend/src/app/vehicles/`, `/apps/frontend/src/app/admin/`
- **Features**: CRUD operations, image uploads, search, filtering

### User Authentication
- **Location**: `/apps/frontend/src/app/api/auth/`, `/apps/frontend/src/lib/auth.js`
- **Features**: Google/Apple OAuth, role-based access

### Admin Dashboard
- **Location**: `/apps/frontend/src/app/admin/`
- **Features**: Analytics, user management, vehicle management

### Compliance Forms
- **Location**: `/apps/frontend/src/app/api/compliance/`
- **Features**: PDF generation, RIN management, invoice creation

### Image Processing
- **Location**: `/apps/frontend/src/lib/imageProcessor.js`
- **Features**: Resize, watermark, optimization, S3 upload

## 🔒 Security Considerations

### Authentication
- OAuth integration for secure login
- Role-based access control (Admin, Dealer, User)
- Session management and JWT tokens

### Data Protection
- Environment variable management
- Input validation and sanitization
- File upload security and validation

### API Security
- Rate limiting on API endpoints
- CORS configuration
- Input validation middleware

---

*This structure supports a scalable, maintainable automotive platform with clear separation of concerns and modern development practices! 🚗*
