import { NextResponse } from 'next/server';

/**
 * POST /api/facebook/setup - Setup Facebook integration
 */
export async function POST(request) {
  try {
    const body = await request.json();
    const { action, shortLivedToken, pageId } = body;

    const appId = process.env.FACEBOOK_APP_ID;
    const appSecret = process.env.FACEBOOK_APP_SECRET;

    if (!appId || !appSecret) {
      return NextResponse.json({
        success: false,
        error: 'Facebook app credentials not configured. Please add FACEBOOK_APP_ID and FACEBOOK_APP_SECRET to your .env.local file.'
      }, { status: 400 });
    }

    if (action === 'exchange-token') {
      if (!shortLivedToken) {
        return NextResponse.json({
          success: false,
          error: 'Short-lived token required'
        }, { status: 400 });
      }

      try {
        // First, let's test if the token works at all
        const testResponse = await fetch(
          `https://graph.facebook.com/v18.0/me?access_token=${shortLivedToken}`
        );

        const testData = await testResponse.json();

        if (testData.error) {
          return NextResponse.json({
            success: false,
            error: `Token test failed: ${testData.error.message}`,
            troubleshooting: [
              '1. Make sure you selected the correct app in Graph API Explorer',
              '2. Try getting a "Page Access Token" instead of "User Access Token"',
              '3. Check if your app has Marketing API permissions',
              '4. Your app may need to be reviewed by Facebook first'
            ]
          }, { status: 400 });
        }

        // Exchange short-lived token for long-lived token
        const tokenResponse = await fetch(
          `https://graph.facebook.com/v18.0/oauth/access_token?grant_type=fb_exchange_token&client_id=${appId}&client_secret=${appSecret}&fb_exchange_token=${shortLivedToken}`
        );

        const tokenData = await tokenResponse.json();

        if (tokenData.error) {
          return NextResponse.json({
            success: false,
            error: tokenData.error.message || 'Token exchange failed',
            troubleshooting: [
              '1. Your app may need Marketing API permissions',
              '2. Try using a Page Access Token directly',
              '3. Check if your app is in Development mode'
            ]
          }, { status: 400 });
        }

        // Try to get user's pages
        const pagesResponse = await fetch(
          `https://graph.facebook.com/v18.0/me/accounts?access_token=${tokenData.access_token || shortLivedToken}`
        );

        const pagesData = await pagesResponse.json();

        return NextResponse.json({
          success: true,
          longLivedToken: tokenData.access_token || shortLivedToken,
          expiresIn: tokenData.expires_in || 'Unknown',
          pages: pagesData.data || [],
          userInfo: testData,
          instructions: [
            '1. Copy the token below and add to .env.local:',
            `FACEBOOK_PAGE_TOKEN=${tokenData.access_token || shortLivedToken}`,
            '2. Restart your development server',
            '3. Test syndication from the admin panel',
            '4. If posting fails, you may need Facebook app review'
          ]
        });

      } catch (error) {
        return NextResponse.json({
          success: false,
          error: error.message
        }, { status: 500 });
      }
    }

    if (action === 'test-connection') {
      const pageToken = process.env.FACEBOOK_PAGE_TOKEN;
      const testPageId = pageId || process.env.FACEBOOK_PAGE_ID;

      if (!pageToken) {
        return NextResponse.json({
          success: false,
          error: 'FACEBOOK_PAGE_TOKEN not configured'
        }, { status: 400 });
      }

      if (!testPageId) {
        return NextResponse.json({
          success: false,
          error: 'FACEBOOK_PAGE_ID not configured'
        }, { status: 400 });
      }

      try {
        // Test the token by getting page info
        const testResponse = await fetch(
          `https://graph.facebook.com/v18.0/${testPageId}?fields=name,id,access_token&access_token=${pageToken}`
        );

        const testData = await testResponse.json();

        if (testData.error) {
          return NextResponse.json({
            success: false,
            error: testData.error.message || 'Token test failed'
          }, { status: 400 });
        }

        return NextResponse.json({
          success: true,
          message: 'Facebook connection successful!',
          pageInfo: {
            name: testData.name,
            id: testData.id
          }
        });

      } catch (error) {
        return NextResponse.json({
          success: false,
          error: error.message
        }, { status: 500 });
      }
    }

    if (action === 'manual-setup') {
      const { pageToken } = body;

      if (!pageToken) {
        return NextResponse.json({
          success: false,
          error: 'Page token required for manual setup'
        }, { status: 400 });
      }

      try {
        // Test the provided token
        const testResponse = await fetch(
          `https://graph.facebook.com/v18.0/me?access_token=${pageToken}`
        );

        const testData = await testResponse.json();

        if (testData.error) {
          return NextResponse.json({
            success: false,
            error: `Token validation failed: ${testData.error.message}`
          }, { status: 400 });
        }

        return NextResponse.json({
          success: true,
          message: 'Token validated successfully!',
          userInfo: testData,
          instructions: [
            '1. Add this to your .env.local file:',
            `FACEBOOK_PAGE_TOKEN=${pageToken}`,
            '2. Restart your development server',
            '3. Test syndication from the admin panel'
          ]
        });

      } catch (error) {
        return NextResponse.json({
          success: false,
          error: error.message
        }, { status: 500 });
      }
    }

    return NextResponse.json({
      success: false,
      error: 'Invalid action. Use: exchange-token, test-connection, or manual-setup'
    }, { status: 400 });

  } catch (error) {
    console.error('❌ Facebook setup error:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

/**
 * GET /api/facebook/setup - Get setup status
 */
export async function GET() {
  try {
    const hasAppCredentials = !!(process.env.FACEBOOK_APP_ID && process.env.FACEBOOK_APP_SECRET);
    const hasPageToken = !!process.env.FACEBOOK_PAGE_TOKEN;
    const hasPageId = !!process.env.FACEBOOK_PAGE_ID;

    return NextResponse.json({
      success: true,
      status: {
        hasAppCredentials,
        hasPageToken,
        hasPageId,
        isFullyConfigured: hasAppCredentials && hasPageToken && hasPageId
      },
      nextSteps: !hasPageToken ? [
        '1. Go to https://developers.facebook.com/tools/explorer/',
        '2. Select your app and get a User Access Token',
        '3. Add permissions: pages_manage_posts, pages_show_list',
        '4. Copy the token and use the exchange-token endpoint'
      ] : [
        'Facebook integration is configured!',
        'You can now syndicate vehicles to Facebook Marketplace'
      ]
    });

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}
