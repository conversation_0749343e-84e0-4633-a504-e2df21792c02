import { NextResponse } from 'next/server';
import { connectToDatabase } from '../../../../lib/dbConnect';
import Ledger from '../../../../lib/models/Ledger';
import PDFDocument from 'pdfkit';

// GET /api/ledger/export - Export ledger data as CSV
export async function GET(request) {
  try {
    await connectToDatabase();

    const { searchParams } = new URL(request.url);
    const format = searchParams.get('format') || 'csv';

    // Build filter object (same as main ledger route)
    const filters = {};
    
    if (searchParams.get('type')) {
      filters.type = searchParams.get('type');
    }
    
    if (searchParams.get('vehicleId')) {
      filters.vehicleId = searchParams.get('vehicleId');
    }
    
    if (searchParams.get('addedBy')) {
      filters.addedBy = { $regex: searchParams.get('addedBy'), $options: 'i' };
    }
    
    if (searchParams.get('category')) {
      filters.category = searchParams.get('category');
    }
    
    if (searchParams.get('isIncome')) {
      filters.isIncome = searchParams.get('isIncome') === 'true';
    }

    // Date range filter
    if (searchParams.get('startDate') || searchParams.get('endDate')) {
      filters.date = {};
      if (searchParams.get('startDate')) {
        filters.date.$gte = new Date(searchParams.get('startDate'));
      }
      if (searchParams.get('endDate')) {
        filters.date.$lte = new Date(searchParams.get('endDate'));
      }
    }

    // Amount range filter
    if (searchParams.get('minAmount') || searchParams.get('maxAmount')) {
      filters.amount = {};
      if (searchParams.get('minAmount')) {
        filters.amount.$gte = parseFloat(searchParams.get('minAmount'));
      }
      if (searchParams.get('maxAmount')) {
        filters.amount.$lte = parseFloat(searchParams.get('maxAmount'));
      }
    }

    // Search in description
    if (searchParams.get('search')) {
      filters.description = { $regex: searchParams.get('search'), $options: 'i' };
    }

    // Get ledger entries
    const ledgerEntries = await Ledger.find(filters)
      .populate('vehicleId', 'make model year vin')
      .sort({ date: -1, createdAt: -1 })
      .lean();

    if (format === 'csv') {
      // Generate CSV
      const csvHeaders = [
        'Date',
        'Type',
        'Description',
        'Amount',
        'Currency',
        'Vehicle',
        'VIN',
        'Added By',
        'Category',
        'Income/Expense',
        'Receipt Count',
        'Notes',
        'Created At'
      ];

      const csvRows = ledgerEntries.map(entry => [
        new Date(entry.date).toLocaleDateString('en-CA'),
        entry.type,
        `"${entry.description.replace(/"/g, '""')}"`, // Escape quotes
        entry.amount,
        entry.currency,
        entry.vehicleId ? `${entry.vehicleId.year} ${entry.vehicleId.make} ${entry.vehicleId.model}` : '',
        entry.vehicleId ? entry.vehicleId.vin : '',
        entry.addedBy,
        entry.category,
        entry.isIncome ? 'Income' : 'Expense',
        entry.receiptCount || 0,
        entry.notes ? `"${entry.notes.replace(/"/g, '""')}"` : '',
        new Date(entry.createdAt).toLocaleDateString('en-CA')
      ]);

      const csvContent = [csvHeaders.join(','), ...csvRows.map(row => row.join(','))].join('\n');

      return new NextResponse(csvContent, {
        status: 200,
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="ledger-export-${new Date().toISOString().split('T')[0]}.csv"`
        }
      });
    }

    if (format === 'pdf') {
      // Generate PDF
      const doc = new PDFDocument({ margin: 50 });
      const chunks = [];

      doc.on('data', chunk => chunks.push(chunk));
      doc.on('end', () => {
        const pdfBuffer = Buffer.concat(chunks);
        return new NextResponse(pdfBuffer, {
          status: 200,
          headers: {
            'Content-Type': 'application/pdf',
            'Content-Disposition': `attachment; filename="ledger-export-${new Date().toISOString().split('T')[0]}.pdf"`
          }
        });
      });

      // PDF Header
      doc.fontSize(20).text('FazeNAuto - Ledger Export', { align: 'center' });
      doc.fontSize(12).text(`Generated on: ${new Date().toLocaleDateString('en-CA')}`, { align: 'center' });
      doc.moveDown(2);

      // Summary
      const totalIncome = ledgerEntries.filter(e => e.isIncome).reduce((sum, e) => sum + e.amount, 0);
      const totalExpenses = ledgerEntries.filter(e => !e.isIncome).reduce((sum, e) => sum + e.amount, 0);
      const netAmount = totalIncome - totalExpenses;

      doc.fontSize(14).text('Summary:', { underline: true });
      doc.fontSize(12)
        .text(`Total Income: $${totalIncome.toFixed(2)} CAD`)
        .text(`Total Expenses: $${totalExpenses.toFixed(2)} CAD`)
        .text(`Net Amount: $${netAmount.toFixed(2)} CAD`)
        .moveDown();

      // Transactions Table
      doc.fontSize(14).text('Transactions:', { underline: true });
      doc.moveDown(0.5);

      ledgerEntries.forEach((entry, index) => {
        if (doc.y > 700) { // Start new page if needed
          doc.addPage();
        }

        const vehicleInfo = entry.vehicleId ?
          `${entry.vehicleId.year} ${entry.vehicleId.make} ${entry.vehicleId.model}` :
          'N/A';

        doc.fontSize(10)
          .text(`${index + 1}. ${new Date(entry.date).toLocaleDateString('en-CA')} - ${entry.type}`)
          .text(`   Amount: ${entry.isIncome ? '+' : '-'}$${entry.amount.toFixed(2)} ${entry.currency}`)
          .text(`   Description: ${entry.description}`)
          .text(`   Vehicle: ${vehicleInfo}`)
          .text(`   Added by: ${entry.addedBy}`)
          .moveDown(0.5);
      });

      doc.end();

      return new Promise((resolve) => {
        doc.on('end', () => {
          const pdfBuffer = Buffer.concat(chunks);
          resolve(new NextResponse(pdfBuffer, {
            status: 200,
            headers: {
              'Content-Type': 'application/pdf',
              'Content-Disposition': `attachment; filename="ledger-export-${new Date().toISOString().split('T')[0]}.pdf"`
            }
          }));
        });
      });
    }

    // Default JSON response
    return NextResponse.json({
      success: true,
      data: ledgerEntries,
      count: ledgerEntries.length
    });

  } catch (error) {
    console.error('Error exporting ledger data:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to export ledger data' },
      { status: 500 }
    );
  }
}
