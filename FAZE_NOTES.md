# 🚗 FazeNAuto - Complete Project Memory & Context

This file contains all essential project memory from Augment sessions and serves as the comprehensive technical documentation for the FazeNAuto vehicle dealership platform.

---

## 🏁 Getting Started

### Installation & Setup
```bash
# Clone and install dependencies
git clone <repository-url>
cd app

# Install all dependencies (monorepo)
npm run install-all

# Or install individually
npm install                    # Root dependencies
cd apps/frontend && npm install # Frontend dependencies
cd ../api && npm install       # Backend dependencies (if exists)
cd ../../packages/utils && npm install # Utils dependencies
```

### Running the Project
```bash
# Development server
npm run dev                    # Runs frontend dev server
cd apps/frontend && npm run dev

# Production build
npm run build
npm run start

# Linting
npm run lint
```

### Project Structure (Monorepo)
```
app/
├── apps/
│   ├── frontend/              # Next.js 15 frontend application
│   │   ├── app/              # App Router pages and API routes
│   │   ├── components/       # Reusable React components
│   │   ├── lib/             # Utilities, models, and business logic
│   │   ├── contexts/        # React contexts (Theme, Language, etc.)
│   │   ├── hooks/           # Custom React hooks
│   │   ├── utils/           # Helper utilities
│   │   ├── constants/       # Application constants
│   │   ├── locales/         # Translation files
│   │   ├── public/          # Static assets
│   │   └── styles/          # CSS modules and global styles
│   └── api/                 # Backend API (if separate)
├── packages/
│   └── utils/               # Shared utilities
├── scripts/                 # Database and utility scripts
├── docs/                    # Comprehensive documentation
└── infra/                   # Infrastructure (Terraform, Amplify)
```

**Note**: Currently a monorepo structure with frontend-focused development. Backend API routes are integrated within the Next.js app using App Router.

---

## 🌐 API Routes

All API routes use Next.js 15 App Router pattern (`/app/api/*/route.js`):

### Vehicle Management
- `POST /api/vehicles/upload` - Upload vehicle with images/video (up to 50 images)
- `GET /api/vehicles` - List vehicles with filtering/pagination
- `GET /api/vehicles/[id]` - Get single vehicle details
- `PUT /api/vehicles/[id]` - Update vehicle information
- `DELETE /api/vehicles/[id]` - Delete vehicle and associated files

### Vehicle Information & VIN Decoding
- `POST /api/vehicle-info` - NHTSA VIN decoder + recalls + MarketCheck pricing
- `GET /api/vehicle-history` - Vehicle history and recalls
- `GET /api/vehicle-attributes` - Populate vehicle attributes from VIN

### Authentication & User Management
- `POST /api/auth/login` - User login with email/password
- `POST /api/auth/register` - User registration (admin-controlled)
- `GET /api/auth/session` - Get current session

### Compliance & Finance
- `POST /api/compliance-forms` - Generate PDF forms (Bill of Sale, OMVIC, etc.)
- `GET /api/ledger` - List financial transactions
- `POST /api/ledger` - Create transaction with receipt uploads
- `GET /api/ledger/[id]` - Get transaction details
- `GET /api/ledger/export/csv` - Export transactions as CSV

### File Processing & Syndication
- `POST /api/image-processing` - Process images (watermark, resize, blur plates)
- `POST /api/ad-generator` - Generate vehicle ads with GPT-4
- `POST /api/syndication` - Auto-post to Facebook/AutoTrader/Craigslist

### Visitor Analytics
- `POST /api/visitors` - Track visitor data
- `GET /api/visitors/stats` - Get visitor analytics

### Features & Attributes
- `GET /api/features` - List vehicle features
- `POST /api/features/populate` - Populate features database
- `GET /api/rins` - List dealer RIN records
- `POST /api/rins` - Create dealer RIN record

---

## 💾 MongoDB Collections

### Primary Collections

#### `vehicles`
```javascript
{
  make: String,           // Vehicle manufacturer
  model: String,          // Vehicle model
  year: Number,           // Model year
  vin: String,            // 17-character VIN (unique)
  color: String,          // Vehicle color
  mileage: Number,        // Odometer reading (KM)
  price: Number,          // Sale price (CAD)
  engine: String,         // Engine description
  transmission: String,   // Transmission type
  drivetrain: String,     // Drive type (FWD, AWD, etc.)
  fuelType: String,       // Fuel type
  doors: Number,          // Number of doors
  images: [String],       // Array of S3 image URLs
  imageHashes: [String],  // MD5 hashes for duplicate detection
  videoUrl: String,       // S3 video URL
  features: {             // Structured features with status
    exterior: [{ value: String, status: 'included'|'not_available' }],
    interior: [{ value: String, status: 'included'|'not_available' }],
    mechanical: [{ value: String, status: 'included'|'not_available' }],
    safety: [{ value: String, status: 'included'|'not_available' }]
  },
  status: String,         // 'active', 'sold', 'draft'
  createdAt: Date,
  updatedAt: Date
}
```

#### `ledger`
```javascript
{
  vehicleId: ObjectId,    // Reference to vehicle (optional)
  date: Date,             // Transaction date
  type: String,           // 'income', 'expense'
  category: String,       // 'vehicle-purchase', 'sale', 'repair', etc.
  description: String,    // Transaction description
  amount: Number,         // Amount (CAD)
  currency: String,       // 'CAD'
  receiptUrls: [String],  // S3 URLs for receipt files
  s3Path: String,         // S3 folder path for receipts
  addedBy: String,        // User who created transaction
  notes: String,          // Additional notes
  createdAt: Date
}
```

#### `users`
```javascript
{
  email: String,          // User email (unique)
  password: String,       // Hashed password (bcrypt)
  role: String,           // 'admin', 'dealer'
  name: String,           // Full name
  isActive: Boolean,      // Account status
  createdAt: Date
}
```

#### `counters`
```javascript
{
  _id: String,            // Counter name ('test-invoice', 'prod-invoice')
  sequence: Number,       // Current sequence number
  year: Number,           // Current year
  lastReset: Date         // Last reset date
}
```

#### `visitors`
```javascript
{
  ip: String,             // Visitor IP address
  userAgent: String,      // Browser user agent
  location: {             // Geolocation data
    country: String,
    region: String,
    city: String,
    timezone: String
  },
  page: String,           // Visited page
  referrer: String,       // Referrer URL
  timestamp: Date         // Visit timestamp
}
```

### Uniqueness Constraints
- **VIN deduplication**: Vehicles are unique by VIN
- **Email uniqueness**: Users are unique by email
- **Invoice numbering**: Counters ensure unique invoice numbers
- **Image hashing**: Duplicate images detected by MD5 hash

---

## 🖼️ S3 Buckets

### Vehicle Images Bucket: `fazenauto-vehicle-images`
```
fazenauto-vehicle-images/
├── vehicles/
│   ├── toyota/
│   │   ├── camry/
│   │   │   ├── 2023/
│   │   │   │   ├── {uuid}.jpg
│   │   │   │   ├── {uuid}.jpg
│   │   │   │   └── videos/
│   │   │   │       └── {uuid}.mp4
│   │   │   └── 2024/
│   │   └── corolla/
│   └── honda/
│       └── civic/
└── processed/
    └── vehicles/
        └── {vehicleId}/
            ├── facebook_1_timestamp.jpg
            ├── autotrader_1_timestamp.jpg
            └── kijiji_1_timestamp.jpg
```

### Ledger Documents Bucket: `fazenauto-ledger-documents`
```
fazenauto-ledger-documents/
├── receipts/
│   ├── vehicle-purchase/
│   │   └── 2025-01/
│   │       └── {transactionId}/
│   │           ├── {uuid}.pdf
│   │           ├── {uuid}.jpg
│   │           └── metadata.json
│   ├── vehicle-repair/
│   ├── sale/
│   └── general-cost/
```

### File Naming Conventions
- **Vehicle Images**: `vehicles/{make}/{model}/{year}/{uuid}.{ext}`
- **Vehicle Videos**: `vehicles/{make}/{model}/{year}/videos/{uuid}.{ext}`
- **Processed Images**: `vehicles/{vehicleId}/processed/{platform}_{index}_{timestamp}.{ext}`
- **Ledger Receipts**: `receipts/{category}/{year-month}/{transactionId}/{uuid}.{ext}`
- **Metadata Files**: JSON files with transaction/upload metadata

---

## ⚙️ Scripts

```json
{
  "scripts": {
    "dev": "cd apps/frontend && npm run dev",
    "build": "cd apps/frontend && npm run build", 
    "start": "cd apps/frontend && npm run start",
    "lint": "cd apps/frontend && npm run lint",
    "init-indexes": "node scripts/init-db-indexes.js",
    "validate-indexes": "node scripts/validate-db-performance.js",
    "install-all": "npm install && cd apps/frontend && npm install && cd ../api && npm install && cd ../../packages/utils && npm install",
    "verify-build": "node scripts/verify-build.js"
  }
}
```

### Database Scripts
- `scripts/init-db-indexes.js` - Initialize MongoDB indexes for performance
- `scripts/init-counters.js` - Initialize invoice counter collections
- `scripts/reset-counters.js` - Reset invoice counters (test mode)
- `scripts/reset-prod-counter.js` - Reset production invoice counter
- `scripts/seed-rins.js` - Seed dealer RIN records
- `scripts/create-user.js` - Create admin/dealer users
- `scripts/validate-db-performance.js` - Validate database performance
- `scripts/test-invoice-generation.js` - Test invoice number generation

---

## 🔐 Environment Variables

### Required Variables (`.env.local`)
```bash
# Database
MONGODB_URI=mongodb+srv://user:<EMAIL>/fazenauto
MONGO_URI=mongodb+srv://user:<EMAIL>/fazenauto  # Fallback

# AWS S3 Configuration
CUSTOM_AWS_ACCESS_KEY_ID=your_access_key
CUSTOM_AWS_SECRET_ACCESS_KEY=your_secret_key
CUSTOM_AWS_REGION=us-east-1
CUSTOM_AWS_S3_BUCKET_NAME=fazenauto-vehicle-images

# Authentication
ADMIN_SECRET=TestSeceret123
AUTHORIZED_EMAILS=<EMAIL>,<EMAIL>

# NextAuth (if using)
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_nextauth_secret

# Application Settings
NEXT_PUBLIC_APP_URL=https://fazenauto.com
NEXT_PUBLIC_PHONE=************
NEXT_PUBLIC_ADDRESS=1120 Meighen Way

# External APIs
MARKETCHECK_API_KEY=FMFLQmNE8ixrzROCZc80bCmZiUJv2sMF
MARKETCHECK_API_SECRET=uNmqH5e3sZwjRXN2
NHTSA_API_URL=https://vpic.nhtsa.dot.gov/api

# Analytics
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-JBNQD8SDQ1

# Social Media
FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_app_secret
FACEBOOK_PAGE_ID=61577486330108

# Development
NODE_ENV=development
DEBUG=true
```

### Environment-Specific Notes
- **Development**: Use test credentials and local MongoDB
- **Production**: Use production AWS bucket and MongoDB Atlas
- **MarketCheck API**: Limited to 500 free calls, dealer-only access
- **NHTSA API**: Free public API, no key required

---

## 📚 Translation System

### Implementation
- **Library**: `i18next`, `react-i18next`, `i18next-browser-languagedetector`
- **Source**: `/lib/translations.js` (legacy compatibility)
- **Output**: `/locales/{lang}/translation.json`
- **Languages**: English (en), Spanish (es), French (fr), Urdu (ur), Arabic (ar)

### File Structure
```
apps/frontend/locales/
├── en/translation.json
├── es/translation.json
├── fr/translation.json
├── ur/translation.json
└── ar/translation.json
```

### Translation Keys (Dot Notation)
```json
{
  "nav": {
    "pricing": "Pricing",
    "inventory": "Inventory",
    "contact": "Contact"
  },
  "settings": {
    "title": "Settings",
    "language": "Language",
    "theme": "Theme"
  },
  "vehicle": {
    "details": "Vehicle Details",
    "features": "Features",
    "price": "Price"
  }
}
```

### Usage in Components
```javascript
import { useTranslation } from 'react-i18next';

function Component() {
  const { t } = useTranslation();
  return <h1>{t('nav.pricing')}</h1>;
}
```

### Adding New Languages
1. Create `/locales/{lang}/translation.json`
2. Add language to `lib/translations.js` import map
3. Add language option to `LanguageSelector` component
4. Update language detection in `LanguageContext`

### Translation Conversion Script
- `scripts/convertTranslations.js` - Convert JS translations to JSON format
- Hybrid system supports both JS and JSON translation files
- FOUC prevention implemented application-wide

---

## 👤 Authentication

### System Overview
- **Provider**: Custom email/password authentication
- **Storage**: MongoDB users collection with bcrypt hashing
- **Session**: localStorage-based session management
- **Access Control**: Role-based (admin, dealer)

### User Roles
- **Admin**: Full system access, user management, all features
- **Dealer**: Vehicle upload, inventory management, compliance forms
- **Public**: Vehicle browsing, VIN lookup, contact forms

### Authentication Flow
1. **Login**: `POST /api/auth/login` with email/password
2. **Validation**: Check against authorized emails list
3. **Session**: Store user data in localStorage
4. **Protection**: `ProtectedRoute` component for admin pages
5. **Logout**: Clear localStorage and redirect

### User Creation
- **No Public Registration**: Users cannot self-register
- **Admin-Controlled**: Use `/admin-create-user` interface
- **Authorized Emails**: Only pre-approved emails can access system
- **Secure Creation**: Uses `ADMIN_SECRET` for validation

### Session Management
```javascript
// Check authentication
import { getCurrentUser, isAuthenticated } from '@/lib/auth';

const user = getCurrentUser();
if (isAuthenticated()) {
  // User is logged in
}
```

---

## 📦 Image & Form Uploads

### Vehicle Image Upload
- **Endpoint**: `POST /api/vehicles/upload`
- **Limit**: 50 images + 1 video per vehicle
- **Processing**: Automatic watermarking, resizing, optimization
- **Storage**: Structured S3 paths by make/model/year
- **Deduplication**: MD5 hash-based duplicate detection

### Upload Flow
1. **Frontend Form**: `multipart/form-data` with vehicle details + files
2. **Backend Processing**: Extract form data and files
3. **File Validation**: Check file types, sizes, limits
4. **S3 Upload**: Upload to structured paths with UUIDs
5. **Database Storage**: Save URLs and metadata to MongoDB
6. **Response**: Return vehicle record with image URLs

### Image Processing Pipeline
- **Watermarking**: Add FazeNAuto watermark to images
- **License Plate Blurring**: Automatic license plate detection and blurring
- **Resizing**: Generate thumbnails and optimized sizes
- **Format Conversion**: Convert to web-optimized formats

### PDF417 Scanner & OCR
- **VIN Scanning**: Camera-based VIN scanning from windshield
- **License Scanning**: Extract dealer license information
- **Real-time Processing**: Live camera feed with overlay
- **OCR Integration**: Text extraction from scanned documents

### VIN Decoder Integration
- **NHTSA API**: Free VIN decoding service
- **MarketCheck API**: Pricing data (dealer-only, 500 calls/month)
- **Recall Data**: Vehicle recall information
- **Auto-population**: Automatically fill vehicle details from VIN
- **Additional Fields**: Plant City, Plant Country, Drive Type, Engine specs

---

## 🧾 Compliance & Finance System

### Compliance Forms
- **Bill of Sale**: Official vehicle sale document with invoice numbering
- **OMVIC Disclosure**: Mandatory Ontario dealer disclosure form
- **Consignment Agreement**: Vehicle consignment contracts
- **HST Reminder**: Tax compliance reminders

### Invoice Numbering System
- **Format**: `BOS-YYYY-#####` (e.g., `BOS-2025-00001`)
- **Modes**: Test and Production counters
- **Auto-increment**: MongoDB counter collection
- **Year Reset**: Automatic reset on year change
- **Manual Fallback**: Manual entry option if auto-generation fails

### PDF Generation
- **Library**: PDFKit for server-side PDF generation
- **Templates**: Structured templates for each form type
- **Dynamic Data**: Vehicle, buyer, seller, dealer information
- **Branding**: FazeNAuto branding with red 'N' accent
- **File**: `lib/pdfGenerator.js` - Main PDF generation utility

### Ledger System
- **Transaction Tracking**: Income/expense tracking by category
- **Receipt Storage**: S3 storage with metadata
- **Vehicle Association**: Link transactions to specific vehicles
- **Export Features**: CSV and PDF export capabilities
- **Categories**: vehicle-purchase, sale, repair, general-cost

### RIN (Registration Identification Number) Logic
- **Dealer Information**: Store dealer registration details
- **Compliance Forms**: Auto-populate dealer info in forms
- **OMVIC Integration**: Track OMVIC numbers and licenses
- **Reusable Logic**: Modular system for multiple compliance forms
- **Database**: `rins` collection with business details

---

## 🌓 Theme & UI

### Dark/Light Mode Implementation
- **Context**: `ThemeContext` with localStorage persistence
- **CSS Variables**: Theme-based color system
- **Default**: Light mode as default theme
- **Toggle**: Footer-based theme toggle component
- **SSR Safe**: Prevents FOUC (Flash of Untranslated Content)

### CSS Variables System
```css
:root {
  /* Light mode */
  --background: #ffffff;
  --foreground: #000000;
  --border: #e5e5e5;
}

[data-theme="dark"] {
  /* Dark mode */
  --background: #0d1117;
  --foreground: #ffffff;
  --border: #30363d;
}
```

### UI Design Patterns
- **Full-width Layouts**: No card containers, full-width sections
- **Gradient Backgrounds**: `linear-gradient(to bottom right, #0d1117, #161b22)` in dark mode
- **Solid Backgrounds**: `#ffffff` in light mode
- **Form Styling**: Global `.formInput` class with consistent styling
- **Chip-style Features**: Modern feature selection UI with uniform icons
- **Mobile-first**: Responsive design with mobile hamburger menu

### Component Architecture
- **Reusable Components**: Modular component system in `/components`
- **Context Providers**: Theme, Language, Sidebar, Compare contexts
- **Custom Hooks**: `useAnalytics`, `useVisitorTracking`
- **Protected Routes**: Role-based route protection

### Navigation & Layout
- **Mobile Menu**: Hamburger menu for non-logged-in users
- **User Dropdown**: Logged-in users see user dropdown with image
- **Responsive Sidebar**: Auto-closes below 768px
- **Footer Controls**: Theme toggle and language selector in footer

### Branding Guidelines
- **FazeNAuto**: All white text except red 'N'
- **No Underlines**: Brand names without underlines
- **Contact Info**: Phone ************, Address 1120 Meighen Way
- **Social Links**: TikTok, Twitter, Facebook business page

---

## 🚀 Advanced Features & Integrations

### Syndication System
- **Auto-posting**: Facebook, AutoTrader, Craigslist, Kijiji
- **Priority**: Facebook & Auto Trader focus
- **API Integration**: Facebook Graph API for marketplace posting
- **Content Generation**: GPT-4 powered ad generation

### VIN-to-Ad Generator
- **GPT-4 Integration**: Automatic ad copy generation
- **Image Processing**: Auto-resize, watermarking, license plate blurring
- **Template System**: Customizable ad templates
- **Multi-platform**: Generate ads for different platforms

### Analytics & Visitor Tracking
- **Google Analytics**: GA4 implementation with measurement ID G-JBNQD8SDQ1
- **Visitor Tracking**: IP-based visitor analytics
- **Geolocation**: Country, region, city tracking
- **Page Analytics**: Track page visits and user behavior
- **SSR-safe**: Analytics utilities in `/lib/analytics.js`

### Vehicle Features & Attributes
- **Structured Features**: Object-based features with status tracking
- **Categories**: Exterior, Interior, Mechanical, Safety
- **Status Tracking**: 'included' vs 'not_available'
- **Auto-population**: VIN-based feature detection

### Marketplace Integration
- **Facebook Marketplace**: Business page integration
- **AutoTrader**: Listing syndication
- **Kijiji**: Automated posting
- **Craigslist**: Cross-posting capabilities

---

## 🔧 Development Tools & Utilities

### Key Utilities
- **VIN Decoder**: `utils/vinDecoder.js` - NHTSA API integration
- **Image Processor**: `lib/imageProcessor.js` - Watermarking and optimization
- **PDF Generator**: `lib/pdfGenerator.js` - Compliance form generation
- **Analytics Helper**: `lib/analytics.js` - GA4 and tracking utilities
- **Market Check**: `lib/marketcheck.js` - Pricing data integration

### Database Utilities
- **Connection**: `lib/dbConnect.js` - MongoDB connection management
- **Models**: Mongoose schemas in `/lib/models/`
- **Indexes**: Performance optimization scripts
- **Seeding**: Data population scripts

### Form Handling
- **Admin Forms**: Consistent styling across all admin pages
- **Validation**: Client and server-side validation
- **File Uploads**: Multi-file upload with progress tracking
- **Error Handling**: Comprehensive error management

### Testing & Validation
- **Ledger Tests**: `tests/ledger.test.js`
- **Performance Validation**: Database performance scripts
- **Build Verification**: Automated build testing

---

## 🎯 Business Context & Terminology

### Business Information
- **Owner**: Faisal N Chaudhry (one-person operation)
- **Roles**: President/Salesman/Admin/Operations
- **Sales Model**: Cars sold 'as-is' without inspection (first year)
- **Contact**: ************, 1120 Meighen Way

### Social Media Presence
- **Facebook Business**: Page ID 61577486330108
- **Personal Profile**: 'Faze N Outo' for marketplace activities
- **TikTok**: @fazenauto
- **Twitter**: @FazeNAuto

### Terminology Preferences
- **VIN Decode**: Preferred over 'Vehicle Information Lookup'
- **KM/Odometer**: Instead of 'Mileage'
- **Used Vehicles**: Consistent terminology
- **Dealer vs Admin**: Role-based terminology

### Vehicle Status Management
- **Active**: Visible to customers
- **Sold**: Large 'SOLD' overlay, archived
- **Draft**: Hidden from customer-facing pages
- **Archive**: Sold vehicle tracking

---

## 🧠 Cursor Integration Note

**This file contains all essential project memory from Augment sessions.**

When working with this project in Cursor or any other IDE:

1. **Use this file as your primary context** - It contains all the technical details, patterns, and decisions made during development

2. **Reference the memories section** - User preferences and business requirements are documented in the project memories

3. **Follow established patterns** - The codebase has consistent patterns for:
   - API routes using Next.js App Router
   - Component structure and naming
   - Database schema and relationships
   - File upload and S3 integration
   - Authentication and authorization

4. **Respect the architecture** - Monorepo structure with domain-driven organization

5. **Check environment variables** - Ensure all required variables are set before development

6. **Use existing utilities** - Many common functions are already implemented:
   - VIN decoding and vehicle information lookup
   - Image processing and watermarking
   - PDF generation for compliance forms
   - Translation and internationalization
   - Theme management and UI components

7. **Package Management** - Always use package managers (npm) instead of manually editing package.json

8. **Testing Approach** - Write tests for new features and run existing tests to ensure compatibility

This documentation should provide everything needed to understand, maintain, and extend the FazeNAuto platform without requiring previous context from Augment sessions. The project is well-structured with comprehensive documentation, established patterns, and a clear separation of concerns.
