#!/usr/bin/env node

/**
 * Reset Invoice Counters Script
 * Deletes existing counters and recreates them from scratch
 * 
 * Usage:
 *   node scripts/reset-counters.js
 */

import dotenv from 'dotenv';
import { connectToDatabase } from '../apps/api/lib/dbConnect.js';
import Counter from '../apps/frontend/src/lib/models/Counter.js';

// Load environment variables from frontend directory
dotenv.config({ path: '../apps/frontend/.env.local' });

async function resetCounters() {
  try {
    console.log('🗑️ Resetting invoice counters...');
    
    // Connect to database
    await connectToDatabase();
    
    // Delete existing counters
    const deleteResult = await Counter.deleteMany({
      _id: { $in: ['billOfSale_test', 'billOfSale_prod'] }
    });
    
    console.log(`🗑️ Deleted ${deleteResult.deletedCount} existing counters`);
    
    // Create fresh counters starting at 0
    const currentYear = new Date().getFullYear();
    
    const testCounter = await Counter.create({
      _id: 'billOfSale_test',
      year: currentYear,
      sequenceValue: 0
    });
    
    const prodCounter = await Counter.create({
      _id: 'billOfSale_prod',
      year: currentYear,
      sequenceValue: 0
    });
    
    console.log('✅ Created fresh counters:');
    console.log(`   Test: ${testCounter._id} (sequence: ${testCounter.sequenceValue})`);
    console.log(`   Prod: ${prodCounter._id} (sequence: ${prodCounter.sequenceValue})`);
    
    console.log('\n🎯 Next invoice numbers will be:');
    console.log(`   Test Mode: BOS-${currentYear}-00001`);
    console.log(`   Prod Mode: BOS-${currentYear}-00001`);
    
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Error resetting counters:', error);
    process.exit(1);
  }
}

// Run the reset
resetCounters();
