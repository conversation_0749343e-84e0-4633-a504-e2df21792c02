# AWS Amplify Deployment Guide - FazeNAuto Monorepo

This guide covers deploying the FazeNAuto monorepo to AWS Amplify after the refactoring.

## 🚀 Quick Fix for Current Issue

The build error you're experiencing is due to Node.js version compatibility. Here's the immediate solution:

### 1. Node.js Version Issue
**Problem**: Amplify is using Node.js v18.20.8, but npm@11.4.2 requires Node.js >=20.17.0

**Solution**: The updated `amplify.yml` now specifies Node.js 20:
```yaml
version: 1
frontend:
  phases:
    preBuild:
      commands:
        - nvm use 20  # This forces Node.js 20
        - node --version
        - npm --version
        - echo "Using Node.js $(node --version) and npm $(npm --version)"
        - cd apps/frontend
        - npm ci
```

### 2. Files Updated
- ✅ `amplify.yml` - Updated with Node.js 20 requirement
- ✅ `.nvmrc` - Created to specify Node.js version
- ✅ `apps/frontend/.nvmrc` - Frontend-specific Node.js version
- ✅ `package.json` - Added engines specification
- ✅ `apps/frontend/package.json` - Added engines specification

## 📋 Deployment Checklist

### Pre-Deployment
- [ ] Commit all changes to your repository
- [ ] Ensure `amplify.yml` is in the root directory
- [ ] Verify `.nvmrc` files are present
- [ ] Test build locally with `npm run verify-build`

### Amplify Console Settings
1. **Build Settings**: Amplify should automatically detect the `amplify.yml`
2. **Environment Variables**: Add these in Amplify Console:
   ```
   MONGO_URI=your_mongodb_connection_string
   CUSTOM_AWS_ACCESS_KEY_ID=your_access_key
   CUSTOM_AWS_SECRET_ACCESS_KEY=your_secret_key
   CUSTOM_AWS_REGION=us-east-1
   CUSTOM_AWS_S3_BUCKET_NAME=fazenauto-vehicle-images
   ADMIN_SECRET=your_admin_secret
   AUTHORIZED_EMAILS=<EMAIL>,<EMAIL>
   FACEBOOK_APP_ID=your_facebook_app_id
   FACEBOOK_APP_SECRET=your_facebook_app_secret
   FACEBOOK_PAGE_ID=your_facebook_page_id
   ```

3. **Build Image**: Use the default build image (Amazon Linux 2023)

## 🔧 Troubleshooting Common Issues

### Issue 1: Node.js Version Mismatch
**Error**: `npm error engine Unsupported engine`
**Solution**: 
- Ensure `amplify.yml` has `nvm use 20`
- Check that `.nvmrc` contains `20`
- Verify `engines` in package.json specifies `"node": ">=20.17.0"`

### Issue 2: Module Not Found Errors
**Error**: `Module not found: Can't resolve '@/models/Vehicle'`
**Solution**: 
- Verify `jsconfig.json` has correct path mappings
- Ensure all import statements use the new domain structure
- Check that all model files exist in `apps/api/domains/*/models/`

### Issue 3: Environment Variables Not Found
**Error**: `MONGO_URI is not defined`
**Solution**:
- Add all environment variables in Amplify Console
- Ensure `.env.local` exists in `apps/frontend/`
- Verify variable names match exactly

### Issue 4: Build Directory Not Found
**Error**: `baseDirectory: apps/frontend/.next not found`
**Solution**:
- Ensure build commands run from correct directory
- Verify `cd apps/frontend` is in preBuild phase
- Check that Next.js build completes successfully

## 🏗️ Build Process Explanation

### What Happens During Build:
1. **Environment Setup**: Node.js 20 is activated
2. **Dependency Installation**: `npm ci` in `apps/frontend/`
3. **Next.js Build**: `npm run build` creates `.next` directory
4. **Artifact Collection**: Amplify packages the `.next` directory

### Build Output Structure:
```
apps/frontend/.next/          # Next.js build output
├── static/                   # Static assets
├── server/                   # Server-side code
└── ...                       # Other Next.js files
```

## 🔍 Local Testing

Before deploying, test the build locally:

```bash
# Verify build environment
npm run verify-build

# Test manual build
cd apps/frontend
npm ci
npm run build
npm run start
```

## 📊 Performance Considerations

### Build Optimization:
- **Caching**: Node modules are cached between builds
- **Incremental Builds**: Only changed files are rebuilt
- **Asset Optimization**: Next.js optimizes images and assets

### Runtime Optimization:
- **API Routes**: Handled by Next.js serverless functions
- **Static Assets**: Served from Amplify CDN
- **Database**: MongoDB Atlas with connection pooling

## 🔒 Security Notes

### Environment Variables:
- Never commit `.env.local` files to git
- Use Amplify Console for production environment variables
- Rotate AWS credentials regularly

### Access Control:
- Admin routes are protected by authentication
- API endpoints validate user permissions
- S3 bucket has appropriate CORS settings

## 📈 Monitoring & Debugging

### Amplify Console:
- **Build Logs**: Check for detailed error messages
- **Function Logs**: Monitor API route performance
- **Metrics**: Track build times and success rates

### Application Monitoring:
- **Database Performance**: Monitor MongoDB Atlas metrics
- **S3 Usage**: Track storage and bandwidth usage
- **Error Tracking**: Implement error logging for production

## 🚀 Deployment Commands

### Initial Deployment:
```bash
# Commit changes
git add .
git commit -m "fix: Update Amplify configuration for monorepo deployment"
git push origin main
```

### Redeploy After Changes:
```bash
# Test locally first
npm run verify-build

# Deploy
git add .
git commit -m "feat: Your change description"
git push origin main
```

## 📞 Support

If you continue to experience issues:

1. **Check Build Logs**: Look for specific error messages in Amplify Console
2. **Test Locally**: Use `npm run verify-build` to identify issues
3. **Environment Variables**: Verify all required variables are set
4. **Node.js Version**: Ensure Amplify is using Node.js 20

The monorepo structure is fully compatible with Amplify - the key is ensuring the correct Node.js version and proper path configurations.
