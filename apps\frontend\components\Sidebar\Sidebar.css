
/* Sidebar Toggle Button - Always Visible */
.sidebar-toggle {
  background: #374151;
  color: white;
  border: none;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  left: 220px; /* Position touching expanded sidebar */
  top: 50%;
  transform: translateY(-50%);
  z-index: 1001;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.sidebar-toggle:hover {
  background: #4b5563;
  transform: translateY(-50%) scale(1.1);
}

.sidebar-toggle.collapsed {
  left: 60px; /* Position touching collapsed sidebar */
  animation: pulse 2s infinite;
}

/* Default pulse animation (gray) */
@keyframes pulse {
  0%, 100% {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }
  50% {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 4px rgba(55, 65, 81, 0.3);
  }
}

/* Light mode pulse animation (gray with subtle red hint) */
[data-theme="light"] .sidebar-toggle.collapsed {
  animation: pulse-light 2s infinite;
}

@keyframes pulse-light {
  0%, 100% {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }
  50% {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 4px rgba(55, 65, 81, 0.3), 0 0 0 4px rgba(231, 76, 60, 0.1);
  }
}

/* Dark mode pulse animation (gray with subtle blue hint) */
[data-theme="dark"] .sidebar-toggle.collapsed {
  animation: pulse-dark 2s infinite;
}

@keyframes pulse-dark {
  0%, 100% {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }
  50% {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 4px rgba(55, 65, 81, 0.3), 0 0 0 4px rgba(99, 179, 237, 0.1);
  }
}

/* Sidebar Content - Fixed width and consistent dark blue background */
.sidebar {
  width: 220px;
  height: 100vh;
  background-color: #111827 !important;
  color: white;
  padding: 1.5rem 1rem;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 1000;
  transition: width 0.3s ease, padding 0.3s ease;
  overflow: hidden;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

/* Ensure consistent dark blue background in both light and dark modes */
[data-theme="light"] .sidebar,
[data-theme="dark"] .sidebar {
  background-color: #111827 !important;
  color: white !important;
}

.sidebar.collapsed {
  width: 60px;
  padding: 1.5rem 0.5rem;
}

.sidebar-header {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 2rem;
}

.sidebar.collapsed .sidebar-header {
  justify-content: center;
}

.sidebar-title {
  font-size: 1.25rem;
  font-weight: bold;
  white-space: nowrap;
}



.sidebar nav ul {
  list-style: none;
  padding: 0;
}

.sidebar nav ul li {
  margin-bottom: 1rem;
}

.sidebar nav ul li a {
  color: #d1d5db;
  text-decoration: none;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  white-space: nowrap;
  overflow: hidden;
  text-align: left;
}

.sidebar.collapsed nav ul li a {
  justify-content: center;
  gap: 0;
  padding: 0.75rem 0.25rem;
}

.nav-icon {
  font-size: 1.2rem;
  min-width: 1.2rem;
  text-align: center;
}

.nav-label {
  flex: 1;
}

.sidebar nav ul li.active a,
.sidebar nav ul li a:hover {
  color: white;
  font-weight: bold;
  background-color: #374151;
}

.sidebar-footer {
  position: absolute;
  bottom: 1.5rem;
  left: 1rem;
  right: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.sidebar.collapsed .sidebar-footer {
  left: 0.5rem;
  right: 0.5rem;
}



.logout-btn {
  width: 100%;
  background: #dc2626;
  color: white;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-size: 0.9rem;
  white-space: nowrap;
  overflow: hidden;
  text-align: center;
}

.sidebar.collapsed .logout-btn {
  font-size: 1.2rem;
  padding: 0.75rem 0.25rem;
}

.logout-btn:hover {
  background: #b91c1c;
}

/* Sidebar Controls */
.sidebar-controls {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding: 1rem 0.75rem;
  border-top: 1px solid #374151;
  margin-bottom: 0.5rem;
}

.sidebar.collapsed .sidebar-controls {
  align-items: center;
  padding: 1rem 0.5rem;
}

.control-item {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.sidebar.collapsed .control-item {
  justify-content: center;
}

/* Adjust controls for collapsed state */
.sidebar.collapsed .sidebar-controls {
  gap: 0.75rem;
  padding: 1rem 0.5rem;
}

/* Logout Confirmation Modal */
.logout-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  backdrop-filter: blur(4px);
}

.logout-modal {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  max-width: 400px;
  width: 90%;
  text-align: center;
  animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.logout-modal h3 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.2rem;
  font-weight: 600;
}

.logout-modal-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.logout-confirm-btn,
.logout-cancel-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 80px;
}

.logout-confirm-btn {
  background: #dc2626;
  color: white;
}

.logout-confirm-btn:hover {
  background: #b91c1c;
  transform: translateY(-1px);
}

.logout-cancel-btn {
  background: #6b7280;
  color: white;
}

.logout-cancel-btn:hover {
  background: #4b5563;
  transform: translateY(-1px);
}

/* Responsive Design - Consistent width and styling across all desktop screen sizes */

/* Desktop screens (768px and above) - Maintain consistent 220px width and styling */
@media (min-width: 768px) {
  .sidebar {
    width: 220px;
    background-color: #111827;
    padding: 1.5rem 1rem;
  }

  .sidebar-title {
    font-size: 1.25rem;
    font-weight: bold;
  }

  .sidebar nav ul li a {
    font-size: 1rem;
    padding: 0.5rem;
    font-weight: 500;
  }
}

/* Mobile Responsive Design - Force sidebar to be hidden and overlay when open */
@media (max-width: 767px) {
  .sidebar {
    width: 280px; /* Wider on mobile for better touch interaction */
    transform: translateX(-100%); /* Always start hidden on mobile */
    z-index: 1500; /* Higher z-index to overlay content */
    box-shadow: 4px 0 20px rgba(0, 0, 0, 0.3); /* Stronger shadow for overlay effect */
  }

  .sidebar:not(.collapsed) {
    transform: translateX(0); /* Show sidebar when not collapsed */
    width: 280px; /* Full width on mobile when expanded */
    padding: 1.5rem 1rem; /* Full padding on mobile */
  }

  .sidebar.collapsed {
    transform: translateX(-100%); /* Hide sidebar when collapsed on mobile */
    pointer-events: none;
  }

  /* Toggle button remains visible on mobile */
  .sidebar-toggle {
    display: flex;
    z-index: 1600; /* Above sidebar */
    left: 12px; /* Reset to original position on mobile */
  }

  .sidebar-toggle.collapsed {
    left: 12px; /* Reset to original position on mobile */
  }

  /* Mobile admin content adjustments */
  .admin-content {
    margin-left: 0 !important;
    width: 100% !important;
  }
}


