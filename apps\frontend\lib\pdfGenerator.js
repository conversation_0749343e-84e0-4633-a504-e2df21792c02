import fs from 'fs';
import path from 'path';

// Conditional import for PDFKit (server-side only)
let PDFDocument;
try {
  if (typeof window === 'undefined') {
    PDFDocument = require('pdfkit');
  }
} catch (error) {
  console.warn('PDFKit not available, using mock implementation');
}

/**
 * PDF Generator for Dealership Compliance Forms
 * Generates Bill of Sale, UVIP, OMVIC disclosure, and tax forms
 */
export class PDFGenerator {
  constructor() {
    this.dealerInfo = {
      name: 'FazeNAuto',
      address: '1120 Meighen Way',
      phone: '************',
      email: '<EMAIL>',
      omvicNumber: 'PENDING', // You'll need to get this when registered
      hstNumber: 'PENDING'    // You'll need to get this for tax purposes
    };

    // Check if PDFKit is available
    this.isAvailable = typeof window === 'undefined' && PDFDocument;

    if (!this.isAvailable) {
      console.warn('PDFGenerator: PDFKit not available, using mock implementation');
    }
  }

  /**
   * Generate Bill of Sale PDF
   * @param {Object} saleData - Sale transaction data
   * @returns {Promise<Buffer>} - PDF buffer
   */
  async generateBillOfSale(saleData) {
    const {
      vehicle,
      buyer,
      dealer,
      dealerInfo,
      seller,
      salePrice,
      purchaseAmount, // Support both field names
      saleDate = new Date(),
      paymentMethod = 'Cash',
      tradeInValue = 0,
      taxes = {},
      warranties = [],
      certification = 'as-is',
      disclaimer,
      buyerSignature,
      sellerSignature,
      invoiceNumber // Pre-generated invoice number
    } = saleData;

    // Use dynamic dealer info from form, fallback to static info
    const finalDealerInfo = dealer || dealerInfo || seller || this.dealerInfo;

    // Use purchaseAmount if salePrice is not provided
    const finalSalePrice = salePrice || purchaseAmount || 0;

    // Return mock PDF if PDFKit not available
    if (!this.isAvailable) {
      return this.generateMockPDF('Bill of Sale', { vehicle, buyer, salePrice: finalSalePrice });
    }

    const doc = new PDFDocument({ margin: 50 });
    const chunks = [];

    doc.on('data', chunk => chunks.push(chunk));

    return new Promise((resolve, reject) => {
      doc.on('end', () => resolve(Buffer.concat(chunks)));
      doc.on('error', reject);

      // Page setup
      const margin = 40;
      const pageWidth = doc.page.width - 2 * margin;
      const pageHeight = doc.page.height - 2 * margin;
      const maxY = doc.page.height - margin - 50; // Leave space for footer

      // Header section
      doc.rect(margin, margin, pageWidth, 50).stroke();
      doc.fontSize(20).font('Helvetica-Bold').text('BILL OF SALE', margin, margin + 15, {
        width: pageWidth,
        align: 'center'
      });

      // Form number in top right corner
      const formNumber = invoiceNumber || 'BOS-PENDING';
      doc.fontSize(8).font('Helvetica').text(`Form #: ${formNumber}`, pageWidth + margin - 100, margin + 5);

      let currentY = margin + 60;

      // Create a more compact, UCDA-style layout
      // Two-column layout for dealer and buyer info
      const leftColWidth = pageWidth * 0.48;
      const rightColWidth = pageWidth * 0.48;
      const colGap = pageWidth * 0.04;

      // Dealer Information (Left Column)
      doc.rect(margin, currentY, leftColWidth, 20).fillAndStroke('#e0e0e0', '#000000');
      doc.fillColor('#000000').fontSize(10).font('Helvetica-Bold')
         .text('DEALER INFORMATION', margin + 5, currentY + 6);

      currentY += 20;
      doc.rect(margin, currentY, leftColWidth, 80).stroke();

      let dealerY = currentY + 8;
      doc.fontSize(8).font('Helvetica')
         .text(`Business Name: ${finalDealerInfo.name || finalDealerInfo.businessName}`, margin + 5, dealerY)
         .text(`Address: ${finalDealerInfo.address}`, margin + 5, dealerY + 12)
         .text(`Phone: ${finalDealerInfo.phone}`, margin + 5, dealerY + 24)
         .text(`Email: ${finalDealerInfo.email}`, margin + 5, dealerY + 36);

      let nextLineY = dealerY + 48;

      // Add RIN if available
      if (finalDealerInfo.rin) {
        doc.text(`RIN: ${finalDealerInfo.rin}`, margin + 5, nextLineY);
        nextLineY += 12;
      }

      // Add OMVIC Number if available
      if (finalDealerInfo.omvicNumber) {
        doc.text(`OMVIC Registration: ${finalDealerInfo.omvicNumber}`, margin + 5, nextLineY);
      }

      // Buyer Information (Right Column)
      const rightColX = margin + leftColWidth + colGap;
      doc.rect(rightColX, currentY - 20, rightColWidth, 20).fillAndStroke('#e0e0e0', '#000000');
      doc.fillColor('#000000').fontSize(10).font('Helvetica-Bold')
         .text('BUYER INFORMATION', rightColX + 5, currentY - 14);

      doc.rect(rightColX, currentY, rightColWidth, 80).stroke();

      let buyerY = currentY + 8;
      doc.fontSize(8).font('Helvetica')
         .text(`Name: ${buyer.name}`, rightColX + 5, buyerY)
         .text(`Address: ${buyer.address}`, rightColX + 5, buyerY + 12)
         .text(`Phone: ${buyer.phone}`, rightColX + 5, buyerY + 24)
         .text(`Email: ${buyer.email || 'N/A'}`, rightColX + 5, buyerY + 36)
         .text(`Driver's License: ${buyer.driversLicense || 'N/A'}`, rightColX + 5, buyerY + 48);

      currentY += 90;

      // Vehicle Information Section (Full Width)
      doc.rect(margin, currentY, pageWidth, 20).fillAndStroke('#e0e0e0', '#000000');
      doc.fillColor('#000000').fontSize(10).font('Helvetica-Bold')
         .text('VEHICLE INFORMATION', margin + 5, currentY + 6);

      currentY += 20;
      doc.rect(margin, currentY, pageWidth, 60).stroke();

      // Vehicle info in two columns
      let vehicleY = currentY + 8;
      doc.fontSize(8).font('Helvetica')
         .text(`Year: ${vehicle.year}`, margin + 5, vehicleY)
         .text(`Make: ${vehicle.make}`, margin + 5, vehicleY + 12)
         .text(`Model: ${vehicle.model}`, margin + 5, vehicleY + 24)
         .text(`Body Style: ${vehicle.bodyStyle || 'N/A'}`, margin + 5, vehicleY + 36);

      // Right column vehicle info
      const vehicleRightX = margin + pageWidth * 0.5;
      doc.text(`VIN: ${vehicle.vin}`, vehicleRightX, vehicleY)
         .text(`Odometer: ${vehicle.mileage} km`, vehicleRightX, vehicleY + 12)
         .text(`Color: ${vehicle.color}`, vehicleRightX, vehicleY + 24);

      currentY += 70;

      // Financial Details Section
      const subtotal = finalSalePrice - tradeInValue;
      const hst = taxes.hst || (subtotal * 0.13); // 13% HST for Ontario
      const total = subtotal + hst;

      doc.rect(margin, currentY, pageWidth, 20).fillAndStroke('#e0e0e0', '#000000');
      doc.fillColor('#000000').fontSize(10).font('Helvetica-Bold')
         .text('FINANCIAL DETAILS', margin + 5, currentY + 6);

      currentY += 20;
      const financialHeight = 70;
      doc.rect(margin, currentY, pageWidth, financialHeight).stroke();

      // Financial details in compact format
      let finY = currentY + 8;
      doc.fontSize(8).font('Helvetica');

      // Left side
      doc.text(`Sale Price: $${finalSalePrice.toLocaleString()}`, margin + 5, finY)
         .text(`Trade-in Value: $${tradeInValue.toLocaleString()}`, margin + 5, finY + 12)
         .text(`Subtotal: $${subtotal.toLocaleString()}`, margin + 5, finY + 24);

      // Right side
      const finRightX = margin + pageWidth * 0.5;
      doc.text(`HST (13%): $${hst.toLocaleString()}`, finRightX, finY)
         .text(`Payment Method: ${paymentMethod}`, finRightX, finY + 12);

      // Total with emphasis
      doc.fontSize(10).font('Helvetica-Bold')
         .text(`TOTAL AMOUNT: $${total.toLocaleString()}`, margin + 5, finY + 40);

      currentY += financialHeight + 10;

      // Warranties Section (if any)
      if (warranties.length > 0) {
        doc.rect(margin, currentY, pageWidth, 20).fillAndStroke('#e0e0e0', '#000000');
        doc.fillColor('#000000').fontSize(10).font('Helvetica-Bold')
           .text('WARRANTIES', margin + 5, currentY + 6);

        currentY += 20;
        const warrantyHeight = 40;
        doc.rect(margin, currentY, pageWidth, warrantyHeight).stroke();

        let warrantyY = currentY + 8;
        doc.fontSize(8).font('Helvetica');
        warranties.forEach(warranty => {
          doc.text(`• ${warranty.type}: ${warranty.description}`, margin + 5, warrantyY);
          warrantyY += 12;
        });

        currentY += warrantyHeight + 10;
      }

      // Terms and Conditions Section
      doc.rect(margin, currentY, pageWidth, 20).fillAndStroke('#e0e0e0', '#000000');
      doc.fillColor('#000000').fontSize(10).font('Helvetica-Bold')
         .text('TERMS AND CONDITIONS', margin + 5, currentY + 6);

      currentY += 20;
      const termsHeight = 100;
      doc.rect(margin, currentY, pageWidth, termsHeight).stroke();

      let termsY = currentY + 8;
      doc.fontSize(8).font('Helvetica');

      // Dynamic disclaimer based on certification type
      let disclaimerText = disclaimer;
      if (!disclaimerText) {
        switch (certification) {
          case 'certified':
            disclaimerText = 'This vehicle has been inspected and meets the standards for certification in Ontario. It is being sold as a certified vehicle and is roadworthy at the time of sale. No liens or encumbrances exist unless otherwise disclosed.';
            break;
          case 'warranty':
            disclaimerText = 'This vehicle is sold with a dealer-provided warranty. Warranty terms and coverage details have been reviewed and acknowledged by the buyer. No liens or encumbrances exist unless otherwise disclosed.';
            break;
          default: // 'as-is'
            disclaimerText = 'This vehicle is sold "as-is" without warranty. Buyer acknowledges that they have inspected the vehicle and accept its current condition. No liens or encumbrances exist on this vehicle unless otherwise disclosed.';
        }
      }

      const termsContent = [
        '1. ' + disclaimerText,
        '2. The buyer acknowledges receipt of all required disclosure documents.',
        '3. The buyer is responsible for vehicle registration and insurance.',
        '4. This sale is final and no returns are accepted.',
        '5. The buyer has inspected the vehicle and accepts its current condition.'
      ];

      termsContent.forEach(term => {
        // Handle long text wrapping
        const lines = doc.heightOfString(term, { width: pageWidth - 10 });
        if (lines > 12) {
          doc.text(term, margin + 5, termsY, { width: pageWidth - 10 });
          termsY += lines + 2;
        } else {
          doc.text(term, margin + 5, termsY);
          termsY += 12;
        }
      });

      currentY += termsHeight + 10;

      // Signatures Section
      doc.rect(margin, currentY, pageWidth, 20).fillAndStroke('#e0e0e0', '#000000');
      doc.fillColor('#000000').fontSize(10).font('Helvetica-Bold')
         .text('SIGNATURES', margin + 5, currentY + 6);

      currentY += 20;
      const sigHeight = 60;
      doc.rect(margin, currentY, pageWidth, sigHeight).stroke();

      let sigY = currentY + 10;
      doc.fontSize(8).font('Helvetica');

      // Buyer signature line
      doc.text('Buyer Signature:', margin + 5, sigY);
      if (buyerSignature && buyerSignature.name) {
        doc.text(buyerSignature.name, margin + 80, sigY);
      } else {
        doc.text('_________________________________', margin + 80, sigY);
      }
      doc.text('Date:', margin + 280, sigY);
      if (buyerSignature && buyerSignature.date) {
        doc.text(buyerSignature.date, margin + 310, sigY);
      } else {
        doc.text('_______________', margin + 310, sigY);
      }

      sigY += 25;

      // Dealer signature line
      doc.text('Dealer Representative:', margin + 5, sigY);
      if (sellerSignature && sellerSignature.name) {
        doc.text(sellerSignature.name, margin + 100, sigY);
      } else {
        doc.text('_________________________________', margin + 100, sigY);
      }
      doc.text('Date:', margin + 280, sigY);
      if (sellerSignature && sellerSignature.date) {
        doc.text(sellerSignature.date, margin + 310, sigY);
      } else {
        doc.text('_______________', margin + 310, sigY);
      }

      currentY += sigHeight + 10;

      // Footer with dealer contact info
      doc.fontSize(7).font('Helvetica')
         .text(`${finalDealerInfo.name || finalDealerInfo.businessName} - ${finalDealerInfo.phone} - ${finalDealerInfo.email}`, margin + 5, currentY)
         .text(`Generated on ${new Date().toLocaleDateString()} by FazeNAuto Management System`,
               margin, currentY + 15, { width: pageWidth, align: 'center' });

      doc.end();
    });
  }

  /**
   * Generate Used Vehicle Information Package (UVIP) Request Form
   * @param {Object} vehicleData - Vehicle information
   * @returns {Promise<Buffer>} - PDF buffer
   */
  async generateUVIPRequest(vehicleData) {
    const { vehicle, buyer, dealer, dealerInfo } = vehicleData;

    // Use dynamic dealer info from form, fallback to static info
    const finalDealerInfo = dealer || dealerInfo || this.dealerInfo;

    // Return mock PDF if PDFKit not available
    if (!this.isAvailable) {
      return this.generateMockPDF('UVIP Request', { vehicle });
    }

    const doc = new PDFDocument({ margin: 50 });
    const chunks = [];

    doc.on('data', chunk => chunks.push(chunk));
    
    return new Promise((resolve, reject) => {
      doc.on('end', () => resolve(Buffer.concat(chunks)));
      doc.on('error', reject);

      // Header
      doc.fontSize(18).text('USED VEHICLE INFORMATION PACKAGE (UVIP)', { align: 'center' });
      doc.fontSize(12).text('Request Form', { align: 'center' });
      doc.moveDown();

      // Instructions
      doc.fontSize(10)
         .text('This form is to be completed and submitted to ServiceOntario to obtain the UVIP for the vehicle being sold.')
         .moveDown();

      // Vehicle Information
      doc.fontSize(14).text('VEHICLE INFORMATION', { underline: true });
      doc.fontSize(12)
         .text(`VIN: ${vehicle.vin}`)
         .text(`Year: ${vehicle.year}`)
         .text(`Make: ${vehicle.make}`)
         .text(`Model: ${vehicle.model}`)
         .text(`License Plate: ________________`)
         .moveDown();

      // Dealer Information
      doc.fontSize(14).text('DEALER INFORMATION', { underline: true });
      doc.fontSize(12)
         .text(`Business Name: ${finalDealerInfo.name || finalDealerInfo.businessName}`)
         .text(`Address: ${finalDealerInfo.address}`)
         .text(`Phone: ${finalDealerInfo.phone}`);

      if (finalDealerInfo.rin) {
        doc.text(`RIN: ${finalDealerInfo.rin}`);
      }

      if (finalDealerInfo.omvicNumber) {
        doc.text(`OMVIC Registration: ${finalDealerInfo.omvicNumber}`);
      }

      doc.moveDown();

      // Required Documents Checklist
      doc.fontSize(14).text('REQUIRED DOCUMENTS CHECKLIST', { underline: true });
      doc.fontSize(12)
         .text('☐ Vehicle Registration')
         .text('☐ Proof of Insurance')
         .text('☐ Safety Standards Certificate (if required)')
         .text('☐ Emissions Test Certificate (if required)')
         .text('☐ Lien Information (if applicable)')
         .moveDown();

      // Instructions for ServiceOntario
      doc.fontSize(14).text('INSTRUCTIONS', { underline: true });
      doc.fontSize(10)
         .text('1. Take this form and required documents to any ServiceOntario location')
         .text('2. Pay the UVIP fee (currently $20)')
         .text('3. The UVIP must be provided to the buyer before the sale')
         .text('4. Keep a copy for your records')
         .moveDown();

      // Footer
      doc.fontSize(8)
         .text(`Generated on ${new Date().toLocaleDateString()} by FazeNAuto Management System`, 
               { align: 'center' });

      doc.end();
    });
  }

  /**
   * Generate OMVIC Disclosure Form
   * @param {Object} disclosureData - Disclosure information
   * @returns {Promise<Buffer>} - PDF buffer
   */
  async generateOMVICDisclosure(disclosureData) {
    const { vehicle, disclosures = [], dealer, dealerInfo } = disclosureData;

    // Use dynamic dealer info from form, fallback to static info
    const finalDealerInfo = dealer || dealerInfo || this.dealerInfo;
    const doc = new PDFDocument({ margin: 50 });
    const chunks = [];

    doc.on('data', chunk => chunks.push(chunk));
    
    return new Promise((resolve, reject) => {
      doc.on('end', () => resolve(Buffer.concat(chunks)));
      doc.on('error', reject);

      // Header
      doc.fontSize(18).text('OMVIC DISCLOSURE STATEMENT', { align: 'center' });
      doc.moveDown();

      // Dealer Information
      doc.fontSize(14).text('DEALER INFORMATION', { underline: true });
      doc.fontSize(12)
         .text(`Business Name: ${finalDealerInfo.name || finalDealerInfo.businessName}`)
         .text(`Address: ${finalDealerInfo.address}`)
         .text(`Phone: ${finalDealerInfo.phone}`);

      if (finalDealerInfo.rin) {
        doc.text(`RIN: ${finalDealerInfo.rin}`);
      }

      if (finalDealerInfo.omvicNumber) {
        doc.text(`OMVIC Registration: ${finalDealerInfo.omvicNumber}`);
      }

      doc.moveDown();

      // Vehicle Information
      doc.fontSize(14).text('VEHICLE INFORMATION', { underline: true });
      doc.fontSize(12)
         .text(`Year: ${vehicle.year}`)
         .text(`Make: ${vehicle.make}`)
         .text(`Model: ${vehicle.model}`)
         .text(`VIN: ${vehicle.vin}`)
         .text(`Odometer: ${vehicle.mileage} km`)
         .moveDown();

      // Mandatory Disclosures
      doc.fontSize(14).text('MANDATORY DISCLOSURES', { underline: true });
      doc.fontSize(12);

      const standardDisclosures = [
        'This vehicle is being sold "AS IS" and is not represented as being in road worthy condition.',
        'No warranty or guarantee is given by the dealer unless specified in writing.',
        'The buyer is advised to have the vehicle inspected by a qualified mechanic.',
        'The dealer is not responsible for any repairs needed after the sale.',
        ...disclosures
      ];

      standardDisclosures.forEach((disclosure, index) => {
        doc.text(`${index + 1}. ${disclosure}`);
      });

      doc.moveDown();

      // Buyer Acknowledgment
      doc.fontSize(14).text('BUYER ACKNOWLEDGMENT', { underline: true });
      doc.fontSize(12)
         .text('I acknowledge that I have read and understand all disclosures above.')
         .moveDown();

      doc.text('Buyer Signature: _________________________ Date: _____________');
      doc.moveDown();
      doc.text('Dealer Representative: _________________________ Date: _____________');

      // Footer
      doc.fontSize(8)
         .text(`Generated on ${new Date().toLocaleDateString()} by FazeNAuto Management System`, 
               { align: 'center' });

      doc.end();
    });
  }

  /**
   * Generate HST Return Reminder
   * @param {Object} taxData - Tax period data
   * @returns {Promise<Buffer>} - PDF buffer
   */
  async generateHSTReminder(taxData) {
    const { 
      period, 
      sales = [], 
      totalHSTCollected = 0,
      dueDate 
    } = taxData;

    const doc = new PDFDocument({ margin: 50 });
    const chunks = [];

    doc.on('data', chunk => chunks.push(chunk));
    
    return new Promise((resolve, reject) => {
      doc.on('end', () => resolve(Buffer.concat(chunks)));
      doc.on('error', reject);

      // Header
      doc.fontSize(18).text('HST RETURN REMINDER', { align: 'center' });
      doc.fontSize(14).text(`Period: ${period}`, { align: 'center' });
      doc.moveDown();

      // Business Information
      doc.fontSize(14).text('BUSINESS INFORMATION', { underline: true });
      doc.fontSize(12)
         .text(`Business Name: ${this.dealerInfo.name}`)
         .text(`HST Registration: ${this.dealerInfo.hstNumber}`)
         .text(`Address: ${this.dealerInfo.address}`)
         .moveDown();

      // Summary
      doc.fontSize(14).text('SUMMARY', { underline: true });
      doc.fontSize(12)
         .text(`Total Sales: ${sales.length}`)
         .text(`Total HST Collected: $${totalHSTCollected.toLocaleString()}`)
         .text(`Due Date: ${dueDate}`)
         .moveDown();

      // Sales Details
      if (sales.length > 0) {
        doc.fontSize(14).text('SALES DETAILS', { underline: true });
        doc.fontSize(10);
        
        sales.forEach(sale => {
          doc.text(`${sale.date} - ${sale.vehicle} - Sale: $${sale.amount} - HST: $${sale.hst}`);
        });
      }

      doc.moveDown();

      // Reminder Notes
      doc.fontSize(14).text('REMINDER NOTES', { underline: true });
      doc.fontSize(12)
         .text('• File your HST return by the due date to avoid penalties')
         .text('• Keep all sales records and receipts')
         .text('• Consult with your accountant if needed')
         .text('• Use CRA My Business Account for online filing');

      doc.end();
    });
  }

  /**
   * Save PDF to file system
   * @param {Buffer} pdfBuffer - PDF buffer
   * @param {String} filename - Output filename
   * @param {String} directory - Output directory
   * @returns {Promise<String>} - File path
   */
  async savePDF(pdfBuffer, filename, directory = 'generated-forms') {
    try {
      // Ensure directory exists
      if (!fs.existsSync(directory)) {
        fs.mkdirSync(directory, { recursive: true });
      }

      const filePath = path.join(directory, filename);
      fs.writeFileSync(filePath, pdfBuffer);
      
      return filePath;
    } catch (error) {
      console.error('Failed to save PDF:', error);
      throw error;
    }
  }

  /**
   * Generate Consignment Agreement PDF
   * @param {Object} consignmentData - Consignment agreement data
   * @returns {Promise<Buffer>} - PDF buffer
   */
  async generateConsignmentAgreement(consignmentData) {
    const {
      vehicle,
      consignor,
      agreementDuration,
      consignorSignature,
      consigneeSignature,
      dealer,
      dealerInfo
    } = consignmentData;

    // Use dynamic dealer info from form, fallback to static info
    const finalDealerInfo = dealer || dealerInfo || this.dealerInfo;

    // Return mock PDF if PDFKit not available
    if (!this.isAvailable) {
      return this.generateMockPDF('Consignment Agreement', { vehicle, consignor });
    }

    const doc = new PDFDocument({ margin: 50 });
    const chunks = [];

    doc.on('data', chunk => chunks.push(chunk));

    return new Promise((resolve, reject) => {
      doc.on('end', () => resolve(Buffer.concat(chunks)));
      doc.on('error', reject);

      const margin = 50;
      const pageWidth = 595.28 - (margin * 2); // A4 width minus margins
      const pageHeight = 841.89; // A4 height in points
      const maxY = pageHeight - margin - 50; // Leave space for footer
      let currentY = margin;

      // Helper function to check if we need a new page
      const checkPageBreak = (neededSpace = 50) => {
        if (currentY + neededSpace > maxY) {
          doc.addPage();
          currentY = margin;
        }
      };

      // Header
      doc.fontSize(16).font('Helvetica-Bold')
         .text('CONSIGNMENT SALES AGREEMENT', { align: 'center' });
      currentY += 40;

      // Agreement introduction
      checkPageBreak(30);
      doc.fontSize(12).font('Helvetica')
         .text('This agreement is made between:', margin, currentY);
      currentY += 30;

      // Consignee (FazeNAuto)
      checkPageBreak(20);
      doc.fontSize(12).font('Helvetica')
         .text('Consignee: FazeNAuto Inc.', margin, currentY);
      currentY += 20;

      // Consignor
      checkPageBreak(40);
      doc.fontSize(12).font('Helvetica')
         .text(`Consignor: ${consignor?.name || '_'.repeat(30)}`, margin, currentY);
      currentY += 40;

      // Vehicle Description Section
      checkPageBreak(120);
      doc.fontSize(14).font('Helvetica-Bold')
         .text('Vehicle Description', margin, currentY);
      currentY += 25;

      checkPageBreak(20);
      doc.fontSize(12).font('Helvetica')
         .text(`VIN: ${vehicle?.vin || '_'.repeat(25)}`, margin, currentY);
      currentY += 20;

      checkPageBreak(20);
      const vehicleDetailsY = currentY;
      doc.text(`Year: ${vehicle?.year || '_'.repeat(8)}`, margin, currentY)
         .text(`Make: ${vehicle?.make || '_'.repeat(10)}`, margin + 150, currentY)
         .text(`Model: ${vehicle?.model || '_'.repeat(10)}`, margin + 300, currentY);
      currentY += 20;

      checkPageBreak(20);
      doc.text(`KM: ${vehicle?.mileage || '_'.repeat(10)}`, margin, currentY)
         .text(`Engine: ${vehicle?.engine || '_'.repeat(10)}`, margin + 150, currentY)
         .text(`Trans: ${vehicle?.transmission || '_'.repeat(10)}`, margin + 300, currentY);
      currentY += 20;

      checkPageBreak(40);
      doc.text(`Color: ${vehicle?.color || '_'.repeat(10)}`, margin, currentY)
         .text(`Body: ${vehicle?.bodyStyle || '_'.repeat(10)}`, margin + 150, currentY);
      currentY += 40;

      // Disclosures & Declarations Section
      checkPageBreak(150);
      doc.fontSize(14).font('Helvetica-Bold')
         .text('Disclosures & Declarations:', margin, currentY);
      currentY += 25;

      const disclosures = consignmentData.disclosures || [
        '(a) The Consignor is the true owner and has the right and title to sell the vehicle.',
        '(b) Description of the vehicle is accurate, including the odometer reading.',
        '(c) Disclosure of any prior use as taxi/police/rental and any liens or encumbrances.',
        '(d) Details of repairs over $3,000 or any material deficiencies.',
        '(e) Consignor certifies vehicle has not been written off or materially altered.',
        '(f) The vehicle will not be used, operated, or taken off-site by the Consignee except for test drives authorized by the Consignor.'
      ];

      doc.fontSize(10).font('Helvetica');
      disclosures.forEach((disclosure, index) => {
        if (disclosure.trim()) { // Only show non-empty disclosures
          checkPageBreak(25);
          doc.text(disclosure, margin, currentY, { width: pageWidth });
          currentY += 25; // Increased spacing to fix the (g) spacing issue
        }
      });
      currentY += 20;

      // Pricing and Sales Terms Section
      checkPageBreak(100);
      doc.fontSize(14).font('Helvetica-Bold')
         .text('Pricing and Sales Terms:', margin, currentY);
      currentY += 25;

      const pricingTerms = consignmentData.pricingTerms || [
        `(a) Agreement duration: from ${agreementDuration?.from || '_'.repeat(12)} to ${agreementDuration?.to || '_'.repeat(12)}`,
        '(c) All written offers to be presented to consignor unless otherwise agreed.',
        '(d) All funds held in trust and payout due within 14 days of vehicle sale.'
      ];

      doc.fontSize(12).font('Helvetica');
      pricingTerms.forEach(term => {
        if (term.trim()) { // Only show non-empty terms
          checkPageBreak(25);
          doc.text(term, margin, currentY, { width: pageWidth });
          currentY += 25;
        }
      });
      currentY += 40;

      // Governing Law Section
      checkPageBreak(60);
      doc.fontSize(14).font('Helvetica-Bold')
         .text('Governing Law:', margin, currentY);
      currentY += 20;

      checkPageBreak(40);
      doc.fontSize(12).font('Helvetica')
         .text('This agreement is governed by the laws of Ontario.', margin, currentY);
      currentY += 40;

      // Signatures Section
      checkPageBreak(100);
      doc.fontSize(14).font('Helvetica-Bold')
         .text('Signatures:', margin, currentY);
      currentY += 30;

      // Consignor Signature
      checkPageBreak(40);
      doc.fontSize(12).font('Helvetica')
         .text(`Consignor Signature: ${consignorSignature?.name || '_'.repeat(30)}`, margin, currentY)
         .text(`Date: ${consignorSignature?.date || '_'.repeat(12)}`, margin + 350, currentY);
      currentY += 40;

      // Consignee Signature
      checkPageBreak(40);
      doc.text(`Consignee (FazeNAuto Inc.) Signature: ${consigneeSignature?.name || '_'.repeat(30)}`, margin, currentY)
         .text(`Date: ${consigneeSignature?.date || '_'.repeat(12)}`, margin + 350, currentY);

      // Footer - positioned at bottom right of current page
      const footerY = pageHeight - 30; // 30 points from bottom
      const footerText = `Generated on ${new Date().toLocaleDateString()} by FazeNAuto Management System`;

      doc.fontSize(8).font('Helvetica');
      const textWidth = doc.widthOfString(footerText);
      const footerX = margin + pageWidth - textWidth; // Right-aligned

      doc.text(footerText, footerX, footerY);

      doc.end();
    });
  }

  /**
   * Generate mock PDF for development/testing
   * @param {String} formType - Type of form
   * @param {Object} data - Form data
   * @returns {Promise<Buffer>} - Mock PDF buffer
   */
  async generateMockPDF(formType, data) {
    const mockContent = `
      ${formType.toUpperCase()}

      Generated by FazeNAuto Management System
      Date: ${new Date().toLocaleDateString()}

      ${data.vehicle ? `Vehicle: ${data.vehicle.year} ${data.vehicle.make} ${data.vehicle.model}` : ''}
      ${data.vehicle?.vin ? `VIN: ${data.vehicle.vin}` : ''}
      ${data.buyer ? `Buyer: ${data.buyer.name}` : ''}
      ${data.salePrice ? `Sale Price: $${data.salePrice.toLocaleString()}` : ''}

      This is a mock PDF generated for development purposes.
      Install PDFKit for full PDF generation functionality.
    `;

    // Return a simple text buffer as mock PDF
    return Buffer.from(mockContent, 'utf8');
  }
}

export default PDFGenerator;
