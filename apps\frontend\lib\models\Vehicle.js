import mongoose from 'mongoose';
import { v4 as uuidv4 } from 'uuid';

const vehicleSchema = new mongoose.Schema({
  uuid: {
    type: String,
    unique: true,
    default: uuidv4,
    index: true
  },
  make: {
    type: String,
    required: true,
    trim: true
  },
  model: {
    type: String,
    required: true,
    trim: true
  },
  year: {
    type: Number,
    required: true,
    min: 1900,
    max: new Date().getFullYear() + 1
  },
  vin: {
    type: String,
    required: true,
    trim: true,
    uppercase: true,
    minlength: 17,
    maxlength: 17
  },
  color: {
    type: String,
    required: true,
    trim: true
  },
  mileage: {
    type: Number,
    required: true,
    min: 0
  },
  price: {
    type: Number,
    required: true,
    min: 0
  },
  engine: {
    type: String,
    required: true,
    trim: true
  },
  transmission: {
    type: String,
    required: true,
    trim: true
  },
  drivetrain: {
    type: String,
    required: true,
    trim: true
  },
  fuelType: {
    type: String,
    trim: true,
    default: 'Gasoline'
  },
  doors: {
    type: Number,
    min: 2,
    max: 8,
    default: 4
  },
  images: [{
    type: String,
    trim: true
  }],
  imageHashes: [{
    type: String,
    trim: true
  }],
  videoUrl: {
    type: String,
    trim: true
  },
  media: [{
    key: {
      type: String,
      required: true,
      trim: true
    },
    type: {
      type: String,
      enum: ['image', 'video'],
      required: true
    },
    url: {
      type: String,
      trim: true
    },
    uploadedAt: {
      type: Date,
      default: Date.now
    }
  }],
  features: {
    exterior: {
      type: [{
        value: { type: String, required: true },
        status: { type: String, enum: ['included', 'not_available'], required: true }
      }],
      default: []
    },
    interior: {
      type: [{
        value: { type: String, required: true },
        status: { type: String, enum: ['included', 'not_available'], required: true }
      }],
      default: []
    },
    mechanical: {
      type: [{
        value: { type: String, required: true },
        status: { type: String, enum: ['included', 'not_available'], required: true }
      }],
      default: []
    },
    safety: {
      type: [{
        value: { type: String, required: true },
        status: { type: String, enum: ['included', 'not_available'], required: true }
      }],
      default: []
    },
    entertainment: {
      type: [{
        value: { type: String, required: true },
        status: { type: String, enum: ['included', 'not_available'], required: true }
      }],
      default: []
    }
  },
  status: {
    type: String,
    enum: ['draft', 'active', 'sold', 'on_hold', 'archived'],
    default: 'active'
  },
  description: {
    type: String,
    trim: true
  },
  dealerId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  soldDate: {
    type: Date
  },
  soldPrice: {
    type: Number,
    min: 0
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Indexes for better query performance
vehicleSchema.index({ make: 1, model: 1, year: 1 });
vehicleSchema.index({ status: 1, createdAt: -1 });
vehicleSchema.index({ price: 1 });
vehicleSchema.index({ vin: 1 }, { unique: true });
vehicleSchema.index({ uuid: 1 }, { unique: true });
vehicleSchema.index({ dealerId: 1, status: 1 });

// Pre-save middleware to update the updatedAt field and ensure UUID
vehicleSchema.pre('save', function(next) {
  this.updatedAt = new Date();

  // Generate UUID if it doesn't exist (for existing vehicles)
  if (!this.uuid) {
    this.uuid = uuidv4();
  }

  next();
});

// Virtual for formatted price
vehicleSchema.virtual('formattedPrice').get(function() {
  return new Intl.NumberFormat('en-CA', {
    style: 'currency',
    currency: 'CAD'
  }).format(this.price);
});

// Virtual for vehicle display name
vehicleSchema.virtual('displayName').get(function() {
  return `${this.year} ${this.make} ${this.model}`;
});

// Method to check if vehicle is available
vehicleSchema.methods.isAvailable = function() {
  return this.status === 'active';
};

// Method to mark vehicle as sold
vehicleSchema.methods.markAsSold = function(soldPrice = null) {
  this.status = 'sold';
  this.soldDate = new Date();
  if (soldPrice) {
    this.soldPrice = soldPrice;
  }
  return this.save();
};

// Static method to find available vehicles
vehicleSchema.statics.findAvailable = function() {
  return this.find({ status: 'active' }).sort({ createdAt: -1 });
};

// Static method to search vehicles
vehicleSchema.statics.search = function(query) {
  const searchRegex = new RegExp(query, 'i');
  return this.find({
    status: 'active',
    $or: [
      { make: searchRegex },
      { model: searchRegex },
      { description: searchRegex }
    ]
  }).sort({ createdAt: -1 });
};

const Vehicle = mongoose.models.Vehicle || mongoose.model('Vehicle', vehicleSchema);

export default Vehicle;
