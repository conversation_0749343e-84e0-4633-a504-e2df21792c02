/* General Navbar Styling */
.navbar {
  position: relative;
  width: 100%;
  background-color: var(--bg-secondary);
  z-index: 100;
  height: 80px; /* Fixed height - no more dynamic sizing */
  display: flex;
  align-items: center;
  border-bottom: 1px solid var(--border-primary);
}

.navbar-large {
  padding: 0; /* Remove dynamic padding */
}

.navbar-small {
  padding: 0; /* Remove dynamic padding */
}

.navbar-container {
  display: flex;
  justify-content: space-between; /* Space between logo and nav items */
  align-items: center;
  max-width: 1120px;
  margin: 0 auto;
  padding: 0 16px;
  width: 100%;
  position: relative; /* For absolute positioning of logo */
}

/* Logo - Always positioned on the left */
.logo-container {
  display: flex;
  align-items: center;
  transition: opacity 0.6s cubic-bezier(0.4, 0, 0.2, 1),
              transform 0.6s cubic-bezier(0.4, 0, 0.2, 1),
              left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 999;
}

/* Desktop logo positioning */
@media (min-width: 768px) {
  .logo-container {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
  }

  /* Slide logo to the right when sidebar is open */
  .logo-container.sidebar-open {
    left: 200px; /* Move to the right of the expanded sidebar with less margin */
  }
}

/* Hide logo when sidebar is open */
.logo-container.hidden {
  opacity: 0;
  transform: translateY(-50%) translateX(-20px);
  pointer-events: none;
}

.logo img {
  height: 50px;
  margin: 1rem;
}

.mobile-hide-logo {
  display: none;
}

.logo-text {
  color: var(--text-primary);
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
  text-decoration: none;
  white-space: nowrap;
}

.logo-text:hover {
  text-decoration: none;
}

.logo-text-desktop {
  display: inline;
}

.brand-faze {
  color: var(--text-primary);
}

.brand-n {
  color: var(--brand-red);
}

.brand-auto {
  color: var(--text-primary);
}

/* Remove the mobile hide behavior - logo always visible */
@media (min-width: 640px) {
  .logo-text {
    font-size: 32px;
  }
}

/* Hamburger Styles */
.hamburger,
.mobile-nav-toggle {
  position: absolute;
  top: 1rem;
  right: 1rem;
  cursor: pointer;
  z-index: 30;
}

/* Light mode: Make X icon white when toggled */
[data-theme="light"] .mobile-nav-toggle [data-testid="hamburger-react"] > div {
  transition: color 0.3s ease;
}

/* Target the hamburger-react component when it's toggled (X state) in light mode */
[data-theme="light"] .mobile-nav-toggle [data-testid="hamburger-react"][aria-expanded="true"] > div {
  background-color: white !important;
}

/* Ensure hamburger lines remain black in light mode when not toggled */
[data-theme="light"] .mobile-nav-toggle [data-testid="hamburger-react"][aria-expanded="false"] > div {
  background-color: black !important;
}

@media (min-width: 768px) {
  .mobile-nav-toggle {
    display: none;
  }
}

.bar {
  width: 25px;
  height: 3px;
  background-color: var(--text-primary);
  margin: 5px 0;
  transition: all 0.3s ease;
}

.close {
  font-size: 2rem;
  color: var(--text-primary);
}

/* Mobile Menu Overlay (full screen) */
.mobile-menu {
  position: fixed;
  top: 0;
  right: 0;
  width: 100%;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  opacity: 0;
  transform: translateY(-20px); /* initial off-screen slide up */
  pointer-events: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition:
    opacity 0.6s ease,
    transform 0.6s ease; /* removed background-color transition */
}


.mobile-menu.show {
  opacity: 1;
  pointer-events: auto;
  transform: translateY(0); /* slide into place */
}
@media (min-width: 768px) {
  .mobile-menu {
    display: none !important;
  }
}

/* Mobile List */
.mobile-list {
  list-style: none;
  padding: 0;
  margin: 0;
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 0;
}

/* Mobile Login Section */
.mobile-login {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: 1rem;
  padding-top: 1rem;
}

.mobile-login .nav-link {
  color: #e53e3e;
  font-weight: 600;
}

.mobile-list .nav-item {
  margin: 1.5rem 0;
  position: relative;
}

.mobile-list .nav-link {
  color: white;
  text-decoration: none;
  font-size: 1.5rem;
}

.mobile-list .nav-link:hover {
  text-decoration: underline;
}

/* Desktop Nav List */
.nav-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.desktop-list {
  display: none;
}

@media (min-width: 768px) {
  .desktop-list {
    display: flex;
    gap: 2.5rem;
    margin-right: 1rem; /* Align closer to edge like mobile */
    align-items: center; /* Ensure all items are centered vertically */
    height: 100%; /* Full height for proper alignment */
  }
}

/* Nav Links */
.nav-link {
  font-size: 20px;
  font-weight: 500;
  cursor: pointer;
  color: var(--text-secondary);
  text-decoration: none;
}

.nav-link:hover,
.nav-link.active {
  color: var(--text-primary);
}

/* Dropdown Container */
.nav-item {
  position: relative;
  display: flex;
  align-items: center; /* Center align all nav items */
  height: 100%; /* Full height for proper alignment */
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: 0.5rem;
  margin-top: 0.5rem;
  padding: 0;
  z-index: 20;
  min-width: 200px;
  box-shadow: var(--card-shadow);
  display: flex;
  flex-direction: column;
  list-style: none;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
  opacity: 0;
  visibility: hidden;
  backdrop-filter: blur(10px);
}

.dropdown-menu.show {
  max-height: 300px;
  opacity: 1;
  visibility: visible;
}

.dropdown-menu li,
.dropdown-item {
  margin: 0;
  padding: 0;
  border-radius: 0;
  transition: all 0.2s ease;
}

.dropdown-item a,
.dropdown-menu li a {
  text-decoration: none;
  color: var(--text-primary);
  font-weight: 500;
  display: block;
  width: 100%;
  font-size: 0.95rem;
  padding: 0.75rem 1.5rem;
  border-radius: 0;
  transition: all 0.2s ease;
  position: relative;
  margin: 0;
}

.dropdown-item:hover {
  background: linear-gradient(135deg, rgba(229, 62, 62, 0.15) 0%, rgba(252, 129, 129, 0.15) 100%);
}

.dropdown-item a:hover,
.dropdown-menu li a:hover {
  color: var(--text-primary);
  background: transparent; /* Remove conflicting background */
}

.dropdown-item:first-child {
  margin-top: 0.5rem;
}

.dropdown-item:last-child {
  margin-bottom: 0.5rem;
}

/* Medium Screen Fixes (Tablet) */
@media (max-width: 1024px) and (min-width: 768px) {
  .navbar-container {
    padding: 0.75rem 2rem;
  }

  .logo-container {
    margin-left: 0; /* Reset margin for medium screens */
  }

  .logo-container h1 {
    font-size: 1.8rem; /* Slightly smaller logo on medium screens */
  }

  .logo-text {
    font-size: 28px; /* Appropriate size for medium screens */
  }

  /* Adjust navigation menu items for medium screens */
  .desktop-list {
    gap: 1.8rem; /* Reduce gap between nav items */
    margin-right: 0.5rem; /* Adjust margin */
  }

  .nav-link {
    font-size: 18px; /* Slightly smaller font size */
    font-weight: 500;
  }

  /* Adjust dropdown menu for medium screens */
  .dropdown-menu {
    min-width: 180px; /* Slightly smaller dropdown width */
  }

  .dropdown-item a,
  .dropdown-menu li a {
    font-size: 0.9rem; /* Slightly smaller dropdown text */
    padding: 0.65rem 1.25rem; /* Adjust padding */
  }

  /* User dropdown adjustments for medium screens */
  .user-dropdown {
    min-width: 160px;
  }

  .user-link {
    padding: 0.5rem 0.75rem;
  }

  .user-avatar {
    width: 32px;
    height: 32px;
  }
}

/* Navbar Left Section */
.navbar-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* Admin Portal Mobile Section - Align with FazeNAuto logo */
.admin-portal-mobile {
  position: absolute;
  right: 1rem; /* Align closer to edge like logo positioning */
  top: 50%;
  transform: translateY(-50%);
  z-index: 25;
}

/* Mobile-specific positioning and scaling adjustments */
@media (max-width: 767px) {
  .admin-portal-mobile {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
  }
}

.user-dropdown-mobile-top {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-link-mobile-top {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: transparent;
  padding: 0.5rem 0.75rem;
  border-radius: 8px;
  cursor: pointer;
  transition: background 0.2s ease;
  justify-content: center;
  min-height: 44px;
  position: relative;
}

/* Mobile-specific user link adjustments */
@media (max-width: 767px) {
  .user-link-mobile-top {
    padding: 0.5rem;
    min-width: 44px; /* Ensure square touch target */
    justify-content: center;
    align-items: center;
  }
}

.user-link-mobile-top:hover {
  background: rgba(255, 255, 255, 0.1);
}

.admin-portal-text {
  color: white;
  font-size: 0.9rem;
  font-weight: 600;
  white-space: nowrap;
}





.user-dropdown-top {
  position: absolute;
  top: 100%;
  right: 0;
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  min-width: 180px;
  max-width: 220px;
  box-shadow: var(--card-shadow);
  backdrop-filter: blur(10px);
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: opacity 0.3s ease, visibility 0.3s ease, transform 0.3s ease;
  margin-top: 0.5rem;
}

/* Mobile positioning - improve dropdown alignment and prevent cutoff */
@media (max-width: 767px) {
  .user-dropdown-top {
    position: absolute;
    top: 100%;
    right: 0;
    left: auto;
    min-width: 180px;
    max-width: 220px;
    max-height: 80vh;
    overflow-y: auto;
    transform-origin: top right;
    margin-top: 0.5rem;
    z-index: 1000;
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    backdrop-filter: blur(10px);
    box-shadow: var(--card-shadow);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
  }

  .user-dropdown-top.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }

  /* Dark mode styling for mobile user dropdown */
  [data-theme="dark"] .user-dropdown-top {
    background: linear-gradient(135deg, #0d1117 0%, #161b22 100%) !important;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
  }
}



/* Removed duplicate rule - now handled in mobile media query above */

.user-dropdown-top .dropdown-item {
  list-style: none;
}

.user-dropdown-top .dropdown-item a {
  display: block;
  padding: 0.75rem 1rem;
  color: var(--text-primary);
  text-decoration: none;
  font-size: 0.9rem;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid var(--border-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-dropdown-top .dropdown-item:last-child a {
  border-bottom: none;
}

.user-dropdown-top .dropdown-item a:hover {
  background: #ff4b4b; /* Red hover color to match desktop/tablet */
}

/* Mobile dropdown arrow styling */
.mobile-dropdown-arrow {
  color: var(--text-primary) !important;
  font-size: 0.75rem;
  margin-left: 0;
  transition: transform 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

/* Logout button specific styling in mobile dropdown */
.user-dropdown-top .dropdown-item .logout-link {
  color: #ef4444 !important;
  font-weight: 500;
}

/* Light mode mobile logout button hover - bright red to match Dashboard item */
:root .user-dropdown-top .dropdown-item .logout-link:hover {
  background: #ff4b4b !important; /* Bright red matching Dashboard item */
  color: #ffffff !important;
}

[data-theme="light"] .user-dropdown-top .dropdown-item .logout-link:hover {
  background: #ff4b4b !important; /* Bright red matching Dashboard item */
  color: #ffffff !important;
}

/* Dark mode mobile logout button hover - keep original styling */
[data-theme="dark"] .user-dropdown-top .dropdown-item .logout-link:hover {
  background: rgba(239, 68, 68, 0.1) !important;
  color: #dc2626 !important;
}

/* Mobile Sub-dropdown for Dashboard */
.mobile-sub-dropdown {
  list-style: none;
  padding: 0;
  margin: 0.5rem 0 0 0;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Dark mode gradient background for mobile sub-dropdown */
[data-theme="dark"] .mobile-sub-dropdown {
  background: linear-gradient(135deg, #0d1117 0%, #161b22 100%) !important;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.mobile-sub-dropdown .dropdown-item {
  margin: 0;
}

.mobile-sub-dropdown .dropdown-item a {
  padding: 0.5rem 1rem;
  font-size: 0.85rem;
  color: #d1d5db;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.mobile-sub-dropdown .dropdown-item:last-child a {
  border-bottom: none;
}

.mobile-sub-dropdown .dropdown-item a:hover {
  background: rgba(255, 75, 75, 0.15); /* Red hover background */
  color: white;
}

/* Mobile Responsive Fixes */
@media (max-width: 767px) {
  .navbar {
    height: 80px; /* Keep consistent height */
  }

  .navbar-container {
    padding: 0 1.5rem; /* Remove vertical padding */
    justify-content: space-between; /* Space between logo and user dropdown */
    height: 100%;
    align-items: center;
    position: relative;
  }

  .logo-container {
    position: relative; /* Change from absolute to relative */
    left: auto;
    top: auto;
    transform: none;
  }

  .logo-text {
    font-size: 24px; /* Smaller on mobile */
  }

  .mobile-nav-toggle {
    right: 1.5rem; /* Better hamburger positioning */
  }

  /* Mobile Accordion Dropdown - Keep Original Styling */
  .mobile-list .nav-link {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
  }

  /* Mobile Dropdown Menu */
  .mobile-list .dropdown-menu {
    position: absolute;
    background: rgba(31, 41, 55, 0.3);
    border-radius: 12px;
    left: calc(-50vw + 50%);
    top: 100%;
    padding: 1rem 0;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    min-width: auto;
  }

  /* Dark mode gradient background for mobile dropdown menu */
  [data-theme="dark"] .mobile-list .dropdown-menu {
    background: linear-gradient(135deg, #0d1117 0%, #161b22 100%) !important;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    width: 100vw;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.4s ease, opacity 0.4s ease, visibility 0.4s ease;
    opacity: 0;
    visibility: hidden;
    transform: none;
    z-index: 1000;
  }

  .mobile-list .dropdown-menu.show {
    max-height: 300px;
    opacity: 1;
    visibility: visible;
  }

  /* Mobile dropdown items */
  .mobile-list .dropdown-item {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .mobile-list .dropdown-item:last-child {
    border-bottom: none;
  }

  .mobile-list .dropdown-item a {
    color: #e5e7eb;
    padding: 1rem 3rem;
    font-size: 1.2rem;
    border-radius: 0;
    margin: 0;
    display: block;
    text-align: center;
    width: 100%;
  }

  .mobile-list .dropdown-item:hover {
    background: linear-gradient(135deg, rgba(229, 62, 62, 0.1) 0%, rgba(252, 129, 129, 0.1) 100%);
  }

  .mobile-list .dropdown-item a:hover {
    color: #ffffff;
    background: linear-gradient(135deg, rgba(229, 62, 62, 0.15) 0%, rgba(252, 129, 129, 0.15) 100%);
  }

  /* Mobile dropdown arrow animation */
  .mobile-list .nav-link span {
    transition: transform 0.3s ease;
    font-size: 0.8rem;
    margin-left: 0.25rem;
  }

  .mobile-list .nav-item.dropdown-open .nav-link span {
    transform: rotate(180deg);
  }

  /* Hide other nav items when inventory dropdown is open */
  .mobile-list .nav-item:not(.dropdown-open) {
    transition: opacity 0.4s ease, visibility 0.4s ease;
  }

  .mobile-list .nav-item.dropdown-open ~ .nav-item {
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
  }
}

/* Enhanced Dropdown Styling */
@media (min-width: 768px) {
  .dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: rgba(31, 41, 55, 0.85);
    border-radius: 12px;
    margin-top: 0.75rem;
    padding: 0.5rem 0;
    z-index: 20;
    min-width: 220px;
    box-shadow:
      0 20px 25px -5px rgba(0, 0, 0, 0.3),
      0 10px 10px -5px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    display: flex;
    flex-direction: column;
    list-style: none;
    max-height: 0;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
  }

  /* Dark mode gradient background for main dropdown menus */
  [data-theme="dark"] .dropdown-menu {
    background: linear-gradient(135deg, #0d1117 0%, #161b22 100%) !important;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
  }

  /* Override the general dropdown styling for desktop */
  .dropdown-menu li a,
  .dropdown-item a {
    color: #e5e7eb !important;
    padding: 0.75rem 1rem !important;
    border-radius: 6px !important;
    margin: 0.25rem 0.5rem !important;
    transition: all 0.2s ease !important;
    background: transparent !important;
  }

  .dropdown-item:hover {
    background: linear-gradient(135deg, rgba(229, 62, 62, 0.2) 0%, rgba(252, 129, 129, 0.2) 100%) !important;
    color: #ffffff !important;
    transform: translateX(2px) !important;
  }

  .dropdown-menu li a:hover,
  .dropdown-item a:hover {
    background: transparent !important;
    color: #ffffff !important;
  }

  .dropdown-menu.show {
    max-height: 300px;
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }

  .dropdown-menu::before {
    content: '';
    position: absolute;
    top: -8px;
    left: 20px;
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid rgba(31, 41, 55, 0.85);
  }
}

/* User Authentication Styles */
.user-auth-mobile,
.user-auth-desktop {
  position: relative;
}

.user-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  color: var(--text-secondary);
  text-decoration: none;
  font-size: 20px;
  font-weight: 500;
  transition: color 0.2s ease;
}

.user-link:hover {
  color: var(--text-primary);
}

.user-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: rgba(102, 126, 234, 0.3); /* Transparent purple */
  border-radius: 50%;
  margin-right: 0; /* Remove margin for mobile alignment - will be overridden for desktop */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease, background 0.2s ease;
  color: var(--text-primary);
  backdrop-filter: blur(10px); /* Add blur effect */
}

/* Desktop user icon - add margin back for desktop layout */
@media (min-width: 768px) {
  .user-icon {
    margin-right: 0.5rem;
  }
}

.user-icon:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.user-icon svg {
  width: 18px;
  height: 18px;
}

.dropdown-arrow {
  font-size: 0.8rem;
  margin-left: 0.25rem;
  transition: transform 0.3s ease, color 0.2s ease;
  color: var(--text-primary);
  animation: subtle-bounce 2s ease-in-out infinite;
}

.dropdown-arrow:hover {
  color: var(--text-secondary);
}

@keyframes subtle-bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-2px);
  }
}

.user-dropdown-desktop .user-link:hover .dropdown-arrow,
.user-dropdown-mobile .user-link:hover .dropdown-arrow {
  transform: rotate(180deg);
}

/* User Dropdown Menu - Match mobile styling exactly */
.user-dropdown {
  background: var(--bg-secondary); /* Match mobile background */
  border: 1px solid var(--border-primary); /* Match mobile border */
  border-radius: 8px; /* Match mobile border-radius */
  backdrop-filter: blur(10px); /* Match mobile backdrop filter */
  box-shadow: var(--card-shadow); /* Match mobile shadow */
  left: auto !important;
  right: 0 !important; /* Align with right edge like mobile */
  min-width: 180px; /* Match mobile min-width */
  max-width: 220px; /* Match mobile max-width */
  padding: 0; /* Remove any padding to match mobile */
  margin-top: 0.5rem; /* Match mobile margin-top */
}

/* Dark mode gradient background for user dropdown */
[data-theme="dark"] .user-dropdown {
  background: linear-gradient(to bottom right, #0d1117, #161b22) !important;
  border: 1px solid #333;
}

.user-dropdown .dropdown-item {
  margin: 0; /* Remove margin to allow full-width hover */
  list-style: none;
}

.user-dropdown .dropdown-item a {
  color: var(--text-primary);
  padding: 0.75rem 1rem; /* Match mobile padding exactly */
  border-radius: 0; /* Remove border radius for full-width effect */
  transition: background-color 0.2s ease; /* Match mobile transition */
  display: block;
  text-decoration: none;
  font-size: 0.9rem; /* Match mobile font size */
  border-bottom: 1px solid var(--border-primary); /* Consistent border */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%; /* Ensure full width */
  box-sizing: border-box;
  min-height: auto; /* Let content determine height */
}

.user-dropdown .dropdown-item:last-child a {
  border-bottom: none; /* Match mobile - no border on last item */
}

.user-dropdown .dropdown-item a:hover {
  background: #ff4b4b; /* Match mobile red hover color */
  color: #ffffff;
}

/* Light mode specific styling - ensure text is black */
:root .user-dropdown .dropdown-item a,
:root .dropdown-menu .dropdown-item a {
  color: #000000 !important; /* Black text in light mode for all dropdowns */
}

[data-theme="light"] .user-dropdown .dropdown-item a,
[data-theme="light"] .dropdown-menu .dropdown-item a {
  color: #000000 !important; /* Black text in light mode for all dropdowns */
}

/* Ensure black text on desktop in light mode for dealer dropdown */
@media (min-width: 768px) {
  :root .user-dropdown .dropdown-item a,
  :root .dropdown-menu .dropdown-item a {
    color: #000000 !important; /* Black text in light mode desktop */
  }

  [data-theme="light"] .user-dropdown .dropdown-item a,
  [data-theme="light"] .dropdown-menu .dropdown-item a {
    color: #000000 !important; /* Black text in light mode desktop */
  }
}

/* Light mode global dropdown hover effects - apply light red background to ALL dropdowns */
:root .dropdown-item:hover,
:root .user-dropdown .dropdown-item:hover {
  background: rgba(239, 68, 68, 0.1) !important; /* Light red hover matching logout button */
}

[data-theme="light"] .dropdown-item:hover,
[data-theme="light"] .user-dropdown .dropdown-item:hover {
  background: rgba(239, 68, 68, 0.1) !important; /* Light red hover matching logout button */
}

/* Apply to all screen sizes in light mode */
@media (max-width: 767px) {
  :root .dropdown-item:hover,
  :root .user-dropdown .dropdown-item:hover {
    background: rgba(239, 68, 68, 0.1) !important;
  }

  [data-theme="light"] .dropdown-item:hover,
  [data-theme="light"] .user-dropdown .dropdown-item:hover {
    background: rgba(239, 68, 68, 0.1) !important;
  }
}

@media (min-width: 768px) {
  :root .dropdown-item:hover,
  :root .user-dropdown .dropdown-item:hover {
    background: rgba(239, 68, 68, 0.1) !important;
  }

  [data-theme="light"] .dropdown-item:hover,
  [data-theme="light"] .user-dropdown .dropdown-item:hover {
    background: rgba(239, 68, 68, 0.1) !important;
  }
}

/* Inventory dropdown styling to match user dropdown in light mode */
:root .dropdown-menu {
  background: var(--bg-secondary); /* Match user dropdown background */
  border: 1px solid var(--border-primary); /* Match user dropdown border */
  border-radius: 8px; /* Match user dropdown border-radius */
  backdrop-filter: blur(10px); /* Match user dropdown backdrop filter */
  box-shadow: var(--card-shadow); /* Match user dropdown shadow */
  padding: 0; /* Match user dropdown padding */
}

[data-theme="light"] .dropdown-menu {
  background: var(--bg-secondary); /* Match user dropdown background */
  border: 1px solid var(--border-primary); /* Match user dropdown border */
  border-radius: 8px; /* Match user dropdown border-radius */
  backdrop-filter: blur(10px); /* Match user dropdown backdrop filter */
  box-shadow: var(--card-shadow); /* Match user dropdown shadow */
  padding: 0; /* Match user dropdown padding */
}

/* Inventory dropdown styling to match user dropdown in dark mode - EXACT match */
[data-theme="dark"] .dropdown-menu {
  background: linear-gradient(to bottom right, #0d1117, #161b22) !important; /* Match user dropdown gradient */
  border: 1px solid #333 !important; /* Match user dropdown border */
  border-radius: 8px !important; /* Match user dropdown border-radius */
  backdrop-filter: blur(10px) !important; /* Match user dropdown backdrop filter */
  box-shadow: var(--card-shadow) !important; /* Match user dropdown shadow */
  padding: 0 !important; /* Match user dropdown padding */
  min-width: 180px !important; /* Match user dropdown min-width */
  max-width: 220px !important; /* Match user dropdown max-width */
}

/* Dark mode desktop - ensure inventory dropdown matches dealer dropdown exactly */
@media (min-width: 768px) {
  [data-theme="dark"] .dropdown-menu {
    background: linear-gradient(to bottom right, #0d1117, #161b22) !important;
    border: 1px solid #333 !important;
    border-radius: 8px !important;
    backdrop-filter: blur(10px) !important;
    box-shadow: var(--card-shadow) !important;
    padding: 0 !important;
    min-width: 180px !important;
    max-width: 220px !important;
    margin-top: 0.5rem !important; /* Match user dropdown margin */
  }
}

/* Dark mode specific styling - white text for all dropdowns */
[data-theme="dark"] .user-dropdown .dropdown-item a,
[data-theme="dark"] .dropdown-menu .dropdown-item a {
  color: #ffffff !important; /* White text in dark mode for all dropdowns */
}

/* Dark mode desktop specific styling */
@media (min-width: 768px) {
  [data-theme="dark"] .user-dropdown .dropdown-item a,
  [data-theme="dark"] .dropdown-menu .dropdown-item a {
    color: #ffffff !important; /* White text in dark mode desktop */
  }
}

.logout-link {
  cursor: pointer;
  color: #ef4444 !important;
  font-weight: 600;
}

/* Light mode logout button hover - bright red to match Dashboard item */
:root .logout-link:hover {
  background: #ff4b4b !important; /* Bright red matching Dashboard item */
  color: #ffffff !important;
}

[data-theme="light"] .logout-link:hover {
  background: #ff4b4b !important; /* Bright red matching Dashboard item */
  color: #ffffff !important;
}

/* Dark mode logout button hover - keep original styling */
[data-theme="dark"] .logout-link:hover {
  background: rgba(239, 68, 68, 0.1) !important;
  color: #ffffff !important;
}

/* Mobile User Authentication */
@media (max-width: 767px) {
  /* Ensure navbar container has proper mobile alignment */
  .navbar-container {
    padding: 0 1rem; /* Consistent padding with logo positioning */
    justify-content: space-between; /* Space between logo and user dropdown */
    align-items: center;
    height: 100%;
  }

  /* Logo positioning on mobile */
  .logo-container {
    position: relative; /* Change from absolute to relative on mobile */
    left: auto;
    top: auto;
    transform: none;
  }

  .user-auth-mobile .user-dropdown {
    position: static;
    background: rgba(31, 41, 55, 0.95); /* Added transparency */
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px); /* Added blur effect */
    margin-top: 0.5rem;
    max-height: 0;
    overflow: hidden;
    opacity: 0;
  }

  /* Dark mode gradient background for mobile user dropdown */
  [data-theme="dark"] .user-auth-mobile .user-dropdown {
    background: linear-gradient(135deg, #0d1117 0%, #161b22 100%) !important;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    visibility: hidden;
    transition: max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.4s ease, visibility 0.4s ease, margin-top 0.4s ease, transform 0.4s ease;
    transform: translateY(-10px); /* Added slide animation */
  }

  .user-auth-mobile .user-dropdown.show {
    max-height: 250px; /* Increased height for better scaling */
    opacity: 1;
    visibility: visible;
    margin-top: 0.5rem;
    transform: translateY(0); /* Slide into place */
  }

  .user-auth-mobile .user-dropdown .dropdown-item {
    transform: translateY(-10px);
    opacity: 0;
    transition: transform 0.3s ease, opacity 0.3s ease;
    transition-delay: 0.1s;
    margin: 0.25rem 0.5rem; /* Added margin for better spacing */
  }

  .user-auth-mobile .user-dropdown.show .dropdown-item {
    transform: translateY(0);
    opacity: 1;
  }

  .user-auth-mobile .user-dropdown .dropdown-item a {
    color: white;
    font-size: 1rem; /* Reduced font size for better scaling */
    padding: 0.75rem 1rem; /* Increased padding for better touch targets */
    text-align: center;
    transition: color 0.2s ease, background-color 0.2s ease, transform 0.2s ease;
    border-radius: 6px; /* Added border radius */
    display: block;
    width: 100%;
  }

  .user-auth-mobile .user-dropdown .dropdown-item a:hover {
    color: #ffffff;
    background: linear-gradient(135deg, rgba(229, 62, 62, 0.2) 0%, rgba(252, 129, 129, 0.2) 100%);
    transform: translateX(2px); /* Added subtle slide effect */
  }

  .user-auth-mobile .user-link {
    font-size: 1.5rem;
    justify-content: center;
    transition: color 0.2s ease;
  }

  .user-auth-mobile .user-icon {
    font-size: 1.2rem; /* Slightly smaller for better balance */
    width: 32px; /* Match desktop size for consistency */
    height: 32px; /* Match desktop size for consistency */
    transition: transform 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0; /* Remove any margin for perfect alignment */
    background: rgba(102, 126, 234, 0.3); /* Ensure background is visible */
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .user-auth-mobile .user-link:hover .user-icon {
    transform: scale(1.1);
  }
}

/* Logout Confirmation Modal */
.logout-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  backdrop-filter: blur(4px);
}

.logout-modal {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  max-width: 400px;
  width: 90%;
  text-align: center;
  animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.logout-modal h3 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.2rem;
  font-weight: 600;
}

.logout-modal-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.logout-confirm-btn,
.logout-cancel-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 80px;
}

.logout-confirm-btn {
  background: #dc2626;
  color: white;
}

.logout-confirm-btn:hover {
  background: #b91c1c;
  transform: translateY(-1px);
}

.logout-cancel-btn {
  background: #6b7280;
  color: white;
}

.logout-cancel-btn:hover {
  background: #4b5563;
  transform: translateY(-1px);
}

/* Sub-dropdown styles */
.dropdown-parent {
  position: relative;
}

.dropdown-label {
  display: block;
  padding: 0.75rem 1rem;
  color: #e5e7eb;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.dropdown-label:hover {
  background: linear-gradient(135deg, rgba(229, 62, 62, 0.2) 0%, rgba(252, 129, 129, 0.2) 100%);
  color: #ffffff;
}

.sub-dropdown {
  position: absolute;
  left: 100%;
  top: 0;
  background: linear-gradient(145deg, rgba(31, 41, 55, 0.95) 0%, rgba(55, 65, 81, 0.95) 100%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  min-width: 180px;
  opacity: 0;
  visibility: hidden;
  transform: translateX(-10px);
  transition: all 0.3s ease;
  z-index: 1001;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  list-style: none;
  padding: 0.5rem 0;
  margin: 0;
}

.dropdown-parent:hover .sub-dropdown {
  opacity: 1;
  visibility: visible;
  transform: translateX(0);
}

.sub-dropdown .dropdown-item {
  margin: 0.25rem 0.5rem;
}

.sub-dropdown .dropdown-item a {
  color: #e5e7eb;
  padding: 0.5rem 0.75rem;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: block;
  text-decoration: none;
  font-size: 0.9rem;
}

.sub-dropdown .dropdown-item a:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(147, 197, 253, 0.2) 100%);
  color: #ffffff;
  transform: translateX(2px);
}

/* ===== DROPDOWN CONSISTENCY ENHANCEMENTS ===== */
/* Ensure inventory dropdown matches dealer dropdown exactly in all aspects */

/* Base styling - inventory dropdown matches dealer dropdown */
.dropdown-menu {
  background: var(--bg-secondary) !important;
  border: 1px solid var(--border-primary) !important;
  border-radius: 8px !important;
  backdrop-filter: blur(10px) !important;
  box-shadow: var(--card-shadow) !important;
  padding: 0 !important;
  min-width: 180px !important;
  max-width: 220px !important;
  margin-top: 0.5rem !important;
}

/* Dark mode - inventory dropdown matches dealer dropdown exactly */
[data-theme="dark"] .dropdown-menu {
  background: linear-gradient(to bottom right, #0d1117, #161b22) !important;
  border: 1px solid #333 !important;
}

/* Inventory dropdown items match dealer dropdown items exactly */
.dropdown-menu .dropdown-item a {
  color: var(--text-primary) !important;
  padding: 0.75rem 1rem !important;
  border-radius: 0 !important;
  transition: background-color 0.2s ease !important;
  display: block !important;
  text-decoration: none !important;
  font-size: 0.9rem !important;
  border-bottom: 1px solid var(--border-primary) !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  width: 100% !important;
  box-sizing: border-box !important;
  font-weight: 500 !important;
}

/* Last item - no border bottom (match user dropdown) */
.dropdown-menu .dropdown-item:last-child a {
  border-bottom: none !important;
}

/* Light mode text colors - black for both dropdowns */
:root .dropdown-menu .dropdown-item a,
[data-theme="light"] .dropdown-menu .dropdown-item a {
  color: #000000 !important;
}

/* Dark mode text colors - white for both dropdowns with highest specificity */
[data-theme="dark"] .user-dropdown .dropdown-item a,
[data-theme="dark"] .dropdown-menu .dropdown-item a,
[data-theme="dark"] .dropdown-menu li a,
[data-theme="dark"] .user-dropdown li a,
[data-theme="dark"] .nav-item .dropdown-menu .dropdown-item a,
[data-theme="dark"] .nav-item .dropdown-menu li a,
[data-theme="dark"] .dropdown-menu .dropdown-item > a,
[data-theme="dark"] .dropdown-menu li > a {
  color: #ffffff !important;
}

/* Specific rule for inventory dropdown in dark mode */
[data-theme="dark"] .navbar .nav-item:nth-child(2) .dropdown-menu a,
[data-theme="dark"] .navbar .nav-item:nth-child(2) .dropdown-menu li a {
  color: #ffffff !important;
}

/* Responsive consistency - desktop */
@media (min-width: 768px) {
  .dropdown-menu {
    min-width: 180px !important;
    max-width: 220px !important;
  }

  [data-theme="dark"] .dropdown-menu .dropdown-item a,
  [data-theme="dark"] .user-dropdown .dropdown-item a,
  [data-theme="dark"] .dropdown-menu li a,
  [data-theme="dark"] .user-dropdown li a,
  [data-theme="dark"] .nav-item .dropdown-menu .dropdown-item a,
  [data-theme="dark"] .nav-item .dropdown-menu li a,
  [data-theme="dark"] .dropdown-menu .dropdown-item > a,
  [data-theme="dark"] .dropdown-menu li > a {
    color: #ffffff !important;
  }

  :root .dropdown-menu .dropdown-item a,
  [data-theme="light"] .dropdown-menu .dropdown-item a {
    color: #000000 !important;
  }
}

/* Additional medium screen specific overrides */
@media (min-width: 768px) and (max-width: 1024px) {
  .dropdown-menu {
    min-width: 170px !important;
    max-width: 200px !important;
  }

  .desktop-list {
    gap: 1.5rem !important; /* Override the general desktop gap */
  }

  .nav-link {
    font-size: 17px !important; /* More specific font size for this range */
  }
}

/* RTL Support for Navbar */
[dir="rtl"] .navbar {
  direction: rtl;
}

[dir="rtl"] .navbar-container {
  direction: rtl;
}

[dir="rtl"] .navbar-left {
  order: 2;
}

[dir="rtl"] .navbar-right {
  order: 1;
}

[dir="rtl"] .desktop-list {
  direction: rtl;
}

[dir="rtl"] .mobile-list {
  direction: rtl;
  text-align: right;
}

[dir="rtl"] .dropdown-menu {
  right: 0;
  left: auto;
  text-align: right;
}

[dir="rtl"] .user-dropdown {
  right: 0;
  left: auto;
  text-align: right;
}

[dir="rtl"] .mobile-nav-toggle {
  left: 1rem;
  right: auto;
}

[dir="rtl"] .logo-container {
  text-align: right;
}

[dir="rtl"] .nav-link {
  text-align: right;
}

[dir="rtl"] .dropdown-item {
  text-align: right;
}

[dir="rtl"] .dropdown-arrow {
  margin-left: 0.5rem;
  margin-right: 0;
}
